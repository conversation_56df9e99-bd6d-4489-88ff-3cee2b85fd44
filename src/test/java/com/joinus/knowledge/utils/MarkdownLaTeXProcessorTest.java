package com.joinus.knowledge.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MarkdownLaTeXProcessor 测试类
 */
public class MarkdownLaTeXProcessorTest {

    @Test
    public void testProcessToHtml() {
        // 测试基本的 Markdown 转换
        String markdown = "# 标题\n\n这是**粗体**和*斜体*。";
        String html = MarkdownLaTeXProcessor.processToHtml(markdown);
        
        assertTrue(html.contains("<h1>标题</h1>"));
        assertTrue(html.contains("<strong>粗体</strong>"));
        assertTrue(html.contains("<em>斜体</em>"));
    }
    
    @Test
    public void testProcessToHtmlWithInlineLaTeX() {
        // 测试包含行内 LaTeX 公式的 Markdown
        String markdown = "行内公式: $E=mc^2$";
        
        // 使用 img 标签
        String htmlWithImg = MarkdownLaTeXProcessor.processToHtml(markdown, true);
        assertTrue(htmlWithImg.contains("<img src=\"data:image/svg+xml;base64,"));
        
        // 直接使用 SVG
        String htmlWithSvg = MarkdownLaTeXProcessor.processToHtml(markdown, false);
        assertTrue(htmlWithSvg.contains("<svg"));
    }
    
    @Test
    public void testProcessToHtmlWithBlockLaTeX() {
        // 测试包含块级 LaTeX 公式的 Markdown
        String markdown = "块级公式:\n\n$$\\sum_{i=1}^n i = \\frac{n(n+1)}{2}$$";
        
        // 使用 img 标签
        String htmlWithImg = MarkdownLaTeXProcessor.processToHtml(markdown, true);
        assertTrue(htmlWithImg.contains("<img src=\"data:image/svg+xml;base64,"));
        
        // 直接使用 SVG
        String htmlWithSvg = MarkdownLaTeXProcessor.processToHtml(markdown, false);
        assertTrue(htmlWithSvg.contains("<svg"));
    }
    
    @Test
    public void testProcessToHtmlWithComplexMarkdown() {
        // 测试复杂的 Markdown 内容
        String markdown = """
                # 数学公式示例
                
                ## 行内公式
                
                这是一个行内公式 $E=mc^2$，它表示能量与质量的关系。
                
                另一个行内公式：$\\sqrt{x^2 + y^2}$ 表示点到原点的距离。
                
                ## 块级公式
                
                下面是一个块级公式：
                
                $$\\sum_{i=1}^n i = \\frac{n(n+1)}{2}$$
                
                这个公式表示前 n 个自然数的和。
                
                ## 列表中的公式
                
                * 项目 1：$a^2 + b^2 = c^2$
                * 项目 2：$\\int_a^b f(x) dx$
                """;
        
        String html = MarkdownLaTeXProcessor.processToHtml(markdown);
        
        // 检查 Markdown 基本元素
        assertTrue(html.contains("<h1>"));
        assertTrue(html.contains("<h2>"));
        assertTrue(html.contains("<ul>"));
        assertTrue(html.contains("<li>"));
        
        // 检查公式转换
        assertTrue(html.contains("<img src=\"data:image/svg+xml;base64,"));
    }
    
    @Test
    public void testProcessToHtmlWithNewlineAndSpecialChars() {
        // 测试包含换行符和特殊字符的LaTeX公式
        String markdown = """
                转换带分数和小数为分数形式:\\n\\n$$
                \\begin{align}
                (-2 \\frac{2}{5}) \\div 1 \\frac{1}{3} \\times (-0.6) &= \\left(-\\frac{12}{5}\\right) \\div \\frac{4}{3} \\times \\left(-\\frac{3}{5}\\right) \\\\n&= \\left(-\\frac{12}{5}\\right) \\times \\frac{3}{4} \\times \\left(-\\frac{3}{5}\\right) \\quad (\\text{除法变乘法，倒数处理})
                \\end{align}
                $$\\n符号处理：两个负号相乘结果为正\\n$$
                \\frac{12}{5} \\times \\frac{3}{4} \\times \\frac{3}{5} = \\frac{12 \\times 3 \\times 3}{5 \\times 4 \\times 5} = \\frac{108}{100} = 1.08
                $$
                """;
        
        // 使用img标签处理
        String html = MarkdownLaTeXProcessor.processToHtml(markdown, true);
        
        // 打印输出以便调试
        System.out.println("Generated HTML: " + html);
        
        // 检查HTML是否包含img标签和base64编码的SVG
        assertTrue(html.contains("<img src=\"data:image/svg+xml;base64,"), "HTML应该包含base64编码的SVG图像");
        
        // 检查是否不包含空的base64编码（这是之前的问题）
        assertFalse(html.contains("src=\"data:image/svg+xml;base64,\""), "HTML不应该包含空的base64编码");
        
        // 检查换行符是否被正确处理为段落或换行标签
        // 注意：由于我们的修改，可能输出为<p>...</p>或包含<br>标签
        assertTrue(
            html.contains("<p>符号处理：两个负号相乘结果为正</p>") || 
            html.contains(">符号处理：两个负号相乘结果为正<") ||
            html.contains(">符号处理：两个负号相乘结果为正<br>"),
            "换行符应该被正确处理为HTML格式"
        );
        
        // 检查LaTeX公式前后的文本是否被正确处理
        assertTrue(
            html.contains("<p>转换带分数和小数为分数形式:</p>") || 
            html.contains(">转换带分数和小数为分数形式:<") || 
            html.contains(">转换带分数和小数为分数形式:<br>"),
            "LaTeX公式前的文本应该被正确处理"
        );
        
        // 确保不包含原始的\\n字符
        assertFalse(html.contains("\\n\\n"), "HTML不应该包含原始的换行符转义字符");
    }
}
