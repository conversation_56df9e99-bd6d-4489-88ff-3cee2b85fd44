package com.joinus.knowledge.service.impl;

import com.volcengine.ark.runtime.model.completion.chat.*;
import com.volcengine.ark.runtime.service.ArkService;
import io.reactivex.Flowable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class AITest {
    @Resource
    private ArkService arkService;

    @Test
    public void chat() {
        //String model = "ep-20250217000346-2wfvt"; //r1
        String model = "ep-20250313174003-6tg66"; //vision
        //String model = "doubao-1-5-lite-32k-250115"; //lite
        String promptText = "提取图片中的文字内容";
        String imageUrl = "https://s3-minio.ijx.icu/education-knowledge-hub/3958c3d1-75e2-40f7-9f67-e5ea5756c228/7/original.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=iUKyaRKNmM3fghVXwwNH%2F20250314%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250314T081855Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=30a2922f6ccf90de2fa14f0f3ac4febed9a10007bccef909689bdd28374ee1e9";

        List<ChatMessage> messages = new ArrayList<>();

        List<ChatCompletionContentPart> multiContent = new ArrayList<>();
        ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(promptText).build();
        ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(imageUrl)).build();

        multiContent.add(textPart);
        multiContent.add(imageUrlPart);

        final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
        messages.add(userMessage);
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .build();

        /*Disposable disposable = arkService.streamChatCompletion(chatCompletionRequest)
                .doOnError(Throwable::printStackTrace)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.single())
                .subscribe(
                        choice -> {
                            if (choice.getChoices().size() > 0) {
                                System.out.print(choice.getChoices().get(0).getMessage().getContent());
                            }
                        }
                );*/
        String response = arkService.createChatCompletion(chatCompletionRequest).getChoices().getFirst().getMessage().getContent().toString();
        System.out.println(response);
    }
}
