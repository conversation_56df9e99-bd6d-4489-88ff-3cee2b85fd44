package com.joinus.knowledge.service.impl;

import com.joinus.knowledge.service.TemporalWorkflowService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class TemporalWorkflowServiceImplTest {

    @Resource
    TemporalWorkflowService temporalWorkflowService;

    @Test
    void extractInfoFromImageByLLM() {
        String bookId = "1f7003e7-8a45-4d76-b50e-8c46a68fa026";
        temporalWorkflowService.extractInfoFromImageByLLM(bookId);

    }

    @Test
    void imageOcr() {
        String bookId = "1f7003e7-8a45-4d76-b50e-8c46a68fa026";
        temporalWorkflowService.imageOcr(bookId);
    }
}