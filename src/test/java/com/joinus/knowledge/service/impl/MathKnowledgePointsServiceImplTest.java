package com.joinus.knowledge.service.impl;

import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.service.MathKnowledgePointsService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class MathKnowledgePointsServiceImplTest {

    @Resource
    MathKnowledgePointsService mathKnowledgePointsService;

    @Test
    void listByGradeAndSemester() {
        List<MathKnowledgePoint> list =  mathKnowledgePointsService.listByGradeAndSemester(8,1, null);
        System.out.println(list.size());
    }
}