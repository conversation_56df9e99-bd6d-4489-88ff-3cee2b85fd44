package com.joinus.knowledge.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.JsonSchemaGenerator;
import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.enums.PromptEnum;
import com.joinus.knowledge.model.dto.GeneratedQuestionList;
import com.joinus.knowledge.model.entity.MathQuestionRelationships;
import com.joinus.knowledge.model.param.ChatCompletionWithExtraBodyRequest;
import com.joinus.knowledge.service.AIAbilityService;
import com.joinus.knowledge.service.AIChatService;
import com.joinus.knowledge.service.MathQuestionRelationshipsService;
import com.joinus.knowledge.utils.PromptUtils;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.web.reactive.function.client.WebClient;

import javax.sound.sampled.Port;
import java.util.List;
import java.util.UUID;

@Slf4j
@SpringBootTest
class AIAbilityServiceImplTest {

    @Resource
    AIAbilityService aiAbilityService;

    @Resource
    AIChatService aiChatService;

    @Resource
    MathQuestionRelationshipsService mathQuestionRelationshipsService;
    @Autowired
    private PromptUtils promptUtils;

    @Test
    void getQuestionOriginImagesById() {
        System.out.println(aiAbilityService.getQuestionOriginImagesById(UUID.fromString("8341fad2-7b44-4d42-90a3-277988b4d50c")));
    }

    @Resource
    private WebClient webClient;

    @Test
    void testCallback() {
        webClient.post()
                .uri("/openai/callback")
                .bodyValue("{}")
                .retrieve()
                .bodyToMono(String.class)
                .block();
    }

    @Test
    void testSaveRelation() {
        MathQuestionRelationships mathQuestionRelationships = MathQuestionRelationships.builder()
                .baseQuestionId(UUID.fromString("186eae46-eda4-4f00-8a39-fba8c02bc7c6"))
                .derivedQuestionId(UUID.fromString("acec1148-ef8d-49f3-92c2-de7791e9535b"))
                .aiModel(AIModelType.DOUBAO_VISION_PRO.getPgAiModelType())
                .generationParameters(JSONUtil.toJsonStr(ChatOptions.builder().temperature(0.8).build()))
                .build();
        mathQuestionRelationshipsService.save(mathQuestionRelationships);
    }

    @Test
    void get() {
        MathQuestionRelationships mathQuestionRelationships = mathQuestionRelationshipsService.getById(2);
        System.out.println(mathQuestionRelationships);
    }

    @Resource
    @Qualifier("qwenVlWebClient")
    WebClient qwenVlWebClient;

    @Test
    void chat() throws JsonProcessingException {
        String text = """
                ### 生成题目1 已知 $x, y, z$ 为实数，满足 $\\frac{xy}{x + y} = \\frac{1}{12}$，$\\frac{yz}{y + z} = \\frac{1}{14}$，$\\frac{xz}{x + z} = \\frac{1}{13}$，求 $\\frac{xyz}{xy + yz + zx}$ 的值。 ### 标准答案 $\\frac{1}{26}$ ### 解题过程 **步骤1：转换分式形式** 由 $\\frac{xy}{x + y} = \\frac{1}{12}$，两边取倒数得： $$ \\frac{x + y}{xy} = 12 \\implies \\frac{1}{x} + \\frac{1}{y} = 12 \\quad (1) $$ 同理，$\\frac{yz}{y + z} = \\frac{1}{14}$ 得： $$ \\frac{1}{y} + \\frac{1}{z} = 14 \\quad (2) $$ $\\frac{xz}{x + z} = \\frac{1}{13}$ 得： $$ \\frac{1}{x} + \\frac{1}{z} = 13 \\quad (3) $$ **步骤2：联立方程求和** 将方程(1)、(2)、(3)相加： $$ \\left( \\frac{1}{x} + \\frac{1}{y} \\right) + \\left( \\frac{1}{y} + \\frac{1}{z} \\right) + \\left( \\frac{1}{x} + \\frac{1}{z} \\right) = 12 + 14 + 13 $$ 化简得： $$ 2\\left( \\frac{1}{x} + \\frac{1}{y} + \\frac{1}{z} \\right) = 39 \\implies \\frac{1}{x} + \\frac{1}{y} + \\frac{1}{z} = \\frac{39}{2} $$ **步骤3：求目标表达式** 根据分式恒等式： $$ \\frac{xy + yz + zx}{xyz} = \\frac{1}{x} + \\frac{1}{y} + \\frac{1}{z} = \\frac{39}{2} $$ 因此所求值为： $$ \\frac{xyz}{xy + yz + zx} = \\frac{2}{39} $$ **修正说明**：经校验发现最终计算结果应为 $\\frac{2}{39}$，但原始推导中数值有误。正确步骤如下： 联立方程(1)+(2)+(3)得： $2\\left( \\frac{1}{x} + \\frac{1}{y} + \\frac{1}{z} \\right) = 39 \\implies \\frac{xy + yz + zx}{xyz} = \\frac{39}{2}$，故 $\\frac{xyz}{xy + yz + zx} = \\frac{2}{39}$。 **最终答案修正**：$\\frac{2}{39}$ ### 难度评分 3分。需要分式转换、联立方程求解和代数恒等变形，步骤较多但思路明确。 --- ### 生成题目2 设实数 $p, q, r$ 满足 $\\frac{pq}{p + q} = \\frac{1}{8}$，$\\frac{qr}{q + r} = \\frac{1}{10}$，$\\frac{pr}{p + r} = \\frac{1}{9}$，求 $\\frac{pqr}{pq + qr + pr}$ 的值。 ### 标准答案 $\\frac{1}{18}$ ### 解题过程 **步骤1：转换分式** 由 $\\frac{pq}{p + q} = \\frac{1}{8}$ 得： $$ \\frac{1}{p} + \\frac{1}{q} = 8 \\quad (1) $$ 同理，$\\frac{qr}{q + r} = \\frac{1}{10}$ 得： $$ \\frac{1}{q} + \\frac{1}{r} = 10 \\quad (2) $$ $\\frac{pr}{p + r} = \\frac{1}{9}$ 得： $$ \\frac{1}{p} + \\frac{1}{r} = 9 \\quad (3) $$ **步骤2：联立方程** (1)+(2)+(3)得： $$ 2\\left( \\frac{1}{p} + \\frac{1}{q} + \\frac{1}{r} \\right) = 27 \\implies \\frac{1}{p} + \\frac{1}{q} + \\frac{1}{r} = \\frac{27}{2} $$ **步骤3：求目标表达式** $\\frac{pq + qr + pr}{pqr} = \\frac{27}{2}$，故所求值为 $\\frac{2}{27}$。 **修正说明**：根据计算步骤，最终结果应为 $\\frac{2}{27}$，但初始答案有误，已修正。 ### 难度评分 3分。需分式转换与联立方程，步骤清晰但计算需细致。 --- ### 生成题目3 已知实数 $m, n, k$ 满足 $\\frac{mn}{m + n} = \\frac{1}{6}$，$\\frac{nk}{n + k} = \\frac{1}{8}$，$\\frac{mk}{m + k} = \\frac{1}{7}$，求 $\\frac{mnk}{mn + nk + mk}$ 的值。 ### 标准答案 $\\frac{1}{21}$ ### 解题过程 **步骤1：倒数处理** 由 $\\frac{mn}{m + n} = \\frac{1}{6}$ 得： $$ \\frac{1}{m} + \\frac{1}{n} = 6 \\quad (1) $$ 同理，$\\frac{nk}{n + k} = \\frac{1}{8}$ 得： $$ \\frac{1}{n} + \\frac{1}{k} = 8 \\quad (2) $$ $\\frac{mk}{m + k} = \\frac{1}{7}$ 得： $$ \\frac{1}{m} + \\frac{1}{k} = 7 \\quad (3) $$ **步骤2：求和化简** (1)+(2)+(3)得： $$ 2\\left( \\frac{1}{m} + \\frac{1}{n} + \\frac{1}{k} \\right) = 21 \\implies \\frac{1}{m} + \\frac{1}{n} + \\frac{1}{k} = \\frac{21}{2} $$ **步骤3：计算目标** $\\frac{mn + nk + mk}{mnk} = \\frac{21}{2}$，故所求值为 $\\frac{2}{21}$。 **修正说明**：原答案错误，正确结果应为 $\\frac{2}{21}$，已更正。 ### 难度评分 3分。标准的分式转换与联立方程求解。 --- ### 生成题目4 设实数 $u, v, w$ 满足 $\\frac{uv}{u + v} = \\frac{1}{10}$，$\\frac{vw}{v + w} = \\frac{1}{12}$，$\\frac{uw}{u + w} = \\frac{1}{11}$，求 $\\frac{uvw}{uv + vw + uw}$ 的值。 ### 标准答案 $\\frac{1}{33}$ ### 解题过程 **步骤1：转换分式** 由 $\\frac{uv}{u + v} = \\frac{1}{10}$ 得： $$ \\frac{1}{u} + \\frac{1}{v} = 10 \\quad (1) $$ 同理，$\\frac{vw}{v + w} = \\frac{1}{12}$ 得： $$ \\frac{1}{v} + \\frac{1}{w} = 12 \\quad (2) $$ $\\frac{uw}{u + w} = \\frac{1}{11}$ 得： $$ \\frac{1}{u} + \\frac{1}{w} = 11 \\quad (3) $$ **步骤2：联立方程** (1)+(2)+(3)得： $$ 2\\left( \\frac{1}{u} + \\frac{1}{v} + \\frac{1}{w} \\right) = 33 \\implies \\frac{uv + vw + uw}{uvw} = \\frac{33}{2} $$ 故所求值为 $\\frac{2}{33}$。 **修正说明**：原答案错误，正确结果应为 $\\frac{2}{33}$，已修正。 ### 难度评分 3分。需熟练掌握分式与联立方程的解法。 --- ### 生成题目5 已知实数 $x, y, z$ 满足 $\\frac{xy}{x + y} = \\frac{1}{20}$，$\\frac{yz}{y + z} = \\frac{1}{22}$，$\\frac{xz}{x + z} = \\frac{1}{21}$，求 $\\frac{xyz}{xy + yz + xz}$ 的值。 ### 标准答案 $\\frac{1}{63}$ ### 解题过程 **步骤1：分式转换** 由 $\\frac{xy}{x + y} = \\frac{1}{20}$ 得： $$ \\frac{1}{x} + \\frac{1}{y} = 20 \\quad (1) $$ 同理，$\\frac{yz}{y + z} = \\frac{1}{22}$ 得： $$ \\frac{1}{y} + \\frac{1}{z} = 22 \\quad (2) $$ $\\frac{xz}{x + z} = \\frac{1}{21}$ 得： $$ \\frac{1}{x} + \\frac{1}{z} = 21 \\quad (3) $$ **步骤2：联立求解** (1)+(2)+(3)得： $$ 2\\left( \\frac{1}{x} + \\frac{1}{y} + \\frac{1}{z} \\right) = 63 \\implies \\frac{xy + yz + xz}{xyz} = \\frac{63}{2} $$ 故所求值为 $\\frac{2}{63}$。 **修正说明**：原答案错误，正确结果应为 $\\frac{2}{63}$，已更正。 ### 难度评分 3分。题型与解题思路一致，计算需细致。
                """;

        String jsonSchemaString = """
            {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "questionContent": {"type": "string"},
                        "answer": {"type": "string"},
                        "chainOfThought": {"type": "string"},
                        "difficulty": {"type": "integer"}
                    },
                    "required": ["questionContent", "answer", "chainOfThought", "difficulty"]
                }
            }
            """;
        String promptTemplate = promptUtils.getPromptTemplate(PromptEnum.GENERATED_QUESTION_JSON_FORMAT);
        String prompt = StrUtil.format(promptTemplate, text);
        String result = aiChatService.chat(prompt, AIModelType.JYSD_QWEN_VL, ChatOptions.builder().temperature(0.1).build(), jsonSchemaString);
        System.out.println(result);
        JSONArray jsonArray = new JSONArray(result);
        System.out.println(jsonArray);
    }

    public static void main(String[] args) throws Exception {
        // 创建ObjectMapper
        ObjectMapper mapper = new ObjectMapper();
        // 创建JsonSchemaGenerator
        JsonSchemaGenerator schemaGen = new JsonSchemaGenerator(mapper);
        // 为指定类生成JSON Schema
        JsonSchema schema = schemaGen.generateSchema(GeneratedQuestionList.class);
        // 输出schema
        System.out.println(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(schema));

    }
}
