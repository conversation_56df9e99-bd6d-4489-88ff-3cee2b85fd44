package com.joinus.knowledge.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.enums.ContentPartType;
import com.joinus.knowledge.model.po.ContentPart;
import com.joinus.knowledge.model.response.StreamResponse;
import com.joinus.knowledge.utils.ConverterUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class AIChatServiceImplTest {


    @Autowired
    private AIChatServiceImpl aiChatService;

    @Test
    void chatByHttpStream() {
        Flux<String> stream = aiChatService.chatByHttpStream("请识别图中文字", List.of("https://www.helloimg.com/i/2025/05/08/681c681e6943d.png"), AIModelType.GEMINI_2_5_FLASH,null);
        String result = formatAndSave(stream);
        System.out.println(result);
    }

    private String formatAndSave(Flux<String> flux) {
        final StringBuilder fullContent = new StringBuilder();
        flux.doOnNext(chunk -> {
                    // 收集每个内容块
                    StreamResponse response = JSONUtil.toBean(chunk, StreamResponse.class);
                    if (StrUtil.isNotEmpty(response.getContent())) {
                        fullContent.append(response.getContent());
                    }
                })
                .blockLast(Duration.ofMinutes(10));
        return fullContent.toString();
    }

    @Test
    void chatListContent() {
        List<ContentPart> contentParts = new ArrayList<>();
        ContentPart part1 = ContentPart.builder().contentPartType(ContentPartType.TEXT).text("下边几个选项的数字哪个大,请输出对应选项。\nA.").build();
        ContentPart part2 = ContentPart.builder().contentPartType(ContentPartType.IMAGE_URL).url("https://www.helloimg.com/i/2025/05/08/681c6e13a7f2a.png").build();
        ContentPart part3 = ContentPart.builder().contentPartType(ContentPartType.TEXT).text("\nB.").build();
        ContentPart part4 = ContentPart.builder().contentPartType(ContentPartType.IMAGE_URL).url("https://www.helloimg.com/i/2025/05/08/681c6deeb90fc.png").build();
        ContentPart part5 = ContentPart.builder().contentPartType(ContentPartType.TEXT).text("\nC.").build();
        ContentPart part6 = ContentPart.builder().contentPartType(ContentPartType.IMAGE_URL).url("https://www.helloimg.com/i/2025/05/08/681c6dec25f34.png").build();
        ContentPart part7 = ContentPart.builder().contentPartType(ContentPartType.TEXT).text("\nD.").build();
        ContentPart part8 = ContentPart.builder().contentPartType(ContentPartType.IMAGE_URL).url("https://www.helloimg.com/i/2025/05/08/681c6e08d383f.png").build();
        contentParts.add(part1);
        contentParts.add(part2);
        contentParts.add(part3);
        contentParts.add(part4);
        contentParts.add(part5);
        contentParts.add(part6);
        contentParts.add(part7);
        contentParts.add(part8);
        //String result = aiChatService.chatByHttp(contentParts, AIModelType.JYSD_QWEN_VL,null);
        //System.out.println(result);

        Flux<String> stream = aiChatService.chatByHttpStream(contentParts, AIModelType.GEMINI_2_5_FLASH,null);
        String result = formatAndSave(stream);
        System.out.println(result);
    }

    @Test
    void splitContent() {
        String questionContent = """
                下边几个选项的数字哪个大,请输出对应选项。
                A.<img src="https://www.helloimg.com/i/2025/05/08/681c6e13a7f2a.png" />
                B.<img src="https://www.helloimg.com/i/2025/05/08/681c6deeb90fc.png" />
                C.<img src="https://www.helloimg.com/i/2025/05/08/681c6dec25f34.png" />
                D.<img src="https://www.helloimg.com/i/2025/05/08/681c6e08d383f.png" />
                """;
        String a = ConverterUtils.clearImgSrc(questionContent);
        System.out.println(a);

    }
}