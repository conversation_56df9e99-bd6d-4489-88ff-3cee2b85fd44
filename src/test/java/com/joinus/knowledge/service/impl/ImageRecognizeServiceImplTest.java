package com.joinus.knowledge.service.impl;

import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.param.ErasePenMarksParam;
import com.joinus.knowledge.model.vo.OssFileVO;
import com.joinus.knowledge.service.ImageRecognizeService;
import com.joinus.knowledge.utils.MinioUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
class ImageRecognizeServiceImplTest {

    @Resource
    private ImageRecognizeService imageRecognizeService;
    @Resource
    private MinioUtils minioUtils;

    @Test
    void removeHandwriting() {
        String key = "tmp/ocr/rmhw3";
        OssEnum ossEnum = OssEnum.MINIO_EDU_KNOWLEDGE_HUB;
        String imageUrl = minioUtils.getPresignedDownloadUrl(ossEnum.getBucket(), key, key.substring(key.lastIndexOf("/")+1));
        System.out.println(imageUrl);
        ErasePenMarksParam build = ErasePenMarksParam.builder()
                .ossEnum(ossEnum)
                .ossKey(key)
                .build();
        OssFileVO ossFileVO = imageRecognizeService.removeHandwriting(build);
        System.out.println(JSONUtil.toJsonStr(ossFileVO));
    }
}