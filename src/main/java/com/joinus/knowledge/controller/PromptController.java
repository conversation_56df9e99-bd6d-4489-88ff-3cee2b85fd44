package com.joinus.knowledge.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.PromptEnum;
import com.joinus.knowledge.model.entity.PromptTemplate;
import com.joinus.knowledge.service.PromptTemplateService;
import com.joinus.knowledge.utils.PromptUtils;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/prompt-template")
public class PromptController {

    @Resource
    private PromptTemplateService promptTemplateService;

    @Resource
    private PromptUtils promptUtils;

    @GetMapping("/")
    public Result<List<PromptTemplate>> getAll() {
        return Result.success(promptTemplateService.list());
    }

    @GetMapping("/{id}")
    public Result<PromptTemplate> get(@PathVariable("id") Integer id) {
        return Result.success(promptTemplateService.getById(id));
    }

    @PostMapping("/")
    public Result<PromptTemplate> add(@RequestBody PromptTemplate promptTemplate) {
        promptTemplateService.save(promptTemplate);
        return Result.success(promptTemplate);
    }

    @PutMapping("/{id}")
    public Result<PromptTemplate> update(@PathVariable("id") Integer id, @RequestBody PromptTemplate promptTemplate) {
        PromptTemplate originPromtTemplate = promptTemplateService.getById(id);
        CopyOptions copyOptions = new CopyOptions();
        copyOptions.setIgnoreNullValue(true);
        BeanUtil.copyProperties(promptTemplate, originPromtTemplate, copyOptions);
        promptTemplateService.updateById(originPromtTemplate);
        return Result.success(originPromtTemplate);
    }


    @PostMapping("/cache/refresh")
    public Result<String> refreshPrompt() {
        promptUtils.init();
        return Result.success("刷新成功");
    }

    @GetMapping("/cache/{name}")
    public Result<String> prompt(@PathVariable("name") PromptEnum promptEnum) {
        String prompt = promptUtils.getPromptTemplate(promptEnum);
        if (prompt == null) {
            return Result.error("Prompt not found");
        }
        return Result.success(prompt);
    }

}
