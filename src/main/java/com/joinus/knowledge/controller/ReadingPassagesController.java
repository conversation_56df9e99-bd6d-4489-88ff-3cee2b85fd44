/**
 * <AUTHOR>
 * @Date 2025/3/28 19:47
 */
package com.joinus.knowledge.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.param.ReadingPassagesParam;
import com.joinus.knowledge.service.ReadingPassageQuestionsService;
import com.joinus.knowledge.service.ReadingPassagesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/reading-passages")
@RequiredArgsConstructor
@Tag(name = "语文题库 API", description = "提供语文题库相关的 API")
public class ReadingPassagesController {
    @Resource
    ReadingPassagesService readingPassagesService;
    @Resource
    ReadingPassageQuestionsService readingPassageQuestionsService;

    @PostMapping("/batchCreate")
    public Result<String> batchCreate() {
        readingPassagesService.batchCreateReadingPassages();
        return Result.success("批量创建成功");
    }

    @PostMapping("testListMsg")
    public Result<String> testListMsg() {
        readingPassagesService.testListMsg();
        return Result.success();
    }

    @PostMapping("createPassageQuestion")
    public Result<String> createPassageQuestion(@RequestParam("passageId") UUID passageId) {
        readingPassagesService.createPassageQuestion(passageId);
        return Result.success();
    }
    @PostMapping("createPassageAndQuestion")
    public Result<String> createPassageAndQuestion(@RequestParam("grade") Integer grade,
                                                   @RequestParam("semester") Integer semester,
                                                   @RequestParam("unit") String unit,
                                                   @RequestParam(value = "num",required = false) Integer num) {
        readingPassagesService.batchCreatePassageAndQuestion(grade, semester, unit, num);
        return Result.success();
    }
    @PostMapping("dealClonePassageAndQuestions")
    public Result<String> dealClonePassageAndQuestions() {
        readingPassagesService.dealClonePassageAndQuestions();
        return Result.success();
    }
    @PostMapping("dealUnsetQuestions")
    public Result<String> dealUnsetQuestions() {
        readingPassageQuestionsService.dealUnsetQuestions();
        return Result.success();
    }

    @PostMapping("dealChoiceQuestions")
    public Result<String> dealChoiceQuestions() {
        readingPassageQuestionsService.dealChoiceQuestions();
        return Result.success();
    }
    @PostMapping("dealUploadData")
    public Result<String> dealUploadData(@RequestBody JSONObject json) {
        readingPassagesService.dealUploadData(json);
        return Result.success();
    }
    @PostMapping("dealFillInTheBlankQuestionAnswers")
    public Result<String> dealFillInTheBlankQuestionAnswers() {
        readingPassageQuestionsService.dealFillInTheBlankQuestionAnswers();
        return Result.success();
    }

    @PostMapping("createQuestion")
    public Result<String> createQuestion(@RequestParam("grade") Integer grade,
                                         @RequestParam("semester") Integer semester,
                                         @RequestParam("unit") String unit,
                                         @RequestParam(value = "genreName",required = false) String genreName,
                                         @RequestParam(value = "passageId",required = false) UUID passageId,
                                         @RequestParam(value = "num",required = false) Integer num) {
        readingPassagesService.batchCreateQuestion(grade, semester, unit, genreName, passageId, num);
        return Result.success();
    }


    @DeleteMapping
    @Operation(summary = "删除", description = "删除文章")
    public Result<String> delete(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "文章ID不能为空");
        readingPassagesService.delete(idList);
        return Result.success();
    }

    @PutMapping
    @Operation(summary = "修改", description = "修改文章")
    public Result<String> update(@RequestBody @Valid ReadingPassagesParam param) {
        readingPassagesService.update(param);
        return Result.success();
    }

    @PutMapping("/enable")
    @Operation(summary = "启用", description = "启用文章")
    public Result<String> enable(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "文章ID不能为空");
        readingPassagesService.enable(idList);
        return Result.success();
    }

    @PutMapping("/disable")
    @Operation(summary = "挂起", description = "挂起文章")
    public Result<String> disable(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "文章ID不能为空");
        readingPassagesService.disable(idList);
        return Result.success();
    }

    @PutMapping("/audit/pass")
    @Operation(summary = "审核通过", description = "审核通过")
    public Result<String> auditPass(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "文章ID不能为空");
        readingPassagesService.auditPass(idList);
        return Result.success();
    }

    @PutMapping("/audit/no-pass")
    @Operation(summary = "审核不通过", description = "审核不通过")
    public Result<String> auditNoPass(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "文章ID不能为空");
        readingPassagesService.auditNoPass(idList);
        return Result.success();
    }
}
