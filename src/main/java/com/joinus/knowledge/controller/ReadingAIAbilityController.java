package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.param.ReadingAIAbilityParam;
import com.joinus.knowledge.service.ReadingAIAbilityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Tag(name = "语文阅读AI能力接口", description = "提供语文阅读AI相关的能力，AI批改、AI薄弱点分析、综合训练建议等功能")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/ai/reading/ability")
public class ReadingAIAbilityController {

    private final ReadingAIAbilityService readingAIAbilityService;

    @PostMapping("/common")
    @Operation(summary = "为语文阅读提供AI能力")
    public Result<String> dealByAIAbility(@RequestBody ReadingAIAbilityParam param) {
        readingAIAbilityService.dealByAIAbility(param);
        return Result.success();
    }

    @PostMapping("/ai-correct")
    @Operation(summary = "AI批改")
    public Result<ReadingAIAbilityParam> aiCorrect(@RequestBody ReadingAIAbilityParam param) {
        return Result.success(readingAIAbilityService.aiCorrect(param));
    }

    @PostMapping("/weak-knowledge-point/analysis")
    @Operation(summary = "薄弱知识点分析")
    public Result<String> weakKnowledgePointAnalysis(@RequestBody ReadingAIAbilityParam param) {
        readingAIAbilityService.weakKnowledgePointAnalysis(param);
        return Result.success();
    }

    @PostMapping("/training-suggestion")
    @Operation(summary = "综合训练建议")
    public Result<String> trainingSuggestion(@RequestBody ReadingAIAbilityParam param) {
        readingAIAbilityService.trainingSuggestion(param);
        return Result.success();
    }

    @PostMapping("/weak-question-type/analysis")
    @Operation(summary = "薄弱题型分析")
    public Result<String> weakQuestionTypeAnalysis(@RequestBody ReadingAIAbilityParam param) {
        readingAIAbilityService.weakQuestionTypeAnalysis(param);
        return Result.success();
    }

}
