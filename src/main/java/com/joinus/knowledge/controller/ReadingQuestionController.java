package com.joinus.knowledge.controller;

import cn.hutool.core.lang.Assert;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.param.ReadingPassageParam;
import com.joinus.knowledge.model.param.ReadingPassageQuestionParam;
import com.joinus.knowledge.model.param.ReadingQuestionSetItemEditParam;
import com.joinus.knowledge.model.vo.ReadingPassageVO;
import com.joinus.knowledge.service.ReadingPassageQuestionsService;
import com.joinus.knowledge.service.ReadingPassagesService;
import groovy.util.logging.Slf4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * 语文阅读题相关 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/reading")
@RequiredArgsConstructor
@Tag(name = "语文阅读题 API", description = "提供语文阅读题相关的 API")
@Slf4j
public class ReadingQuestionController {

    private final ReadingPassagesService readingPassagesService;
    private final ReadingPassageQuestionsService readingPassageQuestionsService;

    @PostMapping("/unit/question")
    @Operation(summary = "获取阅读题试卷")
    @Parameters({
            @Parameter(name = "param", description = "参数", schema = @Schema(implementation = ReadingPassageParam.class)),
    })
    public Result<ReadingPassageVO> getPassageAndQuestions(@RequestBody ReadingPassageParam param) {
        ReadingPassageVO passageAndQuestions = readingPassagesService.getPassageAndQuestions(param);
        return Result.success(passageAndQuestions);
    }

    @DeleteMapping
    @Operation(summary = "删除", description = "删除题目")
    public Result<String> delete(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "题目ID不能为空");
        readingPassageQuestionsService.delete(idList);
        return Result.success();
    }

    @PutMapping
    @Operation(summary = "修改", description = "修改题目")
    public Result<String> update(@RequestBody @Valid ReadingPassageQuestionParam param) {
        readingPassageQuestionsService.update(param);
        return Result.success();
    }

    @PutMapping("/batch")
    @Operation(summary = "批量修改", description = "批量修改题目")
    public Result<String> batchUpdate(@RequestBody @Valid List<ReadingQuestionSetItemEditParam> param) {
        readingPassageQuestionsService.batchUpdate(param);
        return Result.success();
    }

    @PutMapping("/enable")
    @Operation(summary = "启用", description = "启用题目")
    public Result<String> enable(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "题目ID不能为空");
        readingPassageQuestionsService.enable(idList);
        return Result.success();
    }

    @PutMapping("/disable")
    @Operation(summary = "挂起", description = "挂起题目")
    public Result<String> disable(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "题目ID不能为空");
        readingPassageQuestionsService.disable(idList);
        return Result.success();
    }

    @PutMapping("/audit/pass")
    @Operation(summary = "审核通过", description = "审核通过")
    public Result<String> auditPass(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "题目ID不能为空");
        readingPassageQuestionsService.auditPass(idList);
        return Result.success();
    }

    @PutMapping("/audit/no-pass")
    @Operation(summary = "审核不通过", description = "审核不通过")
    public Result<String> auditNoPass(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "题目ID不能为空");
        readingPassageQuestionsService.auditNoPass(idList);
        return Result.success();
    }

    @GetMapping("/question/audit/ai/result")
    public Result<String> getAiAuditResult(@RequestParam("questionId") UUID questionId) {
        String aiAuditResult = readingPassageQuestionsService.getAiAuditResult(questionId);
        return Result.success(aiAuditResult);
    }
}
