package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.vo.KnowledgePointAndQuestionTypeVO;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.service.MathQuestionsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

/**
 * 数学题目管理 Controller
 */
@RestController
@RequestMapping("/math/questions")
@RequiredArgsConstructor
@Slf4j
public class MathQuestionsController {

    private final MathQuestionsService mathQuestionsService;

    @Operation(summary = "根据题目id获取题目知识点和题型列表")
    @Parameters({
            @Parameter(name = "id", description = "题目id", required = true, in = ParameterIn.PATH, example = "7983e859-b710-404f-a808-6f2f2518873d")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功解析题目坐标", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathKnowledgePointVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}/knowledge-points-and-question-types")
    public Result<KnowledgePointAndQuestionTypeVO> listKnowledgePointsAndQuestionTypes(@PathVariable("id") UUID id) {
        KnowledgePointAndQuestionTypeVO vo = mathQuestionsService.listKnowledgePointsAndQuestionTypes(id);
        return Result.success(vo);
    }

}
