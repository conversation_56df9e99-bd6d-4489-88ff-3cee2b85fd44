package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.param.GeneratedQuestionParam;
import com.joinus.knowledge.service.TemporalWorkflowService;
import com.joinus.knowledge.service.impl.GenerateQuestionServiceImpl;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/workflow")
public class WorkflowController {

    @Resource
    private TemporalWorkflowService temporalWorkflowService;

    @Resource
    private GenerateQuestionServiceImpl generateQuestionService;

    @PostMapping("/ocr/{bookId}")
    public Result<String> ocr(@PathVariable("bookId") String bookId) {
        CompletableFuture.runAsync(() -> temporalWorkflowService.imageOcr(bookId));
        return Result.success();
    }

    @PostMapping("/extract-key-points/{bookId}")
    public Result<String> extractKeyPoints(@PathVariable("bookId") String bookId) {
        CompletableFuture.runAsync(() -> temporalWorkflowService.extractInfoFromImageByLLM(bookId));
        return Result.success();
    }

    @PostMapping("/knowledge-domain-label/{count}")
    public Result<String> knowledgeDomainLabel(@PathVariable("count") Integer count) {
        CompletableFuture.runAsync(() -> temporalWorkflowService.knowledgeDomainLabel(count));
        return Result.success();
    }

    @PostMapping("/generate-question")
    public Result<String> generateQuestion(@RequestBody GeneratedQuestionParam param) {
        CompletableFuture.runAsync(() -> temporalWorkflowService.generateQuestionBatch(param.getLabelName(), param.getCount(), param.getPublisher()));
        return Result.success();
    }

    @PostMapping("/dify/generate-question")
    public Result<String> difyGenerateQuestion(@RequestParam("count") int count) {
        CompletableFuture.runAsync(() -> generateQuestionService.generateQuestion(count));
        return Result.success();
    }

}
