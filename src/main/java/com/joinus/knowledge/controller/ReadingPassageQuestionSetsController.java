package com.joinus.knowledge.controller;

import cn.hutool.core.lang.Assert;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.param.ReadingQuestionSetEditParam;
import com.joinus.knowledge.service.ReadingPassageQuestionSetsCompositeService;
import com.joinus.knowledge.service.ReadingPassageQuestionSetsService;
import groovy.util.logging.Slf4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * 语文阅读套题相关 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/reading-passages-question-sets")
@RequiredArgsConstructor
@Tag(name = "语文阅读套题 API", description = "提供语文阅读题相关的 API")
@Slf4j
public class ReadingPassageQuestionSetsController {

    private final ReadingPassageQuestionSetsService readingPassageQuestionSetsService;
    private final ReadingPassageQuestionSetsCompositeService readingPassageQuestionSetsCompositeService;

    @DeleteMapping
    @Operation(summary = "删除", description = "删除套题")
    public Result<String> delete(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "套题ID不能为空");
        readingPassageQuestionSetsService.delete(idList);
        return Result.success();
    }

    @PutMapping("/enable")
    @Operation(summary = "启用", description = "启用套题")
    public Result<String> enable(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "套题ID不能为空");
        readingPassageQuestionSetsService.enable(idList);
        return Result.success();
    }

    @PutMapping("/disable")
    @Operation(summary = "挂起", description = "挂起套题")
    public Result<String> disable(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "套题ID不能为空");
        readingPassageQuestionSetsService.disable(idList);
        return Result.success();
    }

    @PutMapping("/audit/pass")
    @Operation(summary = "审核通过", description = "审核通过")
    public Result<String> auditPass(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "套题ID不能为空");
        readingPassageQuestionSetsService.auditPass(idList);
        return Result.success();
    }

    @PutMapping("/audit/no-pass")
    @Operation(summary = "审核不通过", description = "审核不通过")
    public Result<String> auditNoPass(@RequestBody List<UUID> idList) {
        Assert.notEmpty(idList, "套题ID不能为空");
        readingPassageQuestionSetsService.auditNoPass(idList);
        return Result.success();
    }

    @PutMapping("/update")
    @Operation(summary = "修改", description = "修改套题")
    public Result<String> update(@RequestBody ReadingQuestionSetEditParam param) {
        Assert.notNull(param.getQuestionSetId(), "套题ID不能为空");
        readingPassageQuestionSetsCompositeService.updateSets(param);
        return Result.success();
    }
}
