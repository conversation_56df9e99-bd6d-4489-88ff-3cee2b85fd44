package com.joinus.knowledge.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.model.param.CutImageFromPositionsParam;
import com.joinus.knowledge.model.response.StreamResponse;
import com.joinus.knowledge.model.vo.CoordinatePoint;
import com.joinus.knowledge.model.vo.FileVO;
import com.joinus.knowledge.model.vo.MathExamVO;
import com.joinus.knowledge.model.vo.OssFileVO;
import com.joinus.knowledge.service.AIChatService;
import com.joinus.knowledge.service.ImageRecognizeService;
import com.joinus.knowledge.service.MathExamsService;
import com.joinus.knowledge.utils.ConverterUtils;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/common/file/parser")
public class CommonFileParserController {

    @Resource
    private MathExamsService mathExamsService;
    @Resource
    private AIChatService aiChatService;
    @Resource
    private ImageRecognizeService imageRecognizeService;

    @PostMapping("/upload")
    public Result<String> upload() {
        return Result.success("upload");
    }

    @PostMapping(value = "/analyze/{objectId}")
    public Result<String> analyze(@PathVariable("objectId") UUID objectId) {
        MathExamVO mathExamVO = mathExamsService.queryExamDetailById(objectId);
        List<FileVO> images = mathExamVO.getImages();
        String promptText = """
            QwenVL HTML with image caption, 注意带上bbox坐标
            """;
        List<Document> documents = new ArrayList<>(images.size());

        for (int i = 0; i < images.size(); i++) {
            documents.add(null);
        }

        ExecutorService executor = Executors.newFixedThreadPool(10);

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int i = 0; i < images.size(); i++) {
            final int index = i;
            final FileVO fileVO = images.get(i);

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    String result = aiChatService.chat(promptText, fileVO.getOssUrl(), AIModelType.JYSD_QWEN_VL);
                    String htmlContent = ConverterUtils.convertToJsonStr(result);
                    Document document = Jsoup.parse(htmlContent);
                    Elements imgs = document.select("img");
                    imgs.forEach(img -> {
                        String dataBbox = img.attr("data-bbox");
                        Double[] bbox = Arrays.stream(dataBbox.split(" ")).map(Double::valueOf).toArray(Double[]::new);
                        CutImageFromPositionsParam cutImageFromPositionsParam = new CutImageFromPositionsParam();
                        cutImageFromPositionsParam.setOssKey(fileVO.getOssKey());
                        cutImageFromPositionsParam.setOssEnum(fileVO.getOssEnum());
                        List<CoordinatePoint> pointList = new ArrayList<>();
                        pointList.add(new CoordinatePoint(bbox[0], bbox[1], 1));
                        pointList.add(new CoordinatePoint(bbox[2], bbox[1], 2));
                        pointList.add(new CoordinatePoint(bbox[2], bbox[3], 3));
                        pointList.add(new CoordinatePoint(bbox[0], bbox[3], 4)); // 修正了坐标点
                        cutImageFromPositionsParam.setPositions(pointList);
                        OssFileVO ossFileVO = imageRecognizeService.cutImageFromPositions(cutImageFromPositionsParam);
                        log.info(ossFileVO.getPresignedUrl());
                        img.attr("data-s3-key", ossFileVO.getKey());
                        img.attr("oss-enum", ossFileVO.getOssEnum().toString());
                        img.attr("src", ossFileVO.getPresignedUrl());
                    });
                    // 按原始索引位置放入结果
                    documents.set(index, document);
                } catch (Exception e) {
                    log.error("处理图片时发生错误，索引: " + index, e);
                    // 可以选择在这里设置一个错误标记或空文档
                    documents.set(index, null);
                }
            }, executor);

            futures.add(future);
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        executor.shutdown();

        documents.removeIf(Objects::isNull);

        Document aggregateDocument = Jsoup.parse("<html><head><meta charset=\"UTF-8\"></head><body></body></html>");
        documents.forEach(document -> {
            Element body = document.body();
            aggregateDocument.body().appendChildren(body.childNodes());
        });

        String content = aggregateDocument.html();

        String promptTemplate = """
                {}
                请判断以上页面中一共包含多少道题。
                将结果按照以下格式输出：
                共有{n}道题
                """;
        String countResult = aiChatService.chat(StrUtil.format(promptTemplate, content), AIModelType.GEMINI_2_5_FLASH);
        int count = ConverterUtils.extractNumber(countResult).intValue();
        int size = 5;
        int totalPage = Math.ceilDiv(count, size);
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < totalPage; i++) {
            int from = i * size + 1;
            int to = Math.min((i + 1) * size, count);
            String promptTemplate1 = """
                {}
                帮我将以上页面的第{}至{}题解析为json数组，每道题都是一个json对象，包含对应的题目、答案，解析，题型(选择题、填空题、解答题)等属性。
                ** 要求 **
                - 请尽可能完整的保留题目、答案、以及解析中的内容
                - 如果题目中包含img标签，请保留img标签内容。不需要保留img标签外的div标签
                - 题目和答案要以markdown形式输出，如果包含数学公式，请以LaTex表示
                - 注意转成json格式中特殊符号如"\\"要进行转义
                - json请按照以下格式输出
                [
                  {
                    "question": "某校计划给每个年级配发 $n$ 套劳动工具, 则 3 个年级共需配发套劳动工具.",
                    "answer": "$3n$",
                    "analysis": "根据总共配发的数量 = 年级数量 $\\\\times$ 每个年级配发的套数, 列代数式.",
                    "type": "填空题"
                  }
                ]
                """;
            ChatOptions chatOptions = ChatOptions.builder().temperature(0D).build();
            Flux<String> stream = aiChatService.chatStream(StrUtil.format(promptTemplate1, content, from, to), AIModelType.GEMINI_2_5_FLASH, chatOptions);
            String result = formatAndSave(stream);
            String jsonResult = ConverterUtils.convertToJsonStr(result);
            jsonResult = ConverterUtils.fixInvalidBackslashesInJson(jsonResult);
            JSONArray questions = JSONUtil.parseArray(jsonResult);
            jsonArray.addAll(questions);
        }

        return Result.success();
    }

    private String formatAndSave(Flux<String> flux) {
        final StringBuilder fullContent = new StringBuilder();
        flux.doOnNext(chunk -> {
                    // 收集每个内容块
                    StreamResponse response = JSONUtil.toBean(chunk, StreamResponse.class);
                    if (StrUtil.isNotEmpty(response.getContent())) {
                        fullContent.append(response.getContent());
                    }
                })
                .doOnComplete(() -> {
                    // 流完成时，异步处理收集的内容
                    log.info("流式响应完成，开始保存结果... ");
                    /*Mono.fromRunnable(() -> print(fullContent.toString())
                    ).subscribeOn(Schedulers.boundedElastic()).subscribe();*/
                })
                .blockLast(Duration.ofMinutes(10));
        return fullContent.toString();
    }

    private void print(String result) {
        String jsonResult = ConverterUtils.convertToJsonStr(result);
        System.out.println(jsonResult);
    }

    public String readHtmlFile(String filePath) {
        byte[] encoded = null;
        try {
            encoded = Files.readAllBytes(Paths.get(filePath));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return new String(encoded, StandardCharsets.UTF_8);
    }

}
