package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.model.dto.AddExamQuestionParam;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import com.joinus.knowledge.model.param.CreateExamParam;
import com.joinus.knowledge.model.param.UpdateMathExamParam;
import com.joinus.knowledge.model.vo.FileVO;
import com.joinus.knowledge.model.vo.MathExamQuestionVO;
import com.joinus.knowledge.model.vo.MathExamVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import com.joinus.knowledge.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 试卷处理接口
 */
@Tag(name = "数学试卷处理接口", description = "提供数学试卷录入相关功能")
@RestController
@RequestMapping("/math/exams")
@RequiredArgsConstructor
@Slf4j
public class MathExamController {

    private final MathExamsService mathExamsService;
    @Resource
    private AIAbilityService aiAbilityService;
    @Resource
    private MathExamQuestionsService mathExamQuestionsService;
    @Resource
    private QuestionFileService questionFileService;
    @Resource
    private FilesService filesService;
    @Resource
    private ImageRecognizeService imageRecognizeService;
    @Autowired
    private MathExamFilesService mathExamFilesService;
    @Resource
    private TemporalWorkflowService temporalWorkflowService;
    @Resource
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Resource
    private MathExamTagsService mathExamTagsService;

    @Operation(summary = "根据题目ids创建数学试卷")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = UUID.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/by-questions")
    public Result<UUID> createMathExam(@RequestBody @Valid CreateExamParam param) {
        UUID examId = mathExamsService.createMathExam(param);
        return Result.success(examId);
    }

    @Operation(summary = "获取试卷详情")
    @Parameters({
            @Parameter(name = "id", description = "试卷ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = FileVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}")
    public Result<MathExamVO> queryExamDetail(@PathVariable("id") UUID id) {
        MathExamVO mathExamVO = mathExamsService.queryExamDetailById(id);
        return Result.success(mathExamVO);
    }

    @Operation(summary = "修改试卷-主要是修改试卷的审核状态")
    @Parameters({
            @Parameter(name = "id", description = "试卷ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = FileVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/{id}")
    public Result<MathExamVO> updateExamDetail(@PathVariable("id") UUID id,
                                               @RequestBody UpdateMathExamParam param) {
        MathExamVO mathExamVO = mathExamsService.updateExamDetail(id, param);
        if (null != param.getState() && param.getState() == ExamStateEnum.HUMAN_RECOGNIZED) {
            analyzeMathExamAsync(id);
        }
        return Result.success(mathExamVO);
    }

    @Operation(summary = "获取试卷题目列表")
    @Parameters({
            @Parameter(name = "id", description = "试卷ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathExamQuestionVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}/questions")
    public Result<List<MathExamQuestionVO>> listMathExamQuestions(@PathVariable("id") UUID id) {
        List<MathExamQuestionVO> questions = mathExamsService.listMathExamQuestions(id);
        return Result.success(questions);
    }

    @Operation(summary = "添加试卷题目")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = QuestionDetailVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/{id}/questions")
    public Result<QuestionDetailVO> addMathExamQuestion(@PathVariable("id") UUID examId,
                                                        @RequestBody AddExamQuestionParam param) {
        QuestionDetailVO question = mathExamsService.addMathExamQuestion(examId, param);
        return Result.success(question);
    }

    @Operation(summary = "修改试卷题目")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = QuestionDetailVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/{id}/questions/{questionId}")
    public Result<QuestionDetailVO> updateMathExamQuestion(@PathVariable("id") UUID examId,
                                                           @PathVariable("questionId") UUID questionId,
                                                           @RequestBody AddExamQuestionParam param) {
        QuestionDetailVO question = mathExamsService.updateMathExamQuestion(examId, questionId, param);
        return Result.success(question);
    }

    @Operation(summary = "删除试卷题目")
    @Parameters({
            @Parameter(name = "id", description = "试卷ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa"),
            @Parameter(name = "questionId", description = "题目ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = Boolean.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("/{id}/questions/{questionId}")
    public Result<Boolean> deleteMathExamQuestion(@PathVariable("id") UUID examId,
                                                  @PathVariable("questionId") UUID questionId) {
        mathExamsService.deleteMathExamQuestion(examId, questionId);
        return Result.success(true);
    }

    private void analyzeMathExamAsync(UUID examId) {

        MathExam mathExam = mathExamsService.getById(examId);
        if (mathExam.getName().contains("薄弱点专项训练试卷")) {
            throw new BusinessException("专项训练试卷不支持分析");
        }

        AnalyzeExamParam examParam = AnalyzeExamParam.builder()
                .examId(examId)
                .publisher(mathExam.getPublisher())
                .build();
        CompletableFuture.runAsync(() -> temporalWorkflowService.analyzeKnowledgePointsForExam(examParam));
    }
}
