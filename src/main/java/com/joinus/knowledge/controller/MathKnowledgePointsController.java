package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.service.MathKnowledgePointsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 数学知识点管理 Controller
 */
@RestController
@RequestMapping("/api/math/knowledge-points")
@RequiredArgsConstructor
public class MathKnowledgePointsController {

    private final MathKnowledgePointsService mathKnowledgePointsService;

    @PostMapping("/list")
    public Result<List<MathKnowledgePointVO>> list(@RequestBody List<UUID> kpIds) {
        return Result.success(mathKnowledgePointsService.listByIds(kpIds));
    }
}
