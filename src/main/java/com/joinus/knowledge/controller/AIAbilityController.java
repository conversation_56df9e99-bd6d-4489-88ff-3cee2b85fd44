package com.joinus.knowledge.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.aliyun.sts20150401.models.AssumeRoleResponseBody;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.config.AliOssProperties;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.*;
import com.joinus.knowledge.model.dto.MathTrainingHtmlDTO;
import com.joinus.knowledge.model.dto.pdf.PdfParam;
import com.joinus.knowledge.model.entity.MathAnswer;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.util.StreamResponseCollector;
import com.joinus.knowledge.util.StreamResponseLogger;
import com.joinus.knowledge.utils.AliOssUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * AI能力控制器
 */
@Tag(name = "AI能力接口", description = "提供AI相关的能力，包括图像识别、文本处理等功能")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/ai/ability")
public class AIAbilityController {

    @Resource
    private ImageRecognizeService imageRecognizeService;

    @Resource
    private HttpServletRequest request;

    @Resource
    private StreamContentStorageService streamContentStorageService;

    @Resource
    private AIAbilityService aiAbilityService;

    @Resource
    private AIChatService aiChatService;

    @Resource
    private AliOssUtils aliOssUtils;

    @Resource
    private OssService ossService;

    @Resource
    private AliOssProperties aliOssProperties;

    @Resource
    private TemporalWorkflowService temporalWorkflowService;

    @Resource
    private SpeechAsrService speechAsrService;
    @Autowired
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Autowired
    private MathQuestionsService mathQuestionsService;
    @Autowired
    private FilesService filesService;
    @Autowired
    private MathSectionService mathSectionService;
    @Autowired
    private MathExamsService mathExamsService;
    @Autowired
    private MathChapterService mathChapterService;
    @Autowired
    private TextbooksService textbooksService;
    @Autowired
    private MathCatalogNodesService mathCatalogNodesService;


    @Operation(summary = "根据图片解析题目坐标", description = "通过图片URL解析试卷中的题目，并返回每个题目的坐标信息")
    @Parameters({
            @Parameter(name = "imgUrl", description = "图片URL地址，必须是可访问的图片链接", required = true, example = "https://example.com/math_paper.jpg"),
            @Parameter(name = "paperSubjectType", description = "试卷学科类型，用于指定解析的学科类型", required = true, schema = @Schema(implementation = PaperSubjectType.class), example = "MATH")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功解析题目坐标", content = @Content(mediaType = "application/json", schema = @Schema(implementation = CutQuestionVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/coordinate-point/multi")
    public Result<CutQuestionVO> getMultiCoordinateFromImage(@RequestBody  GetCoordinateParam param) {
        //先查询数据库中是否有坐标信息
        CutQuestionVO cutQuestionVO = filesService.getPositions(param.getOssKey(), param.getOssEnum());
        if (null != cutQuestionVO) {
            return Result.success(cutQuestionVO);
        }
        //调用图像识别服务
        cutQuestionVO = imageRecognizeService.paperStructed(param);
        return Result.success(cutQuestionVO);
    }

    @PostMapping("/text/stream")
    public Flux<String> textStream(@RequestBody SolveQuestionParam param) {
        log.info("收到文本流请求，提示词: {}, 模型客户端: {}", param.getPrompt(), AIModelType.DOUBAO_DEEPSEEK_R1);

        // 记录开始时间
        final long startTime = System.currentTimeMillis();

        // 生成请求ID
        String requestId = UUID.randomUUID().toString();

        // 获取原始流
        Flux<String> originalFlux = aiChatService.chatStream(param.getPrompt(), AIModelType.DOUBAO_DEEPSEEK_R1);

        // 获取请求相关信息
        String sourceIp = request.getRemoteAddr();
        String sourcePath = request.getRequestURI();

        // 使用非阻塞方式收集流内容，同时立即返回原始流
        return StreamResponseCollector.collectFluxNonBlocking(
                originalFlux,
                "textStream-" + requestId,
                // 流结束时的异步处理函数
                fullContent -> {
                    long processingTime = System.currentTimeMillis() - startTime;
                    // 使用StreamContentStorageService存储内容
                    return streamContentStorageService.storeContent(
                            requestId,
                            param,
                            fullContent,
                            processingTime,
                            sourceIp,
                            sourcePath
                    );
                }
        );
    }

    @PostMapping("/text/stream/mock")
    public Flux<String> textStreamMock(@RequestBody SolveQuestionParam param) {
        log.info("收到文本流请求，提示词: {}, 模型客户端: {}", param.getPrompt(), AIModelType.DOUBAO_DEEPSEEK_R1);

        // 记录开始时间
        final long startTime = System.currentTimeMillis();

        // 生成请求ID
        String requestId = UUID.randomUUID().toString();

        int chunkSize = 20; // 每块20字符
        String result = param.getPrompt();
        Flux<String> streamFlux = Flux.range(0, (result.length() + chunkSize - 1) / chunkSize)
                .map(i ->
                        result.substring(i * chunkSize,
                                Math.min((i + 1) * chunkSize, result.length())))
                .delayElements(Duration.ofMillis(20));
        // 获取原始流

        // 使用非阻塞方式收集流内容，同时立即返回原始流
        return StreamResponseLogger.logFlux(streamFlux, "textStream-" + requestId);
    }


    @Operation(summary = "获取ali OSS临时凭证", description = "获取ali OSS临时凭证")
//    @ApiOperationSupport(order = 2)
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功解析题目坐标", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OssAliTokenVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/oss/ali/token")
    public Result<OssAliTokenVO> getOssToken() throws Exception {
        AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials credentials = aliOssUtils.getToken();
        OssAliTokenVO ossAliTokenVO = OssAliTokenVO.builder()
                .bucket(aliOssProperties.getBucketName())
                .region(aliOssProperties.getRegion())
                //这个endpoint已解决pc端跨域问题，公司oss域名的话会出现跨域问题，app端没有这个现象。
                .endpoint("https://oss-cn-beijing.aliyuncs.com")
                .accessKeyId(credentials.getAccessKeyId())
                .accessKeySecret(credentials.getAccessKeySecret())
                .expiration(credentials.getExpiration())
                .securityToken(credentials.getSecurityToken())
                .build();
        return Result.success(ossAliTokenVO);
    }

    @Operation(summary = "上传文件到OSS", description = "上传文件到OSS")
//    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "ossEnum", description = "oss枚举类", required = true, schema = @Schema(implementation = OssEnum.class), example = "ALIYUN_EDU_KNOWLEDGE_HUB")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "上传成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OssFileVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/oss/upload")
    public Result<OssFileVO> uploadFileToOss(@RequestParam("file") MultipartFile file,
                                             @RequestParam("ossEnum") OssEnum ossEnum) throws IOException {
        String fileName = UUID.randomUUID() + "." + FilenameUtils.getExtension(file.getOriginalFilename());

        OssFileVO ossFileVO = ossService.uploadInputStreamToOss(ossEnum, "math-question", fileName, file.getInputStream(), file.getSize());

        return Result.success(ossFileVO);
    }


    /**
     * 根据图片和坐标裁剪图片，上传至oss，返回给前端
     *
     * @param param
     * @return
     * @throws IOException
     */
    @Operation(summary = "根据图片和坐标裁剪图片，上传至oss，返回给前端", description = "根据图片和坐标裁剪图片，上传至oss，返回给前端")
//    @ApiOperationSupport(order = 4)
    @Parameters({
            @Parameter(name = "param", description = "切题参数", required = true, schema = @Schema(implementation = CutImageFromPositionsParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "上传成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OssFileVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/img/cut")
    public Result<OssFileVO> cutImageFromPositions(@RequestBody CutImageFromPositionsParam param) throws IOException {
        OssFileVO ossFileVO = imageRecognizeService.cutImageFromPositions(param);
        return Result.success(ossFileVO);
    }

    @Operation(summary = "擦除笔迹", description = "擦除图片中的画笔轨迹，上传至oss，返回给前端")
//    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "param", description = "擦除笔迹参数", required = true, schema = @Schema(implementation = ErasePenMarksParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "上传成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OssFileVO.class)))
    })
    @PostMapping("/oss/erase-pen-marks")
    public Result<OssFileVO> erasePenMarks( @RequestBody ErasePenMarksParam param) throws IOException {
        return Result.success(imageRecognizeService.removeHandwriting(param));
    }

    @Operation(summary = "获取oss临时链接", description = "获取oss临时链接")
//    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "ossEnum", description = "oss枚举类", required = true, schema = @Schema(implementation = OssEnum.class), example = "ALIYUN_EDU_KNOWLEDGE_HUB"),
            @Parameter(name = "ossKey", description = "oss文件key", required = true, schema = @Schema(implementation = String.class), example = "uat/math-question/2025/3/20/1f7b2993e6c3455b81f2234419be94a3e1784f1a-b429-48c2-a8bd-ad2fab08eb28.png")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "上传成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OssFileVO.class)))
    })
    @GetMapping("/oss/presigned-url")
    public Result<OssFileVO> getPresignedUrl(@RequestParam("ossEnum") OssEnum ossEnum,
                                             @RequestParam("ossKey") String ossKey) {
        OssFileVO ossFileVO = ossService.getPresignedInfo(ossEnum, ossKey);
        return Result.success(ossFileVO);
    }

    @Operation(summary = "解题（同步返回方式）", description = "解题（同步返回方式）")
//    @ApiOperationSupport(order = 5)
    @Parameters({
            @Parameter(name = "param", description = "解题参数", required = true, schema = @Schema(implementation = SolveQuestionParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "解题成功", content = @Content(mediaType = "application/json"))
    })
    @PostMapping("/math/problem/solve")
    public Result<String> solveMathProblem(@RequestBody SolveQuestionParam param) {
        if (CollUtil.isEmpty(param.getObjectNames()) && StrUtil.isBlank(param.getQuestionText())) {
            return Result.error(400, "图片和文本不能同时为空");
        }
        return Result.success(aiAbilityService.solveMathQuestion(param));
    }


    @Operation(summary = "解题（流式返回方式）", description = "解题（流式返回方式）")
//    @ApiOperationSupport(order = 6)
    @Parameters({
            @Parameter(name = "param", description = "解题参数", required = true, schema = @Schema(implementation = SolveQuestionParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "解题成功",
                    content = @Content(mediaType = "text/event-stream")) // 修改mediaType
    })
    @PostMapping(value = "/math/problem/solve/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> solveMathProblemViaStream(@RequestBody SolveQuestionParam param) {
        if (CollUtil.isEmpty(param.getObjectNames()) && StrUtil.isBlank(param.getQuestionText())) {
            throw new RuntimeException("图片和文本不能同时为空");
        }

        // 获取原始的流
        Flux<String> originalFlux = aiAbilityService.solveMathQuestionViaStream(param);

//        // 检查是否需要转换为 HTML 并处理 LaTeX 公式
//        boolean convertToHtml = param.getExtraParams() != null &&
//                Boolean.TRUE.equals(param.getExtraParams().get("convertToHtml"));
//        boolean useImgTag = param.getExtraParams() != null &&
//                Boolean.TRUE.equals(param.getExtraParams().get("useImgTag"));
//
//        if (convertToHtml) {
//            // 转换 Markdown 为 HTML，并处理 LaTeX 公式
//            return originalFlux.map(content -> MarkdownLaTeXProcessor.processToHtml(content, useImgTag));
//        }

        return originalFlux;
    }

    @Operation(summary = "检查题目是否已存在", description = "检查题目是否已存在")
//    @ApiOperationSupport(order = 6)
    @PostMapping(value = "/math/check/exist-question")
    public Result<UUID> checkExistQuestion( @RequestBody CheckExistQuestionParam param) {
        //TODO 后续要通过图片来判断题库中是否已存在此题目
        return Result.success();
    }


    @Operation(summary = "获取题解知识点和难度", description = "获取题解知识点和难度")
//    @ApiOperationSupport(order = 7)
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = GenerateKnowledgePointParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = KnowledgePointsAndDifficultyVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/problem/knowledge-points")
    public Result<KnowledgePointsAndDifficultyVO> getProblemKnowledgePoints( @RequestBody GenerateKnowledgePointParam param) {
        return Result.success(aiAbilityService.getProblemKnowledgePoints(param));
    }

    @Operation(summary = "根据题目id举一反三", description = "根据题目id举一反三")
//    @ApiOperationSupport(order = 8)
    @Parameters({
            @Parameter(name = "questionId", description = "题目id", required = true)
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = UUID.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/problem/generate")
    public Result<UUID> generateQuestion(@RequestBody GenerateQuestionParam param) {
        UUID similarQuestionId = aiAbilityService.getSimilarQuestionId(param.getQuestionId());
        return Result.success(similarQuestionId);
    }

    @Operation(summary = "检测题目是否存在图形", description = "检测题目是否存在图形")
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = CheckExistGraphicsParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = Boolean.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/check/exist-graphics")
    public Result<Boolean> checkExistGraphics(@RequestBody CheckExistGraphicsParam param) {
//        return Result.success(aiAbilityService.checkExistGraphics(param.getQuestionText()));
        return Result.success(false);
    }

    @Operation(summary = "检测试卷是否存在V2", description = "检测试卷是否存在")
//    @ApiOperationSupport(order = 10)
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = CheckExistExamParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = CheckExistExamVO.class))),
            @ApiResponse()
    })
    @PostMapping("/math/check/exist-exam/V2")
    public Result<CheckExistExamVO> checkExistExamV2(@Valid @RequestBody CheckExistExamParam param) {
        return Result.success(aiAbilityService.checkExistExamV2(param));
    }

    @Operation(summary = "检测试卷的知识点是否存在", description = "检测试卷的知识点是否存在")
//    @ApiOperationSupport(order = 10)
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = CheckExistExamKnowledgePointsParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = CheckExamExistKnowledgePointsVO.class))),
            @ApiResponse()
    })
    @PostMapping("/math/check/exam/exist-knowledge-points")
    public Result<CheckExamExistKnowledgePointsVO> checkExamExistKnowledgePoints( @RequestBody CheckExistExamKnowledgePointsParam param) {
        return Result.success(aiAbilityService.checkExamExistKnowledgePoints(param));
    }

    @Operation(summary = "ai解题流式内容解析", description = "ai解题流式内容解析")
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = ParseAnswerParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathAnswer.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/parse-answer")
    public Result<MathAnswer> parseAnswer(@RequestBody ParseAnswerParam param) {
        return Result.success(aiAbilityService.parseAnswer(param.getText()));
    }

    @Operation(summary = "试卷卷面拆分", description = "试卷卷面拆分")
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = CutExamParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = UUID.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @Deprecated
    @PostMapping("/math/exam/cut")
    public Result<UUID> cutExam(@RequestBody CutExamParam param) {
        return Result.success(aiAbilityService.cutExam(param));
    }

    @Operation(summary = "试卷卷面拆分v2", description = "试卷卷面拆分v2")
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = CutExamParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = UUID.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/exam/cut/v2")
    public Result<UUID> cutExamV1(@RequestBody  CutExamParam param) {
        return Result.success(aiAbilityService.cutExamV2(param));
    }


    @Operation(summary = "数学专项训练", description = "数学专项训练，返回html")
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = SpecialTrainingParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathTrainingHtmlVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/special-training")
    public Result<MathTrainingHtmlVO> mathSpecialTraining( @RequestBody SpecialTrainingParam param) {
        MathTrainingHtmlDTO mathTrainingHtmlDTO = aiAbilityService.mathSpecialTraining(param);
        return Result.success(BeanUtil.copyProperties(mathTrainingHtmlDTO, MathTrainingHtmlVO.class));
    }

    @Operation(summary = "数学专项训练生成pdf", description = "数学专项训练生成pdf")
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = SpecialTrainingParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/special-training/pdf")
    public Result<Map<String, String>> mathSpecialTrainingToPDF( @RequestBody SpecialTrainingParam param) {
        return Result.success(aiAbilityService.mathSpecialTrainingToPDF(param));
    }

    @Operation(summary = "试卷分析", description = "生成试卷中每个题目的知识点")
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = AnalyzeExamParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = UUID.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/exam/analyze")
    public Result<UUID> analyzeExam(@Valid @RequestBody AnalyzeExamParam examParam) {
        Assert.notNull(examParam.getExamId(), "examId参数不能为空");
        MathExam exam = mathExamsService.getById(examParam.getExamId());
        if (null != exam && ExamStateEnum.DONE == exam.getState()) {
            throw new BusinessException("该试卷已经分析完成，不需要再进行分析");
        }
        CompletableFuture.runAsync(() -> temporalWorkflowService.analyzeExam(examParam));
        return Result.success();
    }

    @Operation(summary = "试卷拆分", description = "把试卷的题目（坐标）截图存储且ocr识别")
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = AnalyzeExamParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = UUID.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/exam/cut-questions")
    public Result<UUID> cutExam(@Valid @RequestBody AnalyzeExamParam examParam) {
        CompletableFuture.runAsync(() -> temporalWorkflowService.cutExam(examParam));
        return Result.success();
    }

    @PutMapping("/baidu/speech-asr")
    @Operation(summary = "百度语音识别转文字", description = "语音识别")
    @Parameters({
            @Parameter(name = "path", description = "文件路径 阿里文件key(语音文件的格式，pcm/wav/amr/m4a)", required = true),
    })
    public Result<String> speechAsr(@RequestParam("path") String path) throws IOException {
        return Result.success(speechAsrService.baiduSpeechAsr(path));
    }

    @PutMapping("/ali/speech-asr")
    @Operation(summary = "阿里语音识别转文字", description = "语音识别")
    @Parameters({
            @Parameter(name = "path", description = "文件路径 阿里文件key (语音文件的格式，PCM、WAV、OPUS、SPEEX、AMR、MP3、AAC)", required = true),
    })
    public Result<String> aliSpeechAsr(@RequestParam("path") String path) throws IOException {
        return Result.success(speechAsrService.aliSpeechAsr(path));
    }


    @Operation(summary = "专项训练-查询知识点的题型", description = "专项训练-查询知识点的题型")
    @Parameters({
            @Parameter(name = "knowledgePointIds", description = "知识点id列表", required = true, schema = @Schema(implementation = UUID.class), example = "f8fa7f68-96de-4f03-8fd2-f92f6df494a9")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathKnowledgePointVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/math/special-training/question-types")
    public Result<List<MathKnowledgePointVO>> listQuestionTypeByKnowledgePointIds(@RequestParam("knowledgePointIds") List<UUID> knowledgePointIds) {
        List<MathKnowledgePointVO> result = mathKnowledgePointsService.listQuestionTypeByKnowledgePointIds(knowledgePointIds);
        return Result.success(result);
    }

    @Operation(summary = "专项训练-查询知识点的题型V2", description = "专项训练-查询知识点的题型")
    @Parameters({
            @Parameter(name = "knowledgePointIds", description = "知识点id列表", required = true, schema = @Schema(implementation = UUID.class), example = "f8fa7f68-96de-4f03-8fd2-f92f6df494a9")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathKnowledgePointVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/math/special-training/question-types/V2")
    public Result<Map<String, Object>> listQuestionTypeByKnowledgePointIdsV2Get(@RequestParam("knowledgePointIds") List<UUID> knowledgePointIds) {
        Map<String, Object> result = mathKnowledgePointsService.listQuestionTypeByKnowledgePointIdsV2(knowledgePointIds);
        return Result.success(result);
    }

    @PostMapping("/math/special-training/question-types/V2")
    public Result<Map<String, Object>> listQuestionTypeByKnowledgePointIdsV2(@RequestBody List<UUID> knowledgePointIds) {
        Map<String, Object> result = mathKnowledgePointsService.listQuestionTypeByKnowledgePointIdsV2(knowledgePointIds);
        return Result.success(result);
    }

    @Operation(summary = "试卷分析-查询知识点的章节、小节", description = "试卷分析-查询知识点的章节、小节")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/math/exam-analysis/knowledge-points")
    public Result<List<SectionKnowledgePointVO>> listSectionKnowledgePointByKnowledgeIds( @ParameterObject SectionKnowledgePointParam param) {
        List<SectionKnowledgePointVO> result = mathKnowledgePointsService.listSectionKnowledgePointByKnowledgeIds(param);
        return Result.success(result);
    }

    @Operation(summary = "专项训练-获取题目V2", description = "专项训练-获取题目")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = Map.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/special-training/questions/V2")
    public Result<Map<String, Object>> listMathSpecialTrainingQuestionsV2( @RequestBody MathTrainingQuestionsParamV2 param) {
        Map<String, Object> result = mathQuestionsService.listMathSpecialTrainingQuestionsV2(param);
        return Result.success(result);
    }

    @Operation(summary = "暑期训练-小节测试", description = "暑期训练-小节测试")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = Map.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/holiday-training/questions/section")
    public Result<Map<String, Object>> listMathHolidayTrainingQuestionsBySectionId( @RequestBody MathTrainingQuestionsParamV2 param) {
        Map<String, Object> results = mathQuestionsService.listMathHolidayTrainingQuestionsBySectionId(param);
        return Result.success(results);
    }

    @Operation(summary = "暑期训练-章末测试", description = "暑期训练-章末测试")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = Map.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/holiday-training/questions/chapter")
    public Result<HashMap<String, Object>> listMathHolidayTrainingQuestionsByChapterId( @RequestBody MathTrainingQuestionsParamV2 param) {
        HashMap<String, Object> results = mathQuestionsService.listMathHolidayTrainingQuestionsByChapterId(param);
        return Result.success(results);
    }

    @Operation(summary = "暑期训练-综合测试", description = "暑期训练-综合测试")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathQuestionVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/holiday-training/questions/textbook")
    public Result<List<MathQuestionVO>> listMathHolidayTrainingQuestions( @RequestBody MathTrainingQuestionsParamV2 param) {
        List<MathQuestionVO> results = mathQuestionsService.listMathHolidayTrainingQuestions(param);
        return Result.success(results);
    }

    @Operation(summary = "试卷id查询题目（专项训练、小节测试、章末测试）")
    @Parameters({
            @Parameter(name = "examId", description = "试卷id", required = true, schema = @Schema(implementation = UUID.class), example = "450d2bc4-2e4f-439c-aab7-b7075894fae0"),
            @Parameter(name = "source", description = "试卷来源", required = false, schema = @Schema(implementation = ExamSourceType.class), example = "HOLIDAY_TRAINING")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = Map.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/math/exams/{examId}/training")
    public Result<Map<String, Object>> listQuestionsByExamId(@PathVariable("examId") UUID examId,
                                                             @RequestParam(name = "source", required = false) ExamSourceType source) {
        Map<String, Object> map = mathQuestionsService.listQuestionsByExamIdForTraining(examId, source);
        return Result.success(map);
    }

    @Operation(summary = "试卷id查询题目（真题卷）")
    @Parameters({
            @Parameter(name = "examId", description = "试卷id", required = true, schema = @Schema(implementation = UUID.class), example = "e42cc8a6-f3b4-408d-a75e-2c2f4a112e31")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathQuestionVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/math/exams/{examId}")
    public Result<List<MathQuestionVO>> listQuestionsByExamId(@PathVariable("examId") UUID examId,
                                                              @RequestParam(value = "publisher", required = false) PublisherType publisher) {
        List<MathQuestionVO> results = mathQuestionsService.listQuestionsByExamIdForTraining(examId, publisher);
        return Result.success(results);
    }

    @Operation(summary = "根据试卷ID获取所有章节、小节信息", description = "根据试卷ID获取所有章节、小节信息")
    @Parameters({
            @Parameter(name = "examId", description = "试卷ID", required = true, schema = @Schema(implementation = UUID.class), example = "bf6f9ca2-b61b-45b5-befb-f9e3ff7aac07")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = SectionVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/math/books-chapters-sections/exams/{examId}")
    public Result<List<SectionVO>> listAllsectionsByExamId(@PathVariable("examId") UUID examId,
                                                           @RequestParam(value = "publisher", required = false) PublisherType publisher) {
        List<SectionVO> results = mathSectionService.listAllsectionsByExamId(examId, publisher);
        return Result.success(results);
    }

    @Operation(summary = "根据小节ID获取知识点", description = "根据小节ID列表获取相关知识点信息")
    @Parameters({
            @Parameter(name = "sectionIds", description = "小节ID列表", required = true, schema = @Schema(implementation = UUID.class), example = "d5ede9c2-3db2-4006-80c7-d61f8deef136")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathKnowledgePointVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/math/sections/knowledge-points")
    public Result<List<MathKnowledgePointVO>> listBySectionIds(@RequestParam("sectionIds") List<UUID> sectionIds) {
        List<MathKnowledgePointVO> results = mathKnowledgePointsService.listBySectionIds(sectionIds);
        return Result.success(results);
    }

    @Operation(summary = "暑期训练-查询所有章、小节")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathQuestionVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/math/sections")
    public Result<List<SectionVO>> listSections(@RequestParam("grade") Integer grade,
                                                @RequestParam("publisher") PublisherType publisher,
                                                @RequestParam("semester") Integer semester) {
        List<SectionVO> results = mathSectionService.listSections(grade, publisher, semester);
        return Result.success(results);
    }

    @Operation(summary = "暑期训练-查询所有章")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathQuestionVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/math/chapters")
    public Result<List<MathChapterVO>> listChapters(@RequestParam(value = "grade", required = false) Integer grade,
                                                    @RequestParam(value = "semester", required = false) Integer semester,
                                                    @RequestParam(value = "publisher", required = false) PublisherType publisher) {
        List<MathChapterVO> results = mathChapterService.list(null, grade, semester, publisher);
        return Result.success(results);
    }

    @Operation(summary = "暑期训练-查询电子教材")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = Map.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/math/electronic-textbook")
    public Result<Map<String, Object>> queryElectronicTextbook(@RequestParam("publisher") PublisherType publisher,
                                                  @RequestParam("grade") Integer grade,
                                                  @RequestParam("semester") Integer semester) {
        Map<String, Object> result = textbooksService.queryElectronicTextbook(publisher, grade, semester);
        return Result.success(result);
    }

    @Operation(summary = "暑期训练-查询小节的学习视频")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/math/section-videos")
    public Result<List<QuerySectionVideoVO>> querySectionVideos(@RequestBody List<UUID> sectionIds) {
        List<QuerySectionVideoVO> sectionVideos = mathSectionService.querySectionVideos(sectionIds);
        return Result.success(sectionVideos);
    }

    @Operation(summary = "已存在的母卷针对不同的教材版本重新分析知识点")
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = AnalyzeExamParam.class))
    })
    @PostMapping("/math/exam/knowledge-point/reanalyze")
    public Result<PdfParam> reanalyzeExamKnowledgePoint(@Valid @RequestBody AnalyzeExamParam param) {
        Assert.notNull(param.getExamId(), "examId参数不能为空");
        Assert.notNull(param.getPublisher(), "publisher参数不能为空");

        CompletableFuture.runAsync(() -> temporalWorkflowService.reanalyzeExamKnowledgePoint(param));
        return Result.success();
    }

    @Operation(summary = "重新关联知识点")
    @PostMapping("/questions/{id}/knowledge-points/rebuild")
    public Result<Object> rebuildQuestionKnowledgePoints(@PathVariable("id") UUID id) {
        aiAbilityService.rebuildQuestionKnowledgePoints(id);
        return Result.success();
    }

    @Operation(summary = "重新解题，流式输出")
    @PostMapping(value = "/questions/{id}/solve/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> solveQuestionViaStream(@PathVariable("id") UUID id) {
        return aiAbilityService.solveMathQuestionViaContentPartsStream(id);
    }

    @Operation(summary = "查询目录树")
    @GetMapping("/math/catalog-nodes")
    public Result<List<MathCatalogNodeVO>> listCatalogNodes(@RequestParam("chapterId") UUID chapterId) {
        List<MathCatalogNodeVO> results = mathCatalogNodesService.listCatalogNodes(chapterId);
        return Result.success(results);
    }
}
