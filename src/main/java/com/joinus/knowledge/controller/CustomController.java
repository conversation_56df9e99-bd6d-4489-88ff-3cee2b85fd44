package com.joinus.knowledge.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.ExamFileType;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.dto.pdf.PdfParam;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.QuestionFile;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import com.joinus.knowledge.model.param.PageQuestionParam;
import com.joinus.knowledge.model.param.SolveQuestionParam;
import com.joinus.knowledge.model.vo.CheckExistExamVO;
import com.joinus.knowledge.model.vo.FileVO;
import com.joinus.knowledge.model.vo.MathQuestionVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.service.impl.OnlyOnceServiceImpl;
import com.joinus.knowledge.service.impl.PdfGenerator;
import com.joinus.knowledge.temporal.workflow.MathExamReanalyzeKnowledgePointWorkflow;
import com.joinus.knowledge.utils.AliOssUtils;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/custom")
public class CustomController {

    @Resource
    private MathExamQuestionsService mathExamQuestionsService;
    @Resource
    private QuestionFileService questionFileService;
    @Resource
    private FilesService filesService;
    @Resource
    private AIAbilityService aiAbilityService;
    @Resource
    private MathQuestionsService mathQuestionsService;
    @Resource
    private OnlyOnceServiceImpl onlyOnceService;
    @Resource
    private PdfGenerator pdfGenerator;
    @Resource
    private TemporalWorkflowService temporalWorkflowService;
    @Resource
    private MathExamFilesService mathExamFilesService;
    @Resource
    private AliOssUtils aliOssUtils;

    @PostMapping("/exam/solve")
    public Result<String> examSolve(@RequestParam("examId") UUID examId) {
        List<MathQuestion> questions = mathExamQuestionsService.listQuestionsByExamId(examId);
        List<QuestionFile> questionFiles = questionFileService.lambdaQuery()
                .in(QuestionFile::getQuestionId, questions.stream().map(MathQuestion::getId).toList())
                .list();
        Map<UUID, List<UUID>> questionFileList = questionFiles.stream()
                .collect(Collectors.groupingBy(QuestionFile::getQuestionId, Collectors.mapping(QuestionFile::getFileId, Collectors.toList())));
        questions.forEach(question -> {
            SolveQuestionParam solveQuestionParam = new SolveQuestionParam();
            solveQuestionParam.setQuestionId(question.getId());
            List<UUID> fileIds = questionFileList.get(question.getId());
            List<File> files = filesService.lambdaQuery()
                    .in(File::getId, fileIds)
                    .list();
            List<String> objectNames = files.stream()
                    .map(File::getOssUrl)
                    .toList();
            solveQuestionParam.setObjectNames(objectNames);
            solveQuestionParam.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
            aiAbilityService.ocrQuestionImageAndSaveContent(solveQuestionParam);
            aiAbilityService.solveMathQuestion(solveQuestionParam);
        });
        return Result.success(examId.toString());
    }

    @PostMapping("/question/solve/question")
    public Result<String> examSolveQuestion(@RequestParam("questionId") UUID questionId) {
        MathQuestion question = mathQuestionsService.getById(questionId);
        List<QuestionFile> questionFiles = questionFileService.lambdaQuery()
                .eq(QuestionFile::getQuestionId, question.getId())
                .list();
        Map<UUID, List<UUID>> questionFileList = questionFiles.stream()
                .collect(Collectors.groupingBy(QuestionFile::getQuestionId, Collectors.mapping(QuestionFile::getFileId, Collectors.toList())));
        SolveQuestionParam solveQuestionParam = new SolveQuestionParam();
        solveQuestionParam.setQuestionId(question.getId());
        List<UUID> fileIds = questionFileList.get(question.getId());
        List<File> files = filesService.lambdaQuery()
                .in(File::getId, fileIds)
                .list();
        List<String> objectNames = files.stream()
                .map(File::getOssUrl)
                .toList();
        solveQuestionParam.setObjectNames(objectNames);
        solveQuestionParam.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
        aiAbilityService.solveMathQuestion(solveQuestionParam);
        return Result.success(questionId.toString());
    }

    @PostMapping("/key-points/update-page")
    public Result<String> updatePageIndex(@RequestParam("textbookId") UUID textbookId) {
        onlyOnceService.updatePageIndex(textbookId);
        return Result.success(textbookId.toString());
    }

    @PostMapping("/question-key-points/fix")
    public Result<String> fixMultiQuestionKnowledgePointRelation() {
        CompletableFuture.runAsync(() -> onlyOnceService.fixMultiQuestionKnowledgePointRelation());
        return Result.success();
    }

    @PostMapping("/question-types-mapping/fix")
    public Result<String> fixMultiQuestionTypesMapping() {
        CompletableFuture.runAsync(() -> onlyOnceService.fixMultiQuestionTypesMapping());
        return Result.success();
    }

    @GetMapping("/pdf/parameters")
    public Result<PdfParam> generatePdfParameters(@RequestParam("chapterId") UUID chapterId) {
        return Result.success(onlyOnceService.generatePdfParameters(chapterId));
    }

    @GetMapping("/pdf/parameters/question/{id}")
    public Result<PdfParam> generatePdfParametersById(@PathVariable("id") UUID id) {
        Page<MathQuestionVO> pageParam = new Page<>(1, 50);

        PageQuestionParam pageQuestionParam = new PageQuestionParam();

        pageQuestionParam.setId(id);

        Page<MathQuestionVO> resultPage = mathQuestionsService.page(pageParam, pageQuestionParam);

        List<MathQuestionVO> questionList = resultPage.getRecords();

        return Result.success(pdfGenerator.generatePdfParam(questionList));
    }


    @GetMapping("/exam/{id}/knowledge-point/reanalyze")
    public Result<PdfParam> reanalyzeExamKnowledgePoint(@PathVariable("id") UUID id) {
        AnalyzeExamParam param = new AnalyzeExamParam();
        param.setExamId(id);
        CompletableFuture.runAsync(() -> temporalWorkflowService.reanalyzeExamKnowledgePoint(param));
        return Result.success();
    }

    @GetMapping("/exam/{id}/files")
    public Result<List<String>> listExamFiles(@PathVariable("id") UUID id,
                                              @RequestParam("examFileType") ExamFileType examFileType) {

        List<FileVO> fileVOS = mathExamFilesService.listFilesByExamId(id);
        List<String> images = new ArrayList<String>();
        if (CollUtil.isNotEmpty(fileVOS)) {
            fileVOS.stream()
                    .filter(fileVO -> fileVO.getExamFileType().equals(examFileType))
                    .forEach(fileVO -> {
                        images.add(aliOssUtils.generatePresignedUrl(fileVO.getOssKey()));
                    });
        }
        return Result.success(images);
    }

    @PostMapping("/question/{id}/solve")
    public Result<String> solveQuestion(@PathVariable("id") UUID id) {
        SolveQuestionParam param = new SolveQuestionParam();
        QuestionDetailVO mathQuestion = mathQuestionsService.getDetailById(id);
        param.setQuestionId(id);
        param.setQuestionText(mathQuestion.getContent());
        return Result.success(aiAbilityService.solveMathQuestionViaContentParts(param));
    }

    @GetMapping("/math/check/exist-exam")
    public Result<CheckExistExamVO> checkExamExist(@RequestParam("examName") String examName) {
        CheckExistExamVO checkExistExamVO = aiAbilityService.checkExistExamByName(examName);
        return Result.success(checkExistExamVO);
    }
}
