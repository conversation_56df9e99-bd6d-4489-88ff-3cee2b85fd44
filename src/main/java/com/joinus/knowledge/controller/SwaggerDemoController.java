package com.joinus.knowledge.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Swagger示例控制器
 * 用于演示如何使用Swagger注解
 */
@Tag(name = "示例接口", description = "用于演示Knife4j的使用")
@RestController
@RequestMapping("/api/demo")
public class SwaggerDemoController {

    /**
     * 简单的GET请求示例
     */
    @Operation(summary = "获取示例数据", description = "返回一个简单的JSON示例")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取数据",
                    content = @Content(mediaType = "application/json")),
            @ApiResponse(responseCode = "404", description = "数据不存在")
    })
    @GetMapping("/hello")
    public Map<String, Object> hello() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "Hello, Knife4j!");
        result.put("status", "success");
        return result;
    }

    /**
     * 带路径参数的GET请求示例
     */
    @Operation(summary = "获取指定ID的数据", description = "根据ID获取数据")
    @Parameters({
            @Parameter(name = "id", description = "数据ID", required = true)
    })
    @GetMapping("/{id}")
    public Map<String, Object> getById(@PathVariable String id) {
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        result.put("message", "获取数据成功");
        return result;
    }

    /**
     * POST请求示例
     */
    @Operation(summary = "创建示例数据", description = "接收JSON数据并返回处理结果")
    @PostMapping("/create")
    public Map<String, Object> create(@RequestBody Map<String, Object> data) {
        Map<String, Object> result = new HashMap<>();
        result.put("data", data);
        result.put("message", "创建成功");
        return result;
    }
}
