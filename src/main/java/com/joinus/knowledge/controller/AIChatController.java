package com.joinus.knowledge.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.enums.PromptEnum;
import com.joinus.knowledge.model.entity.PromptTestLog;
import com.joinus.knowledge.model.param.ChatParam;
import com.joinus.knowledge.model.param.ImageParam;
import com.joinus.knowledge.model.response.StreamResponse;
import com.joinus.knowledge.service.AIChatService;
import com.joinus.knowledge.service.PromptTestLogService;
import com.joinus.knowledge.util.StreamResponseCollector;
import com.joinus.knowledge.utils.MinioUtils;
import com.joinus.knowledge.utils.PromptUtils;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.List;


@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/ai")
public class AIChatController {

    @Resource
    private MinioUtils minioUtils;

    @Resource
    private AIChatService aiChatService;

    @Resource
    private PromptTestLogService promptTestLogService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private PromptUtils promptUtils;

    @GetMapping("/{model}/chat")
    public Result<String> chat(@RequestParam("prompt") String prompt,@RequestParam("imageUrl") String imageUrl,@PathVariable("model") AIModelType model) {
        return Result.success(aiChatService.chat(prompt, imageUrl, model));
    }

    @GetMapping(value = "/{model}/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chatStream(@RequestParam("prompt") String prompt,@PathVariable("model") AIModelType model) {
        return aiChatService.chatStream(prompt, model);
    }

    @PostMapping("/math-image/ocr")
    public Result<String> mathImageOcr(@RequestBody ImageParam imageParam) {
        String promptText = promptUtils.getPromptTemplate(PromptEnum.MATH_IMAGE_OCR);
        if (StrUtil.isBlank(imageParam.getImageUrl())) {
            String objectName = StrUtil.format("/tmp/ocr/{}", System.currentTimeMillis());
            boolean result = minioUtils.uploadBase64Image(GlobalConstants.MINIO_BUCKET_NAME, objectName, imageParam.getImageData());
            if (result) {
                String imageUrl = minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, objectName);
                return Result.success(aiChatService.chat(promptText, imageUrl, AIModelType.JYSD_QWEN_VL));
            } else {
                return Result.error("图片处理失败");
            }
        } else {
            return Result.success(aiChatService.chat(promptText, imageParam.getImageUrl(), AIModelType.JYSD_QWEN_VL));
        }
    }

    @PostMapping("/math-image/ocr/stream")
    public Flux<String> mathImageOcrStream(@RequestBody ImageParam imageParam) {
        String promptText = promptUtils.getPromptTemplate(PromptEnum.MATH_IMAGE_OCR);
        if (StrUtil.isBlank(imageParam.getImageUrl())) {
            String objectName = StrUtil.format("/tmp/ocr/{}", System.currentTimeMillis());
            boolean result = minioUtils.uploadBase64Image(GlobalConstants.MINIO_BUCKET_NAME, objectName, imageParam.getImageData());
            if (result) {
                String imageUrl = minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, objectName);
                return aiChatService.chatStream(promptText, imageUrl, AIModelType.JYSD_QWEN_VL);
            } else {
                return Flux.just("图片处理失败");
            }
        } else {
            return aiChatService.chatStream(promptText, imageParam.getImageUrl(), AIModelType.JYSD_QWEN_VL);
        }
    }

    @PostMapping("/image/ocr")
    public Result<String> imageOcr(@RequestBody ImageParam imageParam) {
        String promptText = promptUtils.getPromptTemplate(PromptEnum.IMAGE_OCR);
        if (StrUtil.isBlank(imageParam.getImageUrl())) {
            String objectName = StrUtil.format("/tmp/ocr/{}", System.currentTimeMillis());
            boolean result = minioUtils.uploadBase64Image(GlobalConstants.MINIO_BUCKET_NAME, objectName, imageParam.getImageData());
            if (result) {
                String imageUrl = minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, objectName);
                return Result.success(aiChatService.chat(promptText, imageUrl, AIModelType.JYSD_QWEN_VL));
            } else {
                return Result.error("图片处理失败");
            }
        } else {
            return Result.success(aiChatService.chat(promptText, imageParam.getImageUrl(), AIModelType.JYSD_QWEN_VL));
        }
    }

    @PostMapping(value = "/chat/stream/test", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chatStream(@RequestBody ChatParam param) {
        ChatOptions chatOptions = ChatOptions.builder()
                .temperature(param.temperature())
                .build();
        Flux<String> flux = aiChatService.chatStream(param.promptText(), param.modelType(), chatOptions);
        return streamlogRequest(param, flux);
    }

    private Flux<String> streamlogRequest(ChatParam param, Flux<String> flux) {
        final StringBuilder fullContent = new StringBuilder();
        return flux
                .doOnNext(chunk -> {
                    // 收集每个内容块
                    StreamResponse response = JSONUtil.toBean(chunk, StreamResponse.class);
                    if (StrUtil.isNotEmpty(response.getContent())) {
                        fullContent.append(response.getContent());
                    }
                })
                .doOnComplete(() -> {
                    // 流完成时，异步处理收集的内容
                    log.info("流式响应完成，开始保存答案... ");
                    Mono.fromRunnable(() -> {
                        PromptTestLog promptTestLog = null;
                        try {
                            promptTestLog = PromptTestLog.builder()
                                    .param(objectMapper.writeValueAsString(param))
                                    .result(fullContent.toString())
                                    .build();
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                        promptTestLogService.save(promptTestLog);
                            }
                    ).subscribeOn(Schedulers.boundedElastic()).subscribe();
                });
    }

    @GetMapping("/prompt/test/log")
    public Result<List<PromptTestLog>> promptLog(@RequestParam("description") String description) {
        return Result.success(promptTestLogService.lambdaQuery().apply("param ->> 'description' = {0}", description).orderByDesc(PromptTestLog::getId).list());
    }

    @Resource
    @Qualifier("jysdDeepSeekR1OpenAiApi")
    OpenAiApi openAiApi;
    @GetMapping(value = "/openai/stream/test", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<OpenAiApi.ChatCompletionChunk> test() {
        OpenAiApi.ChatCompletionMessage message = new OpenAiApi.ChatCompletionMessage("帮我生成一篇200字的作文", OpenAiApi.ChatCompletionMessage.Role.USER);
        List<OpenAiApi.ChatCompletionMessage> messages = List.of(message);

        org.springframework.ai.openai.api.OpenAiApi.ChatCompletionRequest chatCompletionRequest =
                new org.springframework.ai.openai.api.OpenAiApi.ChatCompletionRequest(messages, "jysd-deepseekr1", 0.8D, true);
        return openAiApi.chatCompletionStream(chatCompletionRequest);
    }


}
