package com.joinus.knowledge.aop;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.knowledge.model.entity.AiChatLog;
import com.joinus.knowledge.service.AiChatLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Slf4j
@Aspect
@Component
public class ChatServiceAspect {

    @Resource
    private AiChatLogService aiChatLogService;

    @Around("execution(* com.joinus.knowledge.service.impl.AIChatServiceImpl.chat(..)) ||" +
            "execution(* com.joinus.knowledge.service.impl.AIChatServiceImpl.chatWithContentPart(..))")
    public Object logChatMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        log.debug("Method called: " + joinPoint.getSignature().toShortString());
        log.debug("Arguments: " + Arrays.toString(args));

        // 执行目标方法
        Object result = joinPoint.proceed();

        // 记录返回值
        log.debug("Method result: " + result);

        // 将参数和返回值保存到数据库
        saveToDatabase(args, result);

        return result;
    }

    private void saveToDatabase(Object[] args, Object result) {
        ObjectMapper objectMapper = new ObjectMapper();
        for (int i = 0; i < args.length; i++) {
            if (args[i] instanceof JsonNode) {
                try {
                    args[i] = objectMapper.writeValueAsString(args[i]);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        // 实现将参数和返回值保存到数据库的逻辑
        String resultStr = null;
        if (result != null) {
            resultStr = result.toString();
        }
        AiChatLog aiChatLog = AiChatLog.builder()
                .params(JSONUtil.toJsonStr(args))
                .result(resultStr)
                .build();
        aiChatLogService.save(aiChatLog);

    }
}