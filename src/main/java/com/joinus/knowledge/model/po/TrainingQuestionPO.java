package com.joinus.knowledge.model.po;

import com.joinus.knowledge.enums.MathQuestionReviewStatus;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.enums.QuestionType;
import com.joinus.knowledge.model.vo.MathPastExamPaperQuestionVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrainingQuestionPO implements Serializable {

    private UUID sectionId;

    private UUID questionId;

    private QuestionType questionType;

    private UUID knowledgePointId;

    private UUID questionTypeId;

    private Integer pageIndex;

    private Integer sectionSortNo;

    private PublisherType publisher;

    private MathQuestionReviewStatus reviewStatus;

    public MathPastExamPaperQuestionVO toMathPastExamPaperQuestionVO() {
        return MathPastExamPaperQuestionVO.builder()
                .id(questionId)
                .build();
    }
}
