package com.joinus.knowledge.model.po;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.vo.FileVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuestionPublishInfoPO implements Serializable {

    private PublisherType publisher;

    private Integer grade;

    private Integer semester;

    private String chapterName;

    private String sectionName;


}
