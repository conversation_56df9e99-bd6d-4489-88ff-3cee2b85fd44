package com.joinus.knowledge.model.po;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.vo.MathPastExamPaperQuestionVO;
import com.joinus.knowledge.model.vo.MathQuestionTypeVO;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

@Data
public class MathQuestionTypePO implements Serializable {

    private UUID id;

    private String name;

    private Integer grade;

    private Integer semester;

    private PublisherType publisher;

    private UUID questionId;

    private UUID chapterId;

    private String chapterName;

    private Integer chapterSortNo;

    private UUID sectionId;

    private String sectionName;

    private Integer sectionSortNo;

    public MathQuestionTypeVO convertToVO () {
        return MathQuestionTypeVO.builder()
                .id(id)
                .name(name)
                .grade(grade)
                .semester(semester)
                .publisher(publisher)
                .chapterId(chapterId)
                .chapterName(chapterName)
                .chapterSortNo(chapterSortNo)
                .sectionId(sectionId)
                .sectionName(sectionName)
                .sectionSortNo(sectionSortNo)
                .build();
    }

    public MathPastExamPaperQuestionVO toMathPastExamPaperQuestionVO() {
        return MathPastExamPaperQuestionVO.builder()
                .id(questionId)
                .build();
    }
}
