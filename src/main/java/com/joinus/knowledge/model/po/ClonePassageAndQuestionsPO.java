package com.joinus.knowledge.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClonePassageAndQuestionsPO implements Serializable {
    private UUID id;
    private UUID basePassageId;
    private UUID unitId;
    private String genArticle;
    private String title;
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String jsonGenQuestions;
    private String grade;
    private String writtingStyle;
//    @TableField(typeHandler = JsonbTypeHandler.class)
//    private String jsonQuestions;
//    private Integer round;
}
