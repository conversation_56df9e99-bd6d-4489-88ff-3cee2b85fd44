package com.joinus.knowledge.model.po;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SectionKnowledgePointPO extends MathKnowledgePoint {

     private PublisherType publisher;

     private UUID sectionId;

     private Integer pageIndex;
}
