package com.joinus.knowledge.model.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SectionVideoPO implements Serializable {

    private UUID sectionId;

    private UUID fileId;

    private String fileName;

    private Integer sortNo;

    private String ossType;

    private String ossBucket;

    private String ossKey;
}
