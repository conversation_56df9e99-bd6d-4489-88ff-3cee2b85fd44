package com.joinus.knowledge.model.po;

import com.joinus.knowledge.enums.QuestionType;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.vo.FileVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExamQuestionPO implements Serializable {

    private UUID questionId;
    private String content;
    private QuestionType questionType;
    private Integer difficulty;
    private Integer sortNo;
    private UUID answerId;
    private String answer;
    private String answerContent;
    private UUID fileId;
    private String ossKey;
    private String ossType;
    private String ossBucket;
    private List<File> files;
    private List<FileVO> fileVOList;


}
