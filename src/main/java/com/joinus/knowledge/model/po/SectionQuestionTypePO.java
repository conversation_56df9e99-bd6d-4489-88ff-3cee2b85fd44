package com.joinus.knowledge.model.po;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathQuestionType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SectionQuestionTypePO extends MathQuestionType {

     private PublisherType publisher;

     private UUID sectionId;

     private Integer pageIndex;
}
