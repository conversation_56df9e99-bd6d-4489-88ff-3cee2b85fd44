package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "知识点和难度")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KnowledgePointsAndDifficultyVO implements Serializable {
    @Schema(description = "难度", implementation = String.class, example = "中等")
    private String difficulty;
    @Schema(description = "知识点", implementation = List.class)
    private List<MathKnowledgePointVO> knowledgePoints;
}