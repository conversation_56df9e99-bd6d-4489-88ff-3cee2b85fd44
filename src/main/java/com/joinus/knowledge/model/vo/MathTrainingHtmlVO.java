package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * 数学专项训练HTML DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "数学专项训练HTML")
public class MathTrainingHtmlVO {

    @Schema(description = "题目HTML")
    private String questionHtml;

    @Schema(description = "题目答案HTML")
    private String questionWithAnswerHtml;

    @Schema(description = "答案HTML")
    private String answerHtml;

    @Schema(description = "pdfUUID，用户保证html内容与pdf内容保持一致")
    private UUID pdfUUID;

}
