package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@Builder(toBuilder = true)
public class MathTrainingKnowledgePointVO implements Serializable {


    @Schema(description = "知识点ID")
    private UUID knowledgePointId;

    @Schema(description = "知识点名称")
    private String knowledgePointName;

    @Schema(description = "知识点对应的题目ID列表")
    private List<MathPastExamPaperQuestionVO> questions;

    @Schema(description = "年级", implementation = Integer.class, example = "1")
    private Integer grade;

    @Schema(description = "学期", implementation = Integer.class, example = "1")
    private Integer semester;

    @Schema(description = "出版社", implementation = PublisherType.class, example = "1")
    private PublisherType publisher;

    @Schema(description = "章节id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID chapterId;

    @Schema(description = "章节名称", implementation = String.class, example = "1")
    private String chapterName;

    @Schema(description = "章节排序号", implementation = Integer.class, example = "1")
    private Integer chapterSortNo;

    @Schema(description = "章节id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID sectionId;

    @Schema(description = "章节排序号", implementation = Integer.class, example = "1")
    private Integer sectionSortNo;

    @Schema(description = "章节名称", implementation = String.class, example = "1")
    private String sectionName;
}
