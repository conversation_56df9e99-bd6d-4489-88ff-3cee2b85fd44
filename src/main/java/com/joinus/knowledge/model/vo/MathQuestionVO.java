package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.MathQuestionReviewStatus;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.enums.QuestionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 
 * @TableName math_questions
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathQuestionVO implements Serializable {

    @Schema(description = "题目ID")
    private UUID id;
    /**
     * 题目内容
     */
    private String content;

    /**
     * 题目类型
     */
    private QuestionType questionType;

    /**
     * 难度等级
     */
    private Integer difficulty;

    private String source;

    private Boolean existGraphics;

    private Date createdAt;

    private Date updatedAt;

    private List<MathKnowledgePointVO> knowledgePoints;

    private List<MathQuestionTypeVO> questionTypes;

    private List<MathLabelVO> labels;

    private PublisherType publisher;

    private String publisherDescription;

    private Integer grade;

    private Integer semester;

    private Boolean enabled;

    private String knowledgeDomains;

    private Boolean validationSuccess;

    private Boolean validationSuccessEight;

    private Integer validation;

    private Integer validationEight;

    @Schema(description = "是否是历年真题")
    private Boolean isPastExamPaper;

    @Schema(description = "历年真题年份")
    private Integer pastExamPaperYear;

    @Schema(description = "历年真题区域")
    private String pastExamPaperRegion;

    private MathQuestionReviewStatus reviewStatus;

    @Schema(description = "试卷中的排序序号")
    private Integer sortNo;

    private String questionTypeName;

    private List<MathAnswerVO> answers;

    private String sourceName;

    public String getPublisherDescription() {
        return null == publisher ? null : publisher.getValue();
    }

    public Integer getValidation() {
        return null == validationSuccess ? 0 : (validationSuccess ? 1 : 2);
    }

    public Integer getValidationEight() {
        return null == validationSuccessEight ? 0 : (validationSuccessEight ? 1 : 2);
    }

}
