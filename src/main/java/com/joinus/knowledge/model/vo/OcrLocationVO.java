package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "OCR位置响应对象")
public class OcrLocationVO {

    @Schema(description = "表示定位位置的长方形左上顶点的水平坐标", example = "1569")
    private Integer left;

    @Schema(description = "表示定位位置的长方形左上顶点的垂直坐标", example = "333")
    private Integer top;

    @Schema(description = "表示定位位置的长方形的宽度", example = "2310")
    private Integer width;

    @Schema(description = "表示定位位置的长方形的高度", example = "242")
    private Integer height;
}
