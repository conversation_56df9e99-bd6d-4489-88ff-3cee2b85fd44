package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.PublisherType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathChapterVO implements Serializable {

    private Integer grade;

    private PublisherType publisher;

    private Integer semester;

    private UUID id;

    private String name;

    private Integer chapterSortNo;
}
