package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "检测试卷是否存在知识点返回对象")
public class CheckExamExistKnowledgePointsVO implements Serializable {

    @Schema(description = "试卷id", implementation = UUID.class, example = "75244ab6-f70e-451d-aa02-bff12d8fb683")
    private UUID examId;

    @Schema(description = "试卷是否存在知识点", implementation = Boolean.class, example = "true")
    private Boolean exist;


}
