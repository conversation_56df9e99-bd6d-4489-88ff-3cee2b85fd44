package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

@Data
@Schema(description = "数学知识点")
@Builder
public class MathQuestionErrorLabelVO implements Serializable {

    private UUID id;

    private String type;

    private String name;

    private Boolean hasLabel;
}
