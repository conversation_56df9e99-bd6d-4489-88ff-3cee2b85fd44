package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.QuestionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 试卷分页查询返回对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "数学试卷题目")
public class MathExamQuestionVO {

    private QuestionType type;

    private Integer sortNo;

    private List<QuestionDetailVO> questions;

    private String typeName;

}
