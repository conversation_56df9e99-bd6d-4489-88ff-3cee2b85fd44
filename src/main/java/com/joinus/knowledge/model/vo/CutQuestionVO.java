package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.model.dto.CutQuestionSubjectDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Knife4j
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "题目坐标信息响应对象")
public class CutQuestionVO {

    @Schema(description = "处理后图片宽度", example = "1402")
    private Integer width;

    @Schema(description = "处理后图片高度", example = "1672")
    private Integer height;

    @Schema(description = "原始图片高度", example = "1672")
    private Integer orgHeight;

    @Schema(description = "原始图片宽度", example = "1402")
    private Integer orgWidth;
    
    @Schema(description = "题目主体信息列表")
    private List<CutQuestionSubjectDTO> subjects;
}
