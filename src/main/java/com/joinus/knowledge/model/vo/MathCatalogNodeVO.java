package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "题目坐标信息响应对象")
public class MathCatalogNodeVO implements Serializable {

    private UUID id;

    private UUID parentId;

    private String name;

    private Integer level;

    private Boolean isLeaf;

    private List<MathCatalogNodeVO> subNodes;

}
