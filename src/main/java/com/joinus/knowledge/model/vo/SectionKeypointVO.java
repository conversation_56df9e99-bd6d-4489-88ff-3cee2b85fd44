package com.joinus.knowledge.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SectionKeypointVO implements Serializable {


    private UUID chapterId;

    private String chapterName;

    private UUID sectionId;

    private String sectionName;

    private String type;

    private UUID keyPointId;

    private String keyPointName;

    private String keyPointOriginalName;

    private Integer pageIndex;

    private Integer questionCount;

    private Date createdAt;

}
