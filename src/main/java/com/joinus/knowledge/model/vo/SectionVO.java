package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SectionVO implements Serializable {

    @Schema(description = "章节ID", implementation = UUID.class, example = "")
    private UUID chapterId;

    @Schema(description = "章节名称", implementation = String.class, example = "")
    private String chapterName;

    @Schema(description = "章节排序号", implementation = Integer.class, example = "")
    private Integer chapterSortNo;

    @Schema(description = "小节ID", implementation = UUID.class, example = "")
    private UUID sectionId;

    @Schema(description = "小节名称", implementation = String.class, example = "")
    private String sectionName;

    @Schema(description = "小节排序号", implementation = Integer.class, example = "")
    private Integer sectionSortNo;

    @Schema(description = "小节开始页码", implementation = Integer.class, example = "")
    private Integer startPage;

    @Schema(description = "小节结束页码", implementation = Integer.class, example = "")
    private Integer endPage;

    @Schema(description = "小节所属教材ID", implementation = UUID.class, example = "")
    private UUID textbookId;

    @Schema(description = "小节所属出版社", implementation = PublisherType.class, example = "")
    private PublisherType publisher;

    @Schema(description = "小节所属年级", implementation = Integer.class, example = "")
    private Integer grade;

    @Schema(description = "小节所属学期", implementation = Integer.class, example = "")
    private Integer semester;

    private Integer knowledgePointCount;

    private Integer questionTypeCount;
}
