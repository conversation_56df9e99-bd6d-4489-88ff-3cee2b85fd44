package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.dto.CutQuestionSubjectDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "OSS文件对象")
public class OssFileVO implements Serializable {

    @Schema(description = "oss平台枚举", implementation = OssEnum.class, example = "ALIYUN_EDU_KNOWLEDGE_HUB")
    private OssEnum ossEnum;

    @Schema(description = "oss平台类型", implementation = String.class, example = "aliyun")
    private String ossType;

    @Schema(description = "oss平台bucket", implementation = String.class, example = "edu-knowledge-hub")
    private String ossBucket;

    @Schema(description = "oss文件key", implementation = String.class, example = "uat/question/20250319/aaaa.png")
    private String key;

    @Schema(description = "oss文件预览url", implementation = String.class, example = "https://edu-knowledge-hub.oss-cn-beijing.aliyuncs.com/uat/question/20250319/aaaa.png")
    private String presignedUrl;

    public String getOssType() {
        return null != ossEnum ? ossEnum.getType() : ossType;
    }

    public String getOssBucket() {
        return null != ossEnum ? ossEnum.getBucket() : ossBucket;
    }

}
