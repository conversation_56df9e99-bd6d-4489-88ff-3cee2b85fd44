package com.joinus.knowledge.model.vo;

import cn.hutool.core.bean.BeanUtil;
import com.joinus.knowledge.enums.ExamSourceType;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.enums.RegularExamType;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathExamQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.UUID;

/**
 * 试卷分页查询返回对象
 */
@Data
@Schema(description = "数学试卷分页查询结果")
public class MathExamVO {
    @Schema(description = "试卷ID", example = "123456")
    private UUID id;
    @Schema(description = "试卷名称", example = "期末考试")
    private String name;
    @Schema(description = "状态", example = "TODO")
    private ExamStateEnum state;
    @Schema(description = "教材版本", example = "人教版")
    private PublisherType publisher;
    @Schema(description = "年级", example = "8")
    private Integer grade;
    @Schema(description = "学期", example = "2")
    private Integer semester;
    @Schema(description = "创建时间", example = "2022-01-01 12:00:00")
    private String createdAt;
    @Schema(description = "更新时间", example = "2022-01-01 12:00:00")
    private String updatedAt;
    @Schema(description = "试卷图片列表")
    private List<FileVO> images;
    @Schema(description = "试卷原图图片列表")
    private List<FileVO> originalImages;
    @Schema(description = "试卷抹除笔记图片列表")
    private List<FileVO> handwritingRemovedImages;
    @Schema(description = "年份")
    private Integer year;
    @Schema(description = "地区")
    private String regionPath;
    @Schema(description = "试题数量")
    private Integer questionCount;
    @Schema(description = "已生成知识点的教材版本")
    private List<PublisherType> publisherTypes;
    @Schema(description = "来源")
    private ExamSourceType source;
    @Schema(description = "试题列表")
    private List<MathExamQuestion> examQuestions;
    @Schema(description = "地区")
    private String region;
    private Object examSectionDescriptions;
    private String sourceName;
    private String stateName;
    /**
     * 别名列表，第一个是主别名
     */
    private List<String> aliases;

    /**
     * 学校id列表
     */
    private List<SchoolVO> schools;

    /*
     * 名校卷值
     */
    private List<String> eliteSchoolExamPaperValues;

    private RegularExamType regularExamType;

    public static MathExamVO ofMathExam(MathExam mathExam) {
        return BeanUtil.copyProperties(mathExam, MathExamVO.class);
    }
}
