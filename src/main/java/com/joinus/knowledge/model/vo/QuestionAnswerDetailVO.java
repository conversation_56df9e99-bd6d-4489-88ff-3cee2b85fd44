package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

@Data
@Schema(description = "题目答案详情")
public class QuestionAnswerDetailVO implements Serializable {

    @Schema(description = "答案ID")
    private UUID id;

    @Schema(description = "答案内容")
    private String answer;

    @Schema(description = "解析内容")
    private String content;


}
