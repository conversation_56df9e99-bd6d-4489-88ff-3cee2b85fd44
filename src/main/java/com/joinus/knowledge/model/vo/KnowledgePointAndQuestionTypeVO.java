package com.joinus.knowledge.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KnowledgePointAndQuestionTypeVO implements Serializable {

        private List<MathKnowledgePointVO> knowledgePoints;

        private List<MathQuestionTypeVO> questionTypes;

        private List<MathKnowledgePointVO> examPoints;

}
