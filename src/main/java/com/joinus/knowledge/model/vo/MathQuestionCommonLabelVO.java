package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.enums.QuestionSourceType;
import com.joinus.knowledge.model.entity.MathLabel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class MathQuestionCommonLabelVO implements Serializable {
    @Schema(description = "题目来源", example = "AI")
    private QuestionSourceType questionSource;

    private List<String> labels;
}
