package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.model.dto.SchoolExamDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchoolVO implements Serializable {

    @Schema(description = "学校Id")
    private Long id;

    @Schema(description = "学校名称")
    private String schoolName;

    public static SchoolVO ofSchoolDTO(SchoolExamDTO school) {
        return SchoolVO.builder()
                .id(school.getSchoolId())
                .schoolName(school.getSchoolName())
                .build();
    }
}
