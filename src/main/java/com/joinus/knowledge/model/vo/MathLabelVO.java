package com.joinus.knowledge.model.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.joinus.knowledge.enums.MathLabelTypeEnum;
import com.joinus.knowledge.model.entity.MathLabel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathLabelVO implements Serializable {

    private UUID id;

    private String name;

    private String type;

    private String typeDescription;

    public String getTypeDescription() {
        return StrUtil.isBlank(type) ? "" : MathLabelTypeEnum.ofType(type).getDescription();
    }

    public static MathLabelVO ofLabel(MathLabel label) {
        return null == label ? null : BeanUtil.copyProperties(label, MathLabelVO.class);
    }
}
