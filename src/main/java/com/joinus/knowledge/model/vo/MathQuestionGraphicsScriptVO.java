package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.MathGraphicsScriptStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
public class MathQuestionGraphicsScriptVO extends MathQuestionVO {

    private String username;

    private Date completedAt;

    private UUID baseQuestionId;

    private MathGraphicsScriptStatusEnum status;

    private String remark;
}
