package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathPastExamPaperQuestionVO implements Serializable {

    @Schema(description = "题目ID")
    private UUID id;
    @Schema(description = "是否是真题")
    private Boolean isPastExamPaper;
    @Schema(description = "真题年份")
    private Integer pastExamPaperYear;
    @Schema(description = "真题区域")
    private String pastExamPaperRegion;
    @Schema(description = "试卷题目序号")
    private Integer sortNo;
}
