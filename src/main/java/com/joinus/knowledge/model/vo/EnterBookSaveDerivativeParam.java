package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.OssEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnterBookSaveDerivativeParam implements Serializable {

    /**
     *
     */
    private UUID fileId;

    /**
     * 衍生类型，如original, thumbnail, preview, ocr
     */
    private String derivativeType;

    /**
     * 存储路径
     */
    private String storagePath;

    /**
     * 文件格式
     */
    private String format;

    /**
     * 图像宽度
     */
    private Integer width;

    /**
     * 图像高度
     */
    private Integer height;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文本内容
     */
    private String textContent;

}
