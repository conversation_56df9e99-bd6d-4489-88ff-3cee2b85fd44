package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
public class ReadingPassageQuestionsVO {
    @Schema(description = "题目id")
    private UUID id;
    /**
     * 题型
     */
    @Schema(description = "题型")
    private String questionType;
    /**
     * 题目内容
     */
    @Schema(description = "题目内容")
    private String content;
    /**
     * 题目排序
     */
    @Schema(description = "题目排序")
    private Integer orderNo;

    @Schema(description = "选项原始字符串", hidden = true)
    private String optionStr;

    @Schema(description = "选项列表")
    private List<String> options;

    @Schema(description = "答案列表")
    private List<ReadingQuestionAnswersVO> answers;
}