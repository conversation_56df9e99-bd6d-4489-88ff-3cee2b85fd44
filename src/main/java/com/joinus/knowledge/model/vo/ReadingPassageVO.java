package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.UUID;

/**
 * 阅读题试卷DTO
 *
 * <AUTHOR>
 * @date 2025/3/31 16:20
 */
@Data
@Schema(description = "阅读题文章题目响应")
public class ReadingPassageVO {
    @Schema(description = "文章id")
    private UUID id;
    /**
     * 文体类型
     */
    @Schema(description = "文体类型")
    private String genre;

    @Schema(description = "文体类型 中文")
    private String genreName;

    /**
     * 文章标题
     */
    @Schema(description = "文章标题")
    private String title;

    /**
     * 正文内容
     */
    @Schema(description = "正文内容")
    private String content;

    @Schema(description = "题目列表")
    private List<ReadingPassageQuestionsVO> questions;
}
