package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "知识点和章节列表")
public class SectionKnowledgePointVO implements Serializable {


    @Schema(description = "章节ID")
    private UUID chapterId;

    @Schema(description = "章节名称")
    private String chapterName;

    @Schema(description = "小节ID")
    private UUID sectionId;

    @Schema(description = "小节名称")
    private String sectionName;

    @Schema(description = "知识点ID")
    private UUID knowledgePointId;

    @Schema(description = "知识点名称")
    private String knowledgePointName;

    @Schema(description = "章节排序")
    private Integer chapterSortNo;

    @Schema(description = "小节排序")
    private Integer sectionSortNo;

    @Schema(description = "年级")
    private Integer grade;

    @Schema(description = "学期 1上学期 2下学期")
    private Integer semester;

    @Schema(description = "出版社类型")
    private PublisherType  publisher;

    @Schema(description = "教材ID")
    private UUID textbookId;
}
