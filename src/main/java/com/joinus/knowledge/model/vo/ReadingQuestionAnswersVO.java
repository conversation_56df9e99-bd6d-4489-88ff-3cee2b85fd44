package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.UUID;

@Data
public class ReadingQuestionAnswersVO {
    @Schema(description = "答案id")
    private UUID id;
    /**
     * 关联题目ID
     */
    @Schema(description = "关联题目ID")
    private String questionId;
    /**
     * 答案
     */
    @Schema(description = "答案")
    private String answer;
    /**
     * 答案解析
     */
    @Schema(description = "答案解析")
    private String content;
}