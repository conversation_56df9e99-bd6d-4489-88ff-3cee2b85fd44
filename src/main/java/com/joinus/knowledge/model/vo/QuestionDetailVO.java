package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.MathQuestionReviewStatus;
import com.joinus.knowledge.enums.QuestionSourceType;
import com.joinus.knowledge.enums.QuestionType;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.entity.MathLabel;
import com.joinus.knowledge.model.entity.MathQuestionReviewRecords;
import com.joinus.knowledge.model.entity.MathQuestionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "题目详情")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionDetailVO implements Serializable {

    @Schema(description = "题目ID")
    private UUID id;

    @Schema(description = "题目内容")
    private String content;

    @Schema(description = "题目类型", implementation = QuestionType.class)
    private QuestionType questionType;

    private String questionTypeName;

    @Schema(description = "题目难度", implementation = Integer.class)
    private Integer difficulty;

    @Schema(description = "题目答案列表")
    private List<QuestionAnswerDetailVO> answers;

    @Schema(description = "题目文件列表")
    private List<FileVO> files;

    @Schema(description = "题目原图文件列表")
    private List<FileVO> originalFiles;

    @Schema(description = "题目附图文件列表")
    private List<FileVO> derivativeFiles;

    @Schema(description = "题目来源")
    private QuestionSourceType source;

    private String sourceName;

    @Schema(description = "题目序号")
    private Integer sortNo;

    @Schema(description = "题目是否启用")
    private Boolean enabled;

    @Schema(description = "题目达标情况")
    private Integer validation;

    @Schema(description = "题目是否达标")
    private Boolean validationSuccess;

    @Schema(description = "题目达标情况(8维度)")
    private Integer validationEight;

    @Schema(description = "题目是否达标(维度)")
    private Boolean validationSuccessEight;

    private Date createdAt;

    private Date updatedAt;

    private Integer pastExamPaperYear;

    private String pastExamPaperRegion;

    private UUID examId;

    private String examName;

    private Integer sortNoInExam;

    private List<MathLabel> labels;

    /*
     * 是否包含图片
     */
    private Boolean existGraphics;

    private List<MathKnowledgePointVO> knowledgePoints;

    private List<MathQuestionTypeVO> questionTypes;

    public String getQuestionTypeName() {
        return null == questionType ? null : questionType.getType();
    }

    public String getSourceName() {
        return null == source ? null : source.getDescription();
    }

    public Integer getValidation() {
        return null == validationSuccess ? 0 : (validationSuccess ? 1 : 2);
    }

    public Integer getValidationEight() {
        return null == validationSuccessEight ? 0 : (validationSuccessEight ? 1 : 2);
    }

    public MathQuestionReviewStatus reviewStatus;

    public List<MathQuestionReviewRecords> reviewRecords;

}
