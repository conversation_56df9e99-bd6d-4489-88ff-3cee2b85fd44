package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.OssEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QuerySectionVideoVO implements Serializable {

    private UUID sectionId;

    private String name;

    private OssEnum ossEnum;

    private String ossKey;

    private Integer sortNo;

}
