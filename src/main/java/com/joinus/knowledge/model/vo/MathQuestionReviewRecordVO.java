package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.MathGraphicsScriptStatusEnum;
import com.joinus.knowledge.enums.MathQuestionReviewType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
public class MathQuestionReviewRecordVO extends MathQuestionVO {

    private String username;

    private Date reviewedAt;

    private UUID baseQuestionId;

    private String status;

    private MathQuestionReviewType reviewType;

    private String remark;
}
