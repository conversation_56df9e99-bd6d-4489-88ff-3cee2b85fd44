package com.joinus.knowledge.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathAnswerVO implements Serializable {

    private UUID id;

    private String content;

    private String answer;

    private Date createdAt;

    private List<FileVO> files;

    private List<FileVO> originalFiles;

    private List<FileVO> derivativeFiles;

    private List<FileVO> analysisFiles;
}
