package com.joinus.knowledge.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoordinatePoint implements Serializable {


    private Double x;

    private Double y;

    /*
     * 1 左上 2 右上 3 右下 4 左下
     */
    private Integer position;


}
