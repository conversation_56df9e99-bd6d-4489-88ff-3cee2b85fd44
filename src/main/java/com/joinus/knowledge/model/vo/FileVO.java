package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.ExamFileType;
import com.joinus.knowledge.enums.ExamSourceType;
import com.joinus.knowledge.enums.ExamFileType;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.entity.File;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileVO implements Serializable {

    @Schema(description = "文件ID")
    private UUID id;

    @Schema(description = "文件名称")
    private String name;

    @Schema(description = "文件oss平台")
    private OssEnum ossEnum;

    @Schema(description = "文件ossBucket")
    private String ossBucket;

    @Schema(description = "文件ossType")
    private String ossType;

    @Schema(description = "文件ossKey")
    private String ossKey;

    @Schema(description = "文件ossUrl")
    private String ossUrl;

    @Schema(description = "文件ocrHtml")
    private String ocrHtml;

    @Schema(description = "文件sortNo")
    private Integer sortNo;

    @Schema(description = "文件type, 0附图，1原图, 2解析图")
    private Integer type;

    @Schema(description = "试卷图片文件类型")
    private ExamFileType examFileType;
}
