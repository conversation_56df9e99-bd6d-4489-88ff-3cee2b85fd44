package com.joinus.knowledge.model.vo;

import com.aliyun.tea.NameInMap;
import com.joinus.knowledge.enums.OssEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Tag(name = "OssAliTokenVO", description = "阿里云OSS临时凭证")
public class OssAliTokenVO implements Serializable {

    @Schema(description = "bucket名称", example = "edu-knowledge-hub")
    private String bucket;

    @Schema(description = "region名称", example = "oss-cn-beijing")
    private String region;

    @Schema(description = "accessKeyId", example = "****")
    private String accessKeyId;

    @Schema(description = "accessKeySecret", example = "****")
    private String accessKeySecret;

    @Schema(description = "有效期截止时间", example = "2025-03-19T08:24:02Z")
    public String expiration;

    @Schema(description = "securityToken", example = "****")
    public String securityToken;

    @Schema(description = "endpoint", example = "https://oss-edu-knowledge-hub.qingyulan.net")
    private String endpoint;
}
