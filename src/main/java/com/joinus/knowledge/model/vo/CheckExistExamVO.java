package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.ExamSourceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "检测题目是否存在图形接口返回对象")
public class CheckExistExamVO implements Serializable {

    @Schema(description = "试卷id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0a0-d0a0-d0a0d0a0d0a0")
    private UUID examId;

    @Schema(description = "试卷名称", implementation = String.class, example = "试卷名称")
    private String examName;

    @Schema(description = "是否存在试卷", implementation = Boolean.class, example = "true")
    private Boolean exist;

    @Schema(description = "存在试卷的id列表", implementation = List.class)
    private List<UUID> examIds;

    @Schema(description = "试卷来源", implementation = ExamSourceType.class)
    private ExamSourceType examSource;
}
