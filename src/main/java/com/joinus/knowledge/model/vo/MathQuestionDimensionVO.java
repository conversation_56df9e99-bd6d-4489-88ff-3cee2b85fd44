package com.joinus.knowledge.model.vo;

import cn.hutool.core.util.StrUtil;
import com.joinus.knowledge.enums.MathQuestionDimensionOptionType;
import com.joinus.knowledge.enums.MathQuestionDimensionType;
import com.joinus.knowledge.model.dto.CutQuestionSubjectDTO;
import com.joinus.knowledge.model.entity.MathQuestionDimension;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Knife4j
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "题目坐标信息响应对象")
public class MathQuestionDimensionVO {

    private MathQuestionDimensionType type;

    private String typeDescription;

    private MathQuestionDimensionOptionType option;

    private String optionDescription;

    private String reason;

    public static List<MathQuestionDimensionVO> of(MathQuestionDimension questionDimension) {
        List<MathQuestionDimensionVO> questionDimensionVOS = new ArrayList<>();

        for (MathQuestionDimensionType type : MathQuestionDimensionType.values()) {
            MathQuestionDimensionVO defaultQuestionDimensionVO = MathQuestionDimensionVO.builder()
                    .type(type)
                    .option(MathQuestionDimensionOptionType.NULL)
                    .build();
            questionDimensionVOS.add(defaultQuestionDimensionVO);
        }

        if (null == questionDimension) {
            return questionDimensionVOS;
        }

        if (StrUtil.isNotBlank(questionDimension.getProblemTextResult())) {
            questionDimensionVOS.stream()
                    .filter(dimention -> dimention.getType() == MathQuestionDimensionType.PROBLEM_TEXT)
                    .forEach(dimention -> {
                        dimention.setOption(MathQuestionDimensionOptionType.ofDescription(questionDimension.getProblemTextResult()));
                        dimention.setReason(questionDimension.getProblemTextReason());
                    });
        }
        if (StrUtil.isNotBlank(questionDimension.getVisualElementsResult())) {
            questionDimensionVOS.stream()
                    .filter(dimention -> dimention.getType() == MathQuestionDimensionType.VISUAL_ELEMENTS)
                    .forEach(dimention -> {
                        dimention.setOption(MathQuestionDimensionOptionType.ofDescription(questionDimension.getVisualElementsResult()));
                        dimention.setReason(questionDimension.getVisualElementsReason());
                    });
        }
        if (StrUtil.isNotBlank(questionDimension.getFormatAndTypeResult())) {
            questionDimensionVOS.stream()
                    .filter(dimention -> dimention.getType() == MathQuestionDimensionType.FORMAT_AND_TYPE)
                    .forEach(dimention -> {
                        dimention.setOption(MathQuestionDimensionOptionType.ofDescription(questionDimension.getFormatAndTypeResult()));
                        dimention.setReason(questionDimension.getFormatAndTypeReason());
                    });
        }
        if (StrUtil.isNotBlank(questionDimension.getCoreKnowledgePointsResult())) {
            questionDimensionVOS.stream()
                    .filter(dimention -> dimention.getType() == MathQuestionDimensionType.CORE_KNOWLEDGE_POINTS)
                    .forEach(dimention -> {
                        dimention.setOption(MathQuestionDimensionOptionType.ofDescription(questionDimension.getCoreKnowledgePointsResult()));
                        dimention.setReason(questionDimension.getCoreKnowledgePointsReason());
                    });
        }
        if (StrUtil.isNotBlank(questionDimension.getPrimarySolutionMethodResult())) {
            questionDimensionVOS.stream()
                    .filter(dimention -> dimention.getType() == MathQuestionDimensionType.PRIMARY_SOLUTION_METHOD)
                    .forEach(dimention -> {
                        dimention.setOption(MathQuestionDimensionOptionType.ofDescription(questionDimension.getPrimarySolutionMethodResult()));
                        dimention.setReason(questionDimension.getPrimarySolutionMethodReason());
                    });
        }

        if (StrUtil.isNotBlank(questionDimension.getSolutionLogicalStructureResult())) {
            questionDimensionVOS.stream()
                    .filter(dimention -> dimention.getType() == MathQuestionDimensionType.SOLUTION_LOGICAL_STRUCTURE)
                    .forEach(dimention -> {
                        dimention.setOption(MathQuestionDimensionOptionType.ofDescription(questionDimension.getSolutionLogicalStructureResult()));
                        dimention.setReason(questionDimension.getSolutionLogicalStructureReason());
                    });
        }
        if (StrUtil.isNotBlank(questionDimension.getCognitiveLoadAndDifficultyLevelResult())) {
            questionDimensionVOS.stream()
                    .filter(dimention -> dimention.getType() == MathQuestionDimensionType.COGNITIVE_LOAD_AND_DIFFICULTY_LEVEL)
                    .forEach(dimention -> {
                        dimention.setOption(MathQuestionDimensionOptionType.ofDescription(questionDimension.getCognitiveLoadAndDifficultyLevelResult()));
                        dimention.setReason(questionDimension.getCognitiveLoadAndDifficultyLevelReason());
                    });
        }
        if (StrUtil.isNotBlank(questionDimension.getMathematicalRigorAndCorrectnessResult())) {
            questionDimensionVOS.stream()
                    .filter(dimention -> dimention.getType() == MathQuestionDimensionType.MATHEMATICAL_RIGOR_AND_CORRECTNESS)
                    .forEach(dimention -> {
                        dimention.setOption(MathQuestionDimensionOptionType.ofDescription(questionDimension.getMathematicalRigorAndCorrectnessResult()));
                    });
        }

        return questionDimensionVOS;
    }

    public String getTypeDescription() {
        return null == type ? null : type.getDescription();
    }

    public String getOptionDescription() {
        return null == option ? null : option.getDescription();
    }
}
