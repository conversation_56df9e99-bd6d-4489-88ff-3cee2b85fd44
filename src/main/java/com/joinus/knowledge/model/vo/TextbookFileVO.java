package com.joinus.knowledge.model.vo;

import cn.hutool.core.util.StrUtil;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.model.dto.TextbookFileDTO;
import com.joinus.knowledge.utils.MinioUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

@Data
public class TextbookFileVO implements Serializable {

    private UUID id;

    private Integer pageNo;

    private String pngOssUrl;

    private String jpgOssUrl;

    private String previewOssUrl;

    private String thumbnailOssUrl;

    private String ocrHtml;

    private Integer jpgWidth;

    private Integer jpgHeight;

    public static TextbookFileVO ofTextbookFileDTO(TextbookFileDTO dto, MinioUtils minioUtils) {
        TextbookFileVO textbookFileVO = new TextbookFileVO();
        textbookFileVO.setId(dto.getFileId());
        textbookFileVO.setPageNo(dto.getPageNo());
        textbookFileVO.setOcrHtml(dto.getOcrHtml());
        textbookFileVO.setJpgWidth(dto.getJpgWidth());
        textbookFileVO.setJpgHeight(dto.getJpgHeight());
        if (StrUtil.isNotBlank(dto.getPngOssKey())) {
            textbookFileVO.setPngOssUrl(minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, dto.getPngOssKey()));
        }
        if (StrUtil.isNotBlank(dto.getJpgOssKey())) {
            textbookFileVO.setJpgOssUrl(minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, dto.getJpgOssKey()));
        }
        if (StrUtil.isNotBlank(dto.getPreviewOssKey())) {
            textbookFileVO.setPreviewOssUrl(minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, dto.getPreviewOssKey()));
        }
        if (StrUtil.isNotBlank(dto.getThumbnailOssKey())) {
            textbookFileVO.setThumbnailOssUrl(minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, dto.getThumbnailOssKey()));
        }
        //根据key获取oss临时链接
        return textbookFileVO;
    }
}
