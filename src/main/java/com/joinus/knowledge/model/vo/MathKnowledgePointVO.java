package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数学知识点列表")
public class MathKnowledgePointVO implements Serializable {

    @Schema(description = "知识点id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID id;

    @Schema(description = "知识点名称", implementation = String.class, example = "1")
    private String name;

    @Schema(description = "年级", implementation = Integer.class, example = "1")
    private Integer grade;

    @Schema(description = "学期", implementation = Integer.class, example = "1")
    private Integer semester;

    @Schema(description = "出版社", implementation = PublisherType.class, example = "1")
    private PublisherType publisher;

    @Schema(description = "出版社描述", implementation = String.class, example = "1")
    private String publisherDescription;

    @Schema(description = "章id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID chapterId;

    @Schema(description = "章名称", implementation = String.class, example = "1")
    private String chapterName;

    @Schema(description = "章排序号", implementation = Integer.class, example = "1")
    private Integer chapterSortNo;

    @Schema(description = "节id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID sectionId;

    @Schema(description = "节排序号", implementation = Integer.class, example = "1")
    private Integer sectionSortNo;

    @Schema(description = "节名称", implementation = String.class, example = "1")
    private String sectionName;

    @Schema(description = "页码", implementation = Integer.class, example = "1")
    private Integer pageIndex;

    @Schema(description = "上架的ai题目数量", implementation = Integer.class, example = "1")
    private Integer enableAiQuestionCount;

    @Schema(description = "教材id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID textbookId;

    public String getPublisherDescription() {
        return null == publisher ? null : publisher.getValue();
    }



}
