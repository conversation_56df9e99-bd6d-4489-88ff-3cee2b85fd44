package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
/**
 * 语文阅读-单元
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "reading_units")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReadingUnits extends BaseEntity {

    /**
     * 年级（1-一年级，以此类推）
     */
    private Integer grade;

    /**
     * 学期（1-上学期，2-下学期）
     */
    private Integer semester;

    /**
     * 单元名称
     */
    private String name;

    /**
     * 排序（保留列名映射）
     */
    private Integer orderNo;

    /**
     * 大纲
     */
    private String outline;

    /**
     * 教材
     */
    private String textbook;

    private String words;
}