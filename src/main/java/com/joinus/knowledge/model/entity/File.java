package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 
 * @TableName files
 */
@TableName(value ="files")
@Data
@EqualsAndHashCode(callSuper=true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class File extends BaseEntity {

    /**
     * 
     */
    private String name;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 文件媒体类型
     */
    private String mimeType;

    /**
     * oss路径
     */
    private String ossUrl;

    private String ocrHtml;

    private String ossType;

    private String ossBucket;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(typeHandler = JsonbTypeHandler.class)
    private String positions;

}