package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.util.UUID;

import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import com.joinus.knowledge.config.typehandler.AiModelTypeHandler;
import com.joinus.knowledge.enums.PgAIModelType;
import lombok.Builder;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * @TableName math_question_relationships
 */
@TableName(value ="math_question_relationships")
@Data
@Builder
public class MathQuestionRelationships {
    @TableId(type = IdType.AUTO)
    private Long id;

    private UUID baseQuestionId;

    private UUID derivedQuestionId;

    @TableField(typeHandler = AiModelTypeHandler.class, jdbcType = JdbcType.VARCHAR)
    private PgAIModelType aiModel;

    @TableField(typeHandler = JsonbTypeHandler.class)
    private String generationParameters;

    private Date createdAt;

    private Integer hierarchyLevel;
}