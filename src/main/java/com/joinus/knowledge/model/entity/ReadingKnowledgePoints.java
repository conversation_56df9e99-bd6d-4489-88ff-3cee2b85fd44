package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("reading_knowledge_points")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReadingKnowledgePoints extends BaseEntity {

    /**
     * 知识点名称
     */
    private String name;

    /**
     * 知识点描述
     */
    private String content;
}