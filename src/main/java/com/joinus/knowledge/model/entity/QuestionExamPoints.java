package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.UUID;
import lombok.Data;

/**
 * 题目与考点的关联关系表
 * @TableName question_exam_points
 */
@Data
@TableName(value ="question_exam_points")
public class QuestionExamPoints implements Serializable {
    /**
     * 题目ID
     */
    private UUID questionId;

    /**
     * 考点ID
     */
    private UUID examPointId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 获取题目ID
     */
    public UUID getQuestionId() {
        return questionId;
    }

    /**
     * 设置题目ID
     */
    public void setQuestionId(UUID questionId) {
        this.questionId = questionId;
    }

    /**
     * 获取考点ID
     */
    public UUID getExamPointId() {
        return examPointId;
    }

    /**
     * 设置考点ID
     */
    public void setExamPointId(UUID examPointId) {
        this.examPointId = examPointId;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        QuestionExamPoints other = (QuestionExamPoints) that;
        return (this.getQuestionId() == null ? other.getQuestionId() == null : this.getQuestionId().equals(other.getQuestionId()))
            && (this.getExamPointId() == null ? other.getExamPointId() == null : this.getExamPointId().equals(other.getExamPointId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getQuestionId() == null) ? 0 : getQuestionId().hashCode());
        result = prime * result + ((getExamPointId() == null) ? 0 : getExamPointId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", questionId=").append(questionId);
        sb.append(", examPointId=").append(examPointId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}