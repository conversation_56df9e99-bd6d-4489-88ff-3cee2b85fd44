package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.enums.MathQuestionReviewStatus;
import com.joinus.knowledge.enums.QuestionSourceType;
import com.joinus.knowledge.enums.QuestionType;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

/**
 * 
 * @TableName math_questions
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value ="math_questions")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class MathQuestion extends BaseEntity {
    /**
     * 题目内容
     */
    private String content;

    /**
     * 题目类型
     */
    private QuestionType questionType;

    /**
     * 难度等级
     */
    private Integer difficulty;

    private QuestionSourceType source;

    /*
     * 是否包含图片
     */
    private Boolean existGraphics;

    /*
     * 是否启用
     */
    private Boolean enabled;

    /*
     * 审核状态
     */
    private MathQuestionReviewStatus reviewStatus;
}
