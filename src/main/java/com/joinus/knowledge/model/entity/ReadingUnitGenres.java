package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Data
@TableName("reading_unit_genres")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReadingUnitGenres {
    /**
     * 关联单元ID
     */
    private UUID unitId;

    /**
     * 文体类型
     */
    private String genre;

    /**
     * 权重
     */
    private Integer weight;

    private Integer minWordNum;

    private Integer maxWordNum;

    /**
     * 知识点和文体的对应关系
     */
    private String knowledgePointQuestionType;
}