package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

/**
 * 章节与知识点的关联关系表
 * @TableName chapter_knowledge_points
 */
@Data
@TableName(value ="chapter_knowledge_points")
public class ChapterKnowledgePoints implements Serializable {
    /**
     * 章节ID
     */
    private UUID chapterId;

    /**
     * 知识点ID
     */
    private UUID knowledgePointId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public UUID getChapterId() {
        return chapterId;
    }

    /**
     * 
     */
    public void setChapterId(UUID chapterId) {
        this.chapterId = chapterId;
    }

    /**
     * 
     */
    public UUID getKnowledgePointId() {
        return knowledgePointId;
    }

    /**
     * 
     */
    public void setKnowledgePointId(UUID knowledgePointId) {
        this.knowledgePointId = knowledgePointId;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ChapterKnowledgePoints other = (ChapterKnowledgePoints) that;
        return (this.getChapterId() == null ? other.getChapterId() == null : this.getChapterId().equals(other.getChapterId()))
            && (this.getKnowledgePointId() == null ? other.getKnowledgePointId() == null : this.getKnowledgePointId().equals(other.getKnowledgePointId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getChapterId() == null) ? 0 : getChapterId().hashCode());
        result = prime * result + ((getKnowledgePointId() == null) ? 0 : getKnowledgePointId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", chapterId=").append(chapterId);
        sb.append(", knowledgePointId=").append(knowledgePointId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}