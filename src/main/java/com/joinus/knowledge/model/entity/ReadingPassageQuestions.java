package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("reading_passage_questions")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReadingPassageQuestions extends BaseEntity {

    /**
     * 关联文章ID
     */
    @TableField("passage_id")
    private UUID passageId;

    /**
     * 题型
     */
    @TableField("question_type")
    private String questionType;

    /**
     * 题目内容
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String content;

    /**
     * 难度系数
     */
    private Integer difficulty;

    /**
     * 题目排序
     */
    private Integer orderNo;

    /**
     * 题目来源
     */
    private String source;

    /**
     * 是否启用：1、启用，0、禁用
     */
    private Integer isEnabled;

    /**
     * 审核状态：0-未审核 1-审核通过 2-审核不通过
     */
    @TableField("is_audit")
    private Integer isAudit;
}