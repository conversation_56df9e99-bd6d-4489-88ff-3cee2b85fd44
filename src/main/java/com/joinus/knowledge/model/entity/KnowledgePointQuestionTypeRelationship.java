package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@TableName(value ="math_knowledge_point_question_types")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class KnowledgePointQuestionTypeRelationship implements Serializable {


    /**
     * 知识点ID
     */
    private UUID knowledgePointId;

    /**
     * 题型ID
     */
    private UUID questionTypeId;
}