package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.enums.ExamFileType;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

/**
 * @TableName math_exam_files
 */
@TableName(value ="math_exam_files")
@Data
public class MathExamFiles implements Serializable {
    private UUID examId;

    private UUID fileId;

    private Integer sortNo;

    private ExamFileType type;
}