package com.joinus.knowledge.model.entity;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import com.joinus.knowledge.enums.ExamTagType;
import com.joinus.knowledge.model.param.AddExamTagParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 试卷标签表
 * @TableName math_exam_tags
 */
@TableName(value ="math_exam_tags")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathExamTag implements Serializable {
    /**
     * 试卷id
     */
    private UUID examId;

    /**
     * 标签类型 例如： ALIAS
     */
    private ExamTagType type;

    /**
     * 标签的具体内容
     */
    private String value;

    /**
     * 此标签的额外信息 {"isPrimary": true}
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String properties;

    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    @TableLogic(value = "null", delval = "now()")
    private Date deletedAt;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MathExamTag that = (MathExamTag) o;
        return Objects.equals(examId, that.examId) &&
               type == that.type && 
               Objects.equals(value, that.value) && 
               (properties == that.properties || 
                (properties != null && that.properties != null && properties.equals(that.properties)));
    }

    @Override
    public int hashCode() {
        return Objects.hash(examId, type, value, 
                          properties != null ? properties.toString() : null);
    }


}