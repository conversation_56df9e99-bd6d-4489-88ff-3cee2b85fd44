package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import lombok.Builder;
import lombok.Data;

/**
 * @TableName prompt_test_logs
 */
@TableName(value ="prompt_test_logs")
@Data
@Builder
public class PromptTestLog {
    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField(typeHandler = JsonbTypeHandler.class)
    private String param;

    private String result;

    private Date createdAt;
}