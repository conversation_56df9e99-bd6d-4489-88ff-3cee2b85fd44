package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.UUID;

import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.config.typehandler.LtreeTypeHandler;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.JdbcType;

/**
 * @TableName math_catalog_nodes
 */
@TableName(value ="math_catalog_nodes")
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MathCatalogNode extends BaseEntity {

    private String name;

    private Integer sortNo;

    private UUID parentId;

    @TableField(typeHandler = LtreeTypeHandler.class, jdbcType = JdbcType.OTHER)
    private String idPath;

    private Integer level;

    private UUID textbookId;

    private Integer startPage;

    private Integer endPage;
}