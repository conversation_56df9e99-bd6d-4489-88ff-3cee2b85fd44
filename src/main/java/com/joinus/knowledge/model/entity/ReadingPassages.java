package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("reading_passages")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReadingPassages extends BaseEntity {

    /**
     * 关联单元ID
     */
    @TableField("unit_id")
    private UUID unitId;

    /**
     * 文体类型
     */
    private String genre;

    /**
     * 文章标题
     */
    private String title;

    /**
     * 正文内容
     */
    private String content;

    /**
     * 难度系数
     */
    private Integer difficulty;

    /**
     * 文章来源
     */
    private String source;

    /**
     * 提示模板ID
     */
    @TableField("prompt_template_id")
    private UUID promptTemplateId;

    /**
     * 是否启用 1:是 0:否
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 审核状态：0-未审核 1-审核通过 2-审核不通过
     */
    @TableField("is_audit")
    private Integer isAudit;

    private UUID basePassageId;
}