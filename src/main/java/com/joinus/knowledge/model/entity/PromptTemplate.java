package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import lombok.Data;

/**
 * @TableName prompt_templates
 */
@TableName(value ="prompt_templates")
@Data
public class PromptTemplate {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String name;

    private String content;

    private Date createdAt;

    private Date updatedAt;

    private Date deletedAt;

    private String description;

    private String systemPrompt;

    @TableField(typeHandler = JsonbTypeHandler.class)
    private String structuredOutputJsonSchema;
}