/**
 * <AUTHOR>
 * @Date 2025/3/31 16:48
 */
package com.joinus.knowledge.model.entity;

import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;

import java.util.ArrayList;
import java.util.List;

public class Conversation {
    private final List<Message> messages = new ArrayList<>();

    public void addUserMessage(String content) {
        messages.add(new UserMessage(content));
    }

    public void addSystemMessage(String content) {
        messages.add(new SystemMessage(content));
    }

    public List<Message> getHistory() {
        return new ArrayList<>(messages);
    }
}
