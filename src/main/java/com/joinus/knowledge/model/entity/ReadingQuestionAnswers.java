package com.joinus.knowledge.model.entity;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("reading_question_answers")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReadingQuestionAnswers extends BaseEntity {

    /**
     * 关联题目ID
     */
    private UUID questionId;

    /**
     * 答案
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String answer;

    /**
     * 答案解析
     */
    private String content;

    /**
     * 对应的答题公式ID
     */
    private UUID answeringFormulaId;
}