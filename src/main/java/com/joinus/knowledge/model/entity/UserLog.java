package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 
 * @TableName math_user_logs
 */
@TableName(value ="math_user_logs")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class UserLog extends BaseEntity {

    /**
     * 
     */
    private String name;

    /**
     * 
     */
    private String phone;

    /**
     * 
     */
    private String path;

    /**
     * 
     */
    private String method;

    /**
     * 内容字段，存储为jsonb类型
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String content;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}