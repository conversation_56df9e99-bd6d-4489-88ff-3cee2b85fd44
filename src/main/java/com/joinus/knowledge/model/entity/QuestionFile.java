package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

/**
 * 
 * @TableName math_textbook_files
 */
@TableName(value ="math_question_files")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionFile implements Serializable {
    /**
     * 题目Id
     */
    private UUID questionId;

    /**
     * 文件ID
     */
    private UUID fileId;

    private Integer type;

    private Integer sortNo;
}