package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName math_section_knowledge_points
 */
@TableName(value ="math_section_knowledge_points")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SectionKnowledgePoint implements Serializable {
    /**
     * 
     */
    private UUID sectionId;

    /**
     * 
     */
    private UUID knowledgePointId;

    private Integer pageIndex;

    private Boolean remain;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SectionKnowledgePoint other = (SectionKnowledgePoint) that;
        return (this.getSectionId() == null ? other.getSectionId() == null : this.getSectionId().equals(other.getSectionId()))
            && (this.getKnowledgePointId() == null ? other.getKnowledgePointId() == null : this.getKnowledgePointId().equals(other.getKnowledgePointId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getSectionId() == null) ? 0 : getSectionId().hashCode());
        result = prime * result + ((getKnowledgePointId() == null) ? 0 : getKnowledgePointId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", sectionId=").append(sectionId);
        sb.append(", knowledgePointId=").append(knowledgePointId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}