package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.util.UUID;

import com.joinus.knowledge.enums.MathQuestionReviewType;
import lombok.Data;

/**
 * @TableName math_question_review_records
 */
@TableName(value ="math_question_review_records")
@Data
public class MathQuestionReviewRecords {
    private UUID questionId;

    private String username;

    private String status;

    private String remark;

    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    private Date reviewedAt;

    private MathQuestionReviewType type;
}