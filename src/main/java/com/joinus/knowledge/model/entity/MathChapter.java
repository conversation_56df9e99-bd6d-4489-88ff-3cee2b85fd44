package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.UUID;

/**
 * 
 * @TableName math_chapters
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value ="math_chapters")
public class MathChapter extends BaseEntity {
    /**
     * 
     */
    private String name;

    /**
     * 教材名称
     */
    private String textbook;

    /**
     * 学期
     */
    private String semester;

    private UUID textbookId;

    /**
     * 章节排序
     */
    private Integer sortNo;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MathChapter other = (MathChapter) that;
        return (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getTextbook() == null ? other.getTextbook() == null : this.getTextbook().equals(other.getTextbook()))
            && (this.getSemester() == null ? other.getSemester() == null : this.getSemester().equals(other.getSemester()))
            && (this.getSortNo() == null ? other.getSortNo() == null : this.getSortNo().equals(other.getSortNo()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getTextbook() == null) ? 0 : getTextbook().hashCode());
        result = prime * result + ((getSemester() == null) ? 0 : getSemester().hashCode());
        result = prime * result + ((getSortNo() == null) ? 0 : getSortNo().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", name=").append(getName());
        sb.append(", textbook=").append(getTextbook());
        sb.append(", semester=").append(getSemester());
        sb.append(", sortNo=").append(getSortNo());
        sb.append("]");
        return sb.toString();
    }

    @TableField(exist = false)
    private List<MathSection> sections;
}