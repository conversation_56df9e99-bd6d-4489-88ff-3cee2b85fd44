package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import lombok.Builder;
import lombok.Data;

/**
 * @TableName ai_chat_log
 */
@TableName(value ="ai_chat_log")
@Data
@Builder
public class AiChatLog {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String params;

    private String result;

    private Date createdAt;
}