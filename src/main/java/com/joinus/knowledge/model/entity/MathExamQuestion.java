package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

/**
 * @TableName math_exam_questions
 */
@TableName(value ="math_exam_questions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathExamQuestion implements Serializable {

    private UUID examId;

    private UUID questionId;

    private Integer sortNo;
}