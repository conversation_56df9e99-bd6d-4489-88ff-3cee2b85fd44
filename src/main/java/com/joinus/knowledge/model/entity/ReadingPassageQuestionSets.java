package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("reading_passage_question_sets")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReadingPassageQuestionSets extends BaseEntity {

    /**
     * 关联文章ID
     */
    @TableField("passage_id")
    private UUID passageId;

    /**
     * 套题名称
     */
    private String name;

    /**
     * 是否启用 0-挂起 1-使用中
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 审核状态：0-未审核 1-审核通过 2-审核不通过
     */
    @TableField("is_audit")
    private Integer isAudit;
}