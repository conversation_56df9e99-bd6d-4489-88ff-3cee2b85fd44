package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Builder;
import lombok.Data;

/**
 * 
 * @TableName math_textbook_files
 */
@TableName(value ="math_textbook_files")
@Data
@Builder
public class TextbookFile implements Serializable {
    /**
     * 教材ID，作为复合主键的一部分
     */
    private UUID textbookId;

    /**
     * 文件ID
     */
    private UUID fileId;

    /**
     * 页码，作为复合主键的一部分
     */
    private Integer pageNo;

    /**
     * 
     */
    private Date createdAt;

    /**
     * 
     */
    private Date updatedAt;

    /**
     * 
     */
    private Date deletedAt;

}