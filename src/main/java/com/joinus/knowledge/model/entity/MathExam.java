package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import com.joinus.knowledge.config.typehandler.LtreeTypeHandler;
import com.joinus.knowledge.enums.ExamSourceType;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.PublisherType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.JdbcType;

/**
 * @TableName math_exams
 */
@TableName(value ="math_exams")
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class MathExam extends BaseEntity {


    private String name;

    private Integer semester;

    private Integer grade;

    private ExamStateEnum state;

    private PublisherType publisher;

    private ExamSourceType source;

    private Integer year;

    @TableField(typeHandler = LtreeTypeHandler.class, jdbcType = JdbcType.OTHER)
    private String regionPath;

    @TableField(typeHandler = JsonbTypeHandler.class)
    private Object examSectionDescriptions;

}