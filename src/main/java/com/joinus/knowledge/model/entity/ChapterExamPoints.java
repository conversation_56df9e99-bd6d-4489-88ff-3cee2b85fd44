package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

/**
 * 章节与考点的关联关系表
 * @TableName chapter_exam_points
 */
@Data
@TableName(value ="chapter_exam_points")
public class ChapterExamPoints implements Serializable {
    /**
     * 章节ID
     */
    private UUID chapterId;

    /**
     * 考点ID
     */
    private UUID examPointId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public UUID getChapterId() {
        return chapterId;
    }

    /**
     * 
     */
    public void setChapterId(UUID chapterId) {
        this.chapterId = chapterId;
    }

    /**
     * 
     */
    public UUID getExamPointId() {
        return examPointId;
    }

    /**
     * 
     */
    public void setExamPointId(UUID examPointId) {
        this.examPointId = examPointId;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ChapterExamPoints other = (ChapterExamPoints) that;
        return (this.getChapterId() == null ? other.getChapterId() == null : this.getChapterId().equals(other.getChapterId()))
            && (this.getExamPointId() == null ? other.getExamPointId() == null : this.getExamPointId().equals(other.getExamPointId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getChapterId() == null) ? 0 : getChapterId().hashCode());
        result = prime * result + ((getExamPointId() == null) ? 0 : getExamPointId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", chapterId=").append(chapterId);
        sb.append(", examPointId=").append(examPointId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}