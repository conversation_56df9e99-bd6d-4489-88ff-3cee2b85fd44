package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.UUID;

@Data
@EqualsAndHashCode()
@TableName("reading_passage_question_set_entries")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReadingPassageQuestionSetEntries implements Serializable {
    /**
     * 套题ID
     */
    @TableField("set_id")
    private UUID setId;

    /**
     * 问题ID
     */
    @TableField("question_id")
    private UUID questionId;


    /**
     * 文章ID
     */
    @TableField("passage_id")
    private UUID passageId;

    /**
     * 题目顺序
     */
    @TableField("order_no")
    private Integer orderNo;
}