package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * @TableName math_exam_points
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value ="math_exam_points")
public class MathExamPoints extends BaseEntity {
    /**
     * 
     */
    private String name;

    /**
     * 考点描述
     */
    private String content;

    /**
     * 考点排序
     */
    private Integer sortNo;
}
