package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.util.UUID;

import com.joinus.knowledge.enums.MathGraphicsScriptStatusEnum;
import lombok.Data;

/**
 * @TableName math_question_graphics_scripts
 */
@TableName(value ="math_question_graphics_scripts")
@Data
public class QuestionGraphicsScript {
    private UUID questionId;

    private String aiScript;

    private UUID fileId;

    private String humanScript;

    private Date completedAt;

    private String username;

    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    private String remark;

    private MathGraphicsScriptStatusEnum status;
}