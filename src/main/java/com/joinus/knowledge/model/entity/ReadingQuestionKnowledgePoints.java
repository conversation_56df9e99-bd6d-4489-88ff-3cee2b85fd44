package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

/**
 * 语文阅读-题目和知识点关联关系
 */
@Data
@TableName("reading_question_knowledge_points")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReadingQuestionKnowledgePoints implements Serializable {
    /**
     * 题目ID
     */
    private UUID questionId;

    /**
     * 知识点ID
     */
    private UUID knowledgePointId;
}