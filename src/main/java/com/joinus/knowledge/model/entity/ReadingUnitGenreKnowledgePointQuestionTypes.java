package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

/**
 * 语文阅读-单元、文体、知识点和题型关联关系表
 */
@Data
@TableName("reading_unit_genre_knowledge_point_question_types")
public class ReadingUnitGenreKnowledgePointQuestionTypes implements Serializable {
    /**
     * reading_units表主键
     */
    @TableField("unit_id")
    private UUID unitId;

    /**
     * 文体
     */
    private String genre;

    /**
     * reading_knowledge_points表主键
     */
    @TableField("knowledge_point_id")
    private UUID knowledgePointId;

    /**
     * 题型
     */
    @TableField("question_type")
    private String questionType;

    /**
     * 权重
     */
    private Integer weight;
}