package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * @TableName math_answers
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value ="math_answers")
public class MathAnswer extends BaseEntity {
    /**
     * 答案
     */
    private String answer;

    /**
     * 答案解析
     */
    private String content;

}
