package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

/**
 * 题目与题型关联表
 * @TableName math_question_type_questions
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value ="math_question_type_questions")
public class QuestionTypesMapping implements Serializable {
    /**
     * 题目ID
     */
    private UUID questionId;

    /**
     * 题型ID
     */
    private UUID questionTypeId;

}