package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

/**
 * 题目与知识点关联表
 * @TableName math_knowledge_point_questions
 */
@TableName(value ="math_knowledge_point_questions")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class QuestionKnowledgePoint implements Serializable {

    /**
     * 题目ID
     */
    private UUID questionId;

    /**
     * 知识点ID
     */
    private UUID knowledgePointId;

}