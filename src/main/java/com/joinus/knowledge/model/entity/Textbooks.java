package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.UUID;

import com.joinus.knowledge.config.base.BaseEntity;
import lombok.Data;

/**
 * 
 * @TableName math_textbooks
 */
@TableName(value ="math_textbooks")
@Data
public class Textbooks extends BaseEntity {
    /**
     * 书名
     */
    private String name;

    /**
     * 出版社
     */
    private String publisher;

    /**
     * 学科
     */
    private String subject;

    /**
     * 文件id
     */
    private UUID fileId;

    private Integer pageOffset;

    private Integer grade;

    private Integer semester;

    private UUID ebookFileId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}