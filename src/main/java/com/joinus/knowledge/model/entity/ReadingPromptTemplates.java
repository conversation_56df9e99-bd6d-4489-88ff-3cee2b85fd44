package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("reading_prompt_templates")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReadingPromptTemplates extends BaseEntity {

    /**
     * 关联单元ID
     */
    @TableField("unit_id")
    private UUID unitId;

    /**
     * 文体类型
     */
    private String genre;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 模板内容
     */
    private String content;
}