package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import lombok.Data;

import java.util.UUID;

/**
 * 数学小节
 * @TableName math_sections
 */
@TableName(value ="math_sections")
@Data
public class MathSection extends BaseEntity {
    /**
     * 小节名称
     */
    @TableField("name")
    private String sectionName;

    /**
     * 排序号
     */
    private Integer sortNo;

    /**
     * 开始页码
     */
    private Integer startPage;

    /**
     * 结束页码
     */
    private Integer endPage;

    /**
     * 所属章节ID
     */
    private UUID chapterId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
