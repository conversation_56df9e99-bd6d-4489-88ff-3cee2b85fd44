package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

@TableName(value ="math_question_dimensions")
@Data
@Builder
public class MathQuestionDimension implements Serializable {

    @TableId
    private UUID questionId;

    private String problemTextResult;

    private String problemTextReason;

    private String visualElementsResult;

    private String visualElementsReason;

    private String formatAndTypeResult;

    private String formatAndTypeReason;

    private String coreKnowledgePointsResult;

    private String coreKnowledgePointsReason;

    private String primarySolutionMethodResult;

    private String primarySolutionMethodReason;

    private String solutionLogicalStructureResult;

    private String solutionLogicalStructureReason;

    private String cognitiveLoadAndDifficultyLevelResult;

    private String cognitiveLoadAndDifficultyLevelReason;

    private String mathematicalRigorAndCorrectnessResult;

    private String mathematicalRigorAndCorrectnessReason;

    private String potentialForVariationResult;

    private String potentialForVariationReason;

    private Boolean validationSuccess = false;
}