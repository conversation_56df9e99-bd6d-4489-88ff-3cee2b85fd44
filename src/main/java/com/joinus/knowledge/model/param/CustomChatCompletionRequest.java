package com.joinus.knowledge.model.param;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import lombok.Data;

@Data
public class CustomChatCompletionRequest extends ChatCompletionRequest {
    @JsonProperty("guided_json") // Jackson 的字段名映射注解
    private JsonNode guidedJson; // 替换 Fastjson 的 JSONObject
}
