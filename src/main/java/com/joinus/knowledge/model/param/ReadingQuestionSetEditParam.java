package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "修改套题题目 请求参数")
public class ReadingQuestionSetEditParam {

    @Schema(description = "套题id", implementation = String.class, example = "123456")
    @NotEmpty(message = "套题id不能为空")
    private UUID questionSetId;

    @Schema(description = "审核状态（0-未审核 1-已审核）", implementation = String.class, example = "1")
    @NotEmpty(message = "审核状态不能为空")
    private Integer isAudit;

    @Schema(description = "启用状态（0-禁用 1-启用）", implementation = String.class, example = "1")
    @NotEmpty(message = "启用状态不能为空")
    private Integer isEnabled;

    @Schema(description = "题目列表", implementation = String.class, example = "123456")
    @NotEmpty(message = "题目列表不能为空")
    private List<ReadingQuestionSetItemEditParam> questionList;
}

