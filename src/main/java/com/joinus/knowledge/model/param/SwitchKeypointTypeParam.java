package com.joinus.knowledge.model.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

@Data
public class SwitchKeypointTypeParam implements Serializable {

    @NotNull(message = "keyPointId不能为空")
    private UUID keyPointId;

    @NotNull(message = "type不能为空")
    private String type;

    @NotNull(message = "targetType不能为空")
    private String targetType;


}
