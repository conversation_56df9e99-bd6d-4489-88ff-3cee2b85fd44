package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.ExamSourceType;
import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
public class CreateExamParam implements Serializable {

    @Schema(description = "题目ID列表")
    @NotNull(message = "题目ID列表不能为空")
    private List<UUID> questionIds;

    @Schema(description = "试卷名称")
    @NotEmpty(message = "试卷名称不能为空")
    private String name;

    @Schema(description = "年级")
    private Integer grade;

    @Schema(description = "学期")
    private Integer semester;

    @Schema(description = "出版社")
    @NotNull(message = "出版社不能为空")
    private PublisherType publisher;

    @Schema(description = "试卷来源")
    private ExamSourceType examSource;
}
