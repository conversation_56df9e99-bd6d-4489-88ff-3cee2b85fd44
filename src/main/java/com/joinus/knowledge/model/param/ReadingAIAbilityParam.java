package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "语文阅读AI能力请求参数")
public class ReadingAIAbilityParam implements Serializable {

    @NotEmpty(message = "id不能为空")
    @Schema(description = "业务id，回调时用于确认哪次请求", example = "5a14bfdf-9a84-4579-8111-ef4a755d96f5", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    @NotEmpty(message = "题目答案列表不能为空")
    @Schema(description = "题目答案列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ReadingAIAbilityItemParam> items;

    @Deprecated
    @Schema(description = "分析类型：1、单次测试，2、周报或月报", example = "0")
    private Integer type;

    @Schema(description = "AI能力类型 1-AI批改 2-薄弱知识点分析 3-综合训练建议 4-薄弱题型分析 5-答案比对 6-次报告 7-周报告", example = "0")
    private Integer readingAIAbilityType;

    @Schema(description = "直接放到prompt里的内容")
    private String directMessage;

}
