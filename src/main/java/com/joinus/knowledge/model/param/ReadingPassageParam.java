package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.UUID;

/**
 * 阅读题试卷参数
 *
 * <AUTHOR>
 * @date 2025/4/1 9:46
 */
@Data
@Schema(description = "阅读题试卷参数")
public class ReadingPassageParam {
    @Schema(description = "单元id", implementation = String.class, example = "1")
    private UUID unitId;
    @Schema(description = "薄弱知识点列表")
    private List<UUID> knowledgePoints;
    @Schema(description = "做过的题目id列表")
    private List<UUID> questionIds;
    @Schema(description = "文章id 指定文章再次练习时必传", implementation = String.class, example = "1")
    private UUID passageId;
    @Schema(description = "题目数量", hidden = true)
    private int count;
}
