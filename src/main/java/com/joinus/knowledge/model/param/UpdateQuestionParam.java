package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.QuestionType;
import com.joinus.knowledge.model.vo.QuestionAnswerDetailVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
public class UpdateQuestionParam implements Serializable {


    private String content;

    private QuestionType questionType;

    private Integer difficulty;

    private Boolean existGraphics;

    private Boolean enabled;

    private List<String> files;

}
