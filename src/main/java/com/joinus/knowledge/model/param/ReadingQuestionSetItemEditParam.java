package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "修改题目 请求参数")
public class ReadingQuestionSetItemEditParam {
    @Schema(description = "题id", implementation = String.class, example = "123456")
    @NotEmpty(message = "题目ID不能为空")
    private UUID id;

    @Schema(description = "题目内容", implementation = String.class, example = "春眠不觉晓")
    @NotEmpty(message = "题目内容不能为空")
    private String content;

    @Schema(description = "答案解析", implementation = String.class, example = "春眠不觉晓")
    @NotEmpty(message = "答案解析不能为空")
    private String answerContent;

    @Schema(description = "答案", implementation = String.class, example = "春眠不觉晓")
    @NotEmpty(message = "答案不能为空")
    private String answer;
}

