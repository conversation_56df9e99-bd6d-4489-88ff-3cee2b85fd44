package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(description = "题目答案列表请求参数")
public class ReadingAIAbilityItemParam implements Serializable {

    @NotEmpty(message = "题目id")
    @Schema(description = "题目id", example = "5a14bfdf-9a84-4579-8111-ef4a755d96f5", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    @Schema(description = "知识点")
    private String knowledgePoint;

    @Schema(description = "题型")
    private String questionType;

    @Schema(description = "答案，如果是多个（如填空题有多个空），用逗号隔开")
    private String answer;

    private BigDecimal result;

}
