package com.joinus.knowledge.model.param;

import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "设置主要别名参数")
public class SetPrimaryAliasParam implements Serializable {

    @Schema(description = "别名值", example = "2024学年三年级下册数学名校真题卷期中押题卷")
    @NotBlank(message = "别名值不能为空")
    private String aliasValue;

    @Schema(description = "标签属性（会自动设置 isPrimary=true）")
    private JSONObject properties;
}
