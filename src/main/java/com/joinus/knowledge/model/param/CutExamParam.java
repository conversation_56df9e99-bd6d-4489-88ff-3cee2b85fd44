package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "试卷切题请求")
public class CutExamParam implements Serializable {


    @NotNull(message = "原图ossKey列表不能为空")
    @Schema(description = "图片ossKey列表", example = "[\"math-question/2025/3/20/image1.jpg\", \"math-question/2025/3/20/image2.jpg\"]", required = true)
    private List<String> objectNames;

    @NotNull(message = "抹除笔迹图片ossKey列表不能为空")
    @Schema(description = "抹除笔迹图片ossKey列表", example = "[\"math-question/2025/3/20/image1.jpg\", \"math-question/2025/3/20/image2.jpg\"]", required = true)
    private List<String> handwritingRemovedObjectNames;

    @NotNull
    @Schema(description = "oss枚举类", implementation = OssEnum.class, example = "ALIYUN_EDU_KNOWLEDGE_HUB", required = true)
    private OssEnum ossEnum;

    @Schema(description = "年级 1-9", implementation = Integer.class, example = "9", required = true)
    private Integer grade;

    @Schema(description = "学期 1上学期 2下学期", implementation = Integer.class, example = "1", required = true)
    private Integer semester;

    @NotNull
    @Schema(description = "出版社")
    private PublisherType publisher;

    @Schema(description = "试卷名称", implementation = String.class, example = "2025年9年级下学期数学试卷")
    private String name;

}
