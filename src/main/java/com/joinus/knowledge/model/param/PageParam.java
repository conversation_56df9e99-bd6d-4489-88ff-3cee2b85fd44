package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * 通用分页参数父类
 */
@Data
@Schema(description = "通用分页参数")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class PageParam implements Serializable {
    @Schema(description = "页码（从1开始）", example = "1")
    private int page = 1;
    @Schema(description = "每页数量", example = "10")
    private int size = 10;
}
