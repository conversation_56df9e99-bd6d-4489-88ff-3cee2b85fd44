package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
public class SectionKnowledgePointParam implements Serializable {

    @Schema(description = "出版社", required = true, implementation = PublisherType.class, example = "BEI_SHI_DA")
    private PublisherType publisher;

    @Schema(description = "年级 1-9对应一到九年级", required = true, implementation = Integer.class, example = "8")
    private Integer grade;

    @Schema(description = "学期 1上学期 2下学期", required = true, implementation = Integer.class, example = "2")
    private Integer semester;

    @Schema(description = "知识点id列表", required = true, implementation = List.class, example = "8f403b25-9320-4c21-8874-7642afcda25d,60cde8ef-3579-4e71-9010-2af59f93eb48,604f4b35-979d-4f23-b003-6e6727a44ba7")
    private List<UUID> knowledgePointIds;
}
