package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateQuestionEnabledParam implements Serializable {

    @NotNull(message = "问题id列表不能为空")
    private List<UUID> questionIds;

    @NotNull(message = "问题状态不能为空")
    private Boolean enabled;
}
