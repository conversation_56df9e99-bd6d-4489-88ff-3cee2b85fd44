package com.joinus.knowledge.model.param;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.joinus.knowledge.config.excel.UUIDConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateExamByExcelParam implements Serializable {

    @ExcelProperty(value = "试卷ID", index = 0, converter = UUIDConverter.class)
    private UUID examId;

    @ExcelProperty(value = "试卷名称", index = 1)
    private String examName;

    @ExcelProperty(value = "年份", index = 2)
    private Integer year;

    @ExcelProperty(value = "学期", index = 3)
    private Integer semester;

    @ExcelProperty(value = "年级", index = 4)
    private Integer grade;

    @ExcelProperty(value = "地区全路径", index = 5)
    private String regionPath;
}
