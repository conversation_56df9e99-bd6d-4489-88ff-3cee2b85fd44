package com.joinus.knowledge.model.param;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelProperty;

import com.joinus.knowledge.config.excel.GenericEnumConverter;
import com.joinus.knowledge.config.excel.JSONObjectConverter;
import com.joinus.knowledge.config.excel.UUIDConverter;
import com.joinus.knowledge.enums.ExamTagType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AddExamTagByExcelParam implements Serializable {

    @ExcelProperty(value = "试卷ID", index = 0, converter = UUIDConverter.class)
    private UUID examId;

    @ExcelProperty(value = "试卷名称", index = 1)
    private String examName;

    @ExcelProperty(value = "标签类型", index = 2, converter = GenericEnumConverter.class)
    private ExamTagType type;

    @ExcelProperty(value = "标签值", index = 3)
    private String value;

    @ExcelProperty(value = "标签额外属性", index = 4, converter = JSONObjectConverter.class)
    private JSONObject properties;
}
