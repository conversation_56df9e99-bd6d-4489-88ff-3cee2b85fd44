package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.MathQuestionDimensionOptionType;
import com.joinus.knowledge.enums.MathQuestionDimensionType;
import com.joinus.knowledge.model.dto.CutQuestionSubjectDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathQuestionDimensionParam {

    private MathQuestionDimensionType  type;

    private List<MathQuestionDimensionOptionType> options;

}
