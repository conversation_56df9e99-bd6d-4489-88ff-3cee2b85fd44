package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数学专项训练参数")
public class SpecialTrainingParam {

    @Schema(description = "试卷id", required = true, example = "9f93fdf8-5211-4c3a-969a-56087be5c1d8")
    @NotNull(message = "试卷id不能为空")
    private UUID examId;

    @Schema(description = "已选择题目", required = true, example = "true")
    @NotNull(message = "已选择题目不能为空")
    private Boolean selectQuestion;

    @Schema(description = "已选择答案", required = true, example = "true")
    @NotNull(message = "已选择答案不能为空")
    private Boolean selectAnswer;

    @Schema(description = "知识点id集合", required = true, example = "[13f58a1c-f851-4c67-8a7e-c48a51e5219b,1619f1a2-b2da-431d-9ecf-7456e7b8132a]")
    private List<UUID> knowledgePointsIds;

    private UUID pdfUUID;
}
