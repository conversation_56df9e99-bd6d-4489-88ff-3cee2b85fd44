package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.PaperSubjectType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "检测是否存在试卷请求")
public class GetCoordinateParam implements Serializable {


    @Schema(description = "图片ossKey", implementation = String.class, example = "uat/math-question/2025/3/20/1f7b2993e6c3455b81f2234419be94a3e1784f1a-b429-48c2-a8bd-ad2fab08eb28.png", required = true)
    private String ossKey;

    @Schema(description = "oss枚举类", implementation = OssEnum.class, example = "ALIYUN_EDU_KNOWLEDGE_HUB", required = true)
    private OssEnum ossEnum;

    @Schema(description = "试卷学科类型", implementation = PaperSubjectType.class, example = "PRIMARY_SCHOOL_MATH", required = true)
    private PaperSubjectType paperSubjectType;

}
