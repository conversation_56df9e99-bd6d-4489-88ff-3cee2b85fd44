package com.joinus.knowledge.model.param;

import cn.hutool.json.JSONObject;
import com.joinus.knowledge.enums.ExamTagType;
import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddExamTagParam implements Serializable {

    @Schema(description = "标签类型")
    @NotNull(message = "标签类型不能为空")
    private ExamTagType type;

    @Schema(description = "标签值")
    @NotNull(message = "标签值不能为空")
    private String value;

    @Schema(description = "标签属性")
    private JSONObject properties;

}
