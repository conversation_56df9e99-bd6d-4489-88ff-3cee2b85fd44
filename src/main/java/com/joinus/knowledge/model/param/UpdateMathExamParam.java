package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.enums.RegularExamType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "试卷修改参数")
public class UpdateMathExamParam {

        private String name;

        private Integer semester;

        private Integer grade;

        private ExamStateEnum state;

        private PublisherType publisher;

        private List<OssFileParam> images;

        private String examSectionDescriptions;

        /**
         * 别名列表，第一个是主别名
         */
        private List<String> aliases;

        private Integer year;

        private String regionPath;

        /**
         * 学校id列表
         */
        private List<Long> schoolIds;

        /*
         * 名校卷值
         */
        private List<String> eliteSchoolExamPaperValues;

        private RegularExamType regularExamType;


}
