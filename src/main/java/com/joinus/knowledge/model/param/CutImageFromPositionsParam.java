package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.vo.CoordinatePoint;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "切题参数")
public class CutImageFromPositionsParam {

    @Schema(description = "坐标点组", example = "[{\"x\":105,\"y\":375},{\"x\":951,\"y\":375},{\"x\":951,\"y\":515},{\"x\":106,\"y\":515}]")
    @NotNull(message = "坐标点不能为空")
    private List<CoordinatePoint> positions;

    @Schema(description = "oss枚举类", example = "ALIYUN_EDU_KNOWLEDGE_HUB")
    private OssEnum ossEnum;

    @Schema(description = "ossKey", example = "uat/math-question/2025/3/20/1f7b2993e6c3455b81f2234419be94a3e1784f1a-b429-48c2-a8bd-ad2fab08eb28.png")
    @NotEmpty(message = "ossKey不能为空")
    private String ossKey;
}
