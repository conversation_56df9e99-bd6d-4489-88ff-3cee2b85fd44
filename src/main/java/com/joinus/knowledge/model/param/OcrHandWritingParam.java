package com.joinus.knowledge.model.param;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.HashMap;

@Data
@Schema(description = "OCR手写文字识别请求参数")
public class OcrHandWritingParam implements Serializable {

    @NotEmpty(message = "图片URL不能为空")
    @Schema(description = "图片完整URL", example = "https://www.images.com/uat/math-question/2025/3/20/1f7b2993e6c3455b81f2234419be94a3e1784f1a-b429-48c2-a8bd-ad2fab08eb28.png", requiredMode = Schema.RequiredMode.REQUIRED)
    private String url;

    @Pattern(regexp = "^(?:$|true|false)$", message = "参数格式不正确，如设置，请输入true或false")
    @Schema(description = "是否返回识别结果中每一行的置信度，默认为false，不返回置信度", example = "false")
    private String probability;

    @Pattern(regexp = "^(?:$|true|false)$", message = "参数格式不正确，如设置，请输入true或false")
    @Schema(description = "是否检测图像朝向，默认不检测，即：false。", example = "false")
    private String detectDirection;

    public HashMap<String, String> buildOptions() {
        HashMap<String, String> options = new HashMap<>();
        if (StrUtil.isNotBlank(probability)) {
            options.put("probability", probability);
        }
        if (StrUtil.isNotBlank(detectDirection)) {
            options.put("detect_direction", detectDirection);
        }
        return options;
    }

}
