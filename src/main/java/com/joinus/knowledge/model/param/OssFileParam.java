package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "oss文件参数")
public class OssFileParam {

        @Schema(description = "oss平台类型", implementation = OssEnum.class)
        private OssEnum ossEnum;

        @Schema(description = "oss平台文件key", implementation = String.class)
        private String ossKey;

        @Schema(description = "文件类型 0附图 1原图", implementation = Integer.class)
        private Integer type;

        @Schema(description = "排序号 从0开始", implementation = Integer.class)
        private Integer sortNo;
}
