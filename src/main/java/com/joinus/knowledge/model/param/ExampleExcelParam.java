package com.joinus.knowledge.model.param;

import com.alibaba.excel.annotation.ExcelProperty;
import com.joinus.knowledge.config.excel.GenericEnumConverter;
import com.joinus.knowledge.config.excel.UUIDConverter;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.enums.QuestionType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

/**
 * 示例Excel参数类
 * 展示如何使用通用枚举转换器处理所有枚举类型
 *
 * 🎯 关键优势：
 * - 一个转换器处理所有枚举
 * - 自动识别枚举值字段
 * - 无需为每个枚举创建专用转换器
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExampleExcelParam implements Serializable {

    @ExcelProperty(value = "ID", index = 0, converter = UUIDConverter.class)
    private UUID id;

    @ExcelProperty(value = "名称", index = 1)
    private String name;

    // ✅ 所有枚举都使用同一个通用转换器
    @ExcelProperty(value = "试卷状态", index = 2, converter = GenericEnumConverter.class)
    private ExamStateEnum examState;

    @ExcelProperty(value = "出版社", index = 3, converter = GenericEnumConverter.class)
    private PublisherType publisher;

    @ExcelProperty(value = "题目类型", index = 4, converter = GenericEnumConverter.class)
    private QuestionType questionType;

    // 🚀 新增任何枚举都可以直接使用通用转换器
    @ExcelProperty(value = "试卷来源", index = 5, converter = GenericEnumConverter.class)
    private com.joinus.knowledge.enums.ExamSourceType examSource;
}
