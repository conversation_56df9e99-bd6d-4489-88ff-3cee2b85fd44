package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.ExamSourceType;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.enums.RegularExamType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.UUID;

/**
 * 试卷分页查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数学试卷分页查询参数")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class MathExamPageQueryParam extends PageParam {
    @Schema(description = "试卷名称", example = "期末考试")
    private String name;
    @Schema(description = "试卷状态", example = "TODO")
    private ExamStateEnum state;
    @Schema(description = "教材版本", example = "BEI_SHI_DA")
    private PublisherType publisher;
    @Schema(description = "年级", example = "8")
    private Integer grade;
    @Schema(description = "学期", example = "2")
    private Integer semester;
    @Schema(description = "试卷来源", example = "PAST_EXAM_PAPER")
    private ExamSourceType source;
    @Schema(description = "年份")
    private Integer year;
    @Schema(description = "地区路径")
    private String regionPath;
    @Schema(description = "试卷id")
    private UUID id;
    @Schema(description = "常规试卷类型", example = "MONTHLY_EXAM")
    private RegularExamType regularExamType;
    @Schema(description = "学校id")
    private Long schoolId;

    private List<UUID> examIds;

}
