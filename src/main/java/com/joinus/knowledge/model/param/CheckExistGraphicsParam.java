package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "检测题目是否存在图形接口")
public class CheckExistGraphicsParam implements Serializable {

    @Schema(description = "题目文本", implementation = String.class, example = "题目文本")
    private String questionText;
}
