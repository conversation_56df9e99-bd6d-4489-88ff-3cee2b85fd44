package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "获取知识点和难度的请求体")
public class GenerateKnowledgePointParam implements Serializable {

    @Schema(description = "题目id", implementation = UUID.class, example = "b624dd6b-38c8-4a9d-bc5f-f1baa4b174cc")
    private UUID questionId;

    @Schema(description = "图片ossKey列表", implementation = List.class, example = "[\"image1.jpg\", \"image2.jpg\"]")
    private List<String> objectNames;

    @Schema(description = "oss枚举类", implementation = OssEnum.class, example = "ALIYUN_EDU_KNOWLEDGE_HUB")
    private OssEnum ossEnum;

    @NotNull(message = "年级不能为空")
    @Schema(description = "年级 1-9", implementation = Integer.class, example = "1", required = true)
    private Integer grade;

    @Schema(description = "学期 1上学期 2下学期", implementation = Integer.class, example = "1", required = true)
    private Integer semester;

    @Schema(description = "是否为考试题", implementation = Boolean.class, example = "false", required = false)
    private Boolean fromExam;

    @Schema(description = "考试id", implementation = UUID.class, example = "e489e8f6-10f6-4825-a8cb-fb28f7e6f6f7", required = false)
    private UUID examId;

    @Schema(description = "出版社类型", implementation = PublisherType.class, example = "BEI_SHI_DA", required = false)
    private PublisherType publisher;

}
