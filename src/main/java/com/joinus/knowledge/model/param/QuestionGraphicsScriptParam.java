package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "数学题补图脚本请求参数")
public class QuestionGraphicsScriptParam {
    @Schema(description = "题目id")
    private UUID questionId;

    @Schema(description = "oss的key")
    private String objectName;

    @Schema(description = "人工修改后画图的geobra script")
    private String humanScript;

    @Schema(description = "oss平台枚举", example = "ALIYUN_EDU_KNOWLEDGE_HUB")
    private OssEnum ossEnum;
}
