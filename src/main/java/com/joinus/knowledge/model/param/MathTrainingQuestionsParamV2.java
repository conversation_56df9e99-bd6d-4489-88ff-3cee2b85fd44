package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
public class MathTrainingQuestionsParamV2 implements Serializable {

    @Schema(description = "试卷id", implementation = UUID.class, example = "f8fa7f68-96de-4f03-8fd2-f92f6df494a9")
    private UUID examId;

    @Schema(description = "题目数量", implementation = Integer.class, example = "2")
    private Integer questionCount;

    @ArraySchema(arraySchema = @Schema(description = "知识点id列表"), schema = @Schema(implementation = UUID.class, example = "f8fa7f68-96de-4f03-8fd2-f92f6df494a9"))
    private List<UUID> knowledgePointIds;

    @ArraySchema(arraySchema = @Schema(description = "题型id列表"), schema = @Schema(implementation = UUID.class, example = "8392e78a-d5a2-4a22-b70a-bf1400d17534"))
    private List<UUID> questionTypeIds;

    @Schema(description = "是否启用历年真题", implementation = Boolean.class, example = "true")
    private Boolean enablePastExamPapers = false;

    @Schema(description = "章节id", implementation = UUID.class, example = "334fd120-25c9-4360-8816-f457eba8fb46")
    private UUID chapterId;

    @Schema(description = "小节id", implementation = UUID.class, example = "bb973abf-3e73-403d-8dee-011dfd6d62d4")
    private UUID sectionId;

    @Schema(description = "年级", implementation = Integer.class, example = "8")
    private Integer grade;

    @Schema(description = "学期", implementation = Integer.class, example = "2")
    private Integer semester;

    @Schema(description = "出版社", implementation = PublisherType.class, example = "BEI_SHI_DA")
    private PublisherType publisher;
}
