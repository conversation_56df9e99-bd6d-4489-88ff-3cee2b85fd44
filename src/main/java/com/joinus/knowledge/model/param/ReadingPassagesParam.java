package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "修改阅读文章 请求参数")
public class ReadingPassagesParam {

    @NotEmpty(message = "文章id不能为空")
    @Schema(description = "文章id", implementation = String.class, example = "123456")
    private UUID id;

    @NotEmpty(message = "文章标题不能为空")
    @Schema(description = "文章标题", implementation = String.class, example = "春")
    private String title;

    @NotEmpty(message = "文章内容不能为空")
    @Schema(description = "文章内容", implementation = String.class, example = "春眠不觉晓")
    private String content;
}
