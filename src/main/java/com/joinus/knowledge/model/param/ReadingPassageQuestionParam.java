package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "修改文章题目 请求参数")
public class ReadingPassageQuestionParam {

    @NotEmpty(message = "题id不能为空")
    @Schema(description = "题id", implementation = String.class, example = "123456")
    private UUID id;

    //@NotEmpty(message = "题型不能为空")
    @Schema(description = "题型", implementation = String.class, example = "判断题")
    private String questionType;

    @NotEmpty(message = "题目内容不能为空")
    @Schema(description = "题目内容", implementation = String.class, example = "春眠不觉晓")
    private String content;

    @NotEmpty(message = "答案解析不能为空")
    @Schema(description = "答案解析", implementation = String.class, example = "春眠不觉晓")
    private String answerContent;

    @NotEmpty(message = "答案不能为空")
    @Schema(description = "答案", implementation = String.class, example = "春眠不觉晓")
    private String answer;

    //@NotEmpty(message = "题目知识点不能为空")
    @Schema(description = "题目知识点", implementation = String.class, example = "春眠不觉晓")
    private List<UUID> knowledgePointIds;
}
