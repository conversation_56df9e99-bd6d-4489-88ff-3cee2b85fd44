package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "检测试卷是否已解析完毕")
public class CheckExistExamKnowledgePointsParam implements Serializable {


    @Schema(description = "试卷id", implementation = UUID.class, example = "75244ab6-f70e-451d-aa02-bff12d8fb683", required = true)
    private UUID examId;
}
