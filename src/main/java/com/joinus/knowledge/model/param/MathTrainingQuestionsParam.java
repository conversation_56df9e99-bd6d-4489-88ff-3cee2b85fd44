package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MathTrainingQuestionsParam implements Serializable {

    @Schema(description = "题目数量", implementation = Integer.class, example = "2")
    private Integer questionCount;

    @ArraySchema(arraySchema = @Schema(description = "知识点参数列表"), schema = @Schema(implementation = MathTrainingKnowledgePointParam.class))
    private List<MathTrainingKnowledgePointParam> knowledgePointParams;
}
