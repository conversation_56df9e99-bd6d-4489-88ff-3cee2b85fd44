package com.joinus.knowledge.model.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class DifyGenerateQuestionParam {

    @JsonProperty("question_id")
    private String questionId;

    @JsonProperty("content")
    private String content;

    @JsonProperty("type")
    private String type;

    @JsonProperty("answer")
    private String answer;

    @JsonProperty("analysis")
    private String analysis;

    @JsonProperty("answer_id")
    private String answerId;

    @JsonProperty("number_of_questions")
    private String numberOfQuestions;

    @JsonProperty("content_image")
    private List<DifyImageParam> contentImage;


}
