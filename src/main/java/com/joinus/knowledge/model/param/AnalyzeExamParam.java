package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "试卷分析请求参数")
public class AnalyzeExamParam implements Serializable {
    @Schema(description = "试卷id", implementation = UUID.class, example = "9f93fdf8-5211-4c3a-969a-56087be5c1d8", required = true)
    @NotNull(message = "试卷id不能为空")
    private UUID examId;

    @Schema(description = "额外参数，用于扩展功能", implementation = Map.class)
    private Map<String, Object> extraParams;

    @Schema(description = "教材版本")
    @NotNull(message = "教材版本不能为空")
    private PublisherType publisher;

    @Schema(description = "自动分析成功", implementation = Boolean.class, example = "true")
    private Boolean autoAnalyzeSuccess;
}
