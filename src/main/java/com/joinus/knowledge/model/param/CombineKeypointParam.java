package com.joinus.knowledge.model.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

@Data
public class CombineKeypointParam implements Serializable {

    @NotNull(message = "keyPointId不能为空")
    private UUID keyPointId1;

    @NotNull(message = "keyPointId不能为空")
    private UUID keyPointId2;

    @NotNull(message = "type不能为空")
    private String type;



}
