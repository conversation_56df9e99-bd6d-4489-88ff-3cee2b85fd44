package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
public class MathTrainingKnowledgePointParam implements Serializable {

    @Schema(description = "知识点id", implementation = UUID.class, example = "f8fa7f68-96de-4f03-8fd2-f92f6df494a9")
    private UUID knowledgePointId;

    @ArraySchema(arraySchema = @Schema(description = "题目类型id列表"), schema = @Schema(implementation = UUID.class, example = "8392e78a-d5a2-4a22-b70a-bf1400d17534"))
    private List<UUID> questionTypeIds;
    
}
