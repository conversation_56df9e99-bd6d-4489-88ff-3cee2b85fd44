package com.joinus.knowledge.model.param;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.ai.openai.api.OpenAiApi;

import java.util.List;

public record VllmChatCompletionRequest(
        @JsonProperty("messages") List<OpenAiApi.ChatCompletionMessage> messages,
        @JsonProperty("model") String model,
        @JsonProperty("temperature") Double temperature,
        @JsonProperty("stream") Boolean stream,
        @JsonProperty("extra_body") JSONObject extraBody) {

    public VllmChatCompletionRequest(List<OpenAiApi.ChatCompletionMessage> messages, String model, Double temperature, Boolean stream) {
        this(messages, model, temperature, stream, null);
    }

    public VllmChatCompletionRequest(List<OpenAiApi.ChatCompletionMessage> messages, String model, Double temperature) {
        this(messages, model, temperature, null, null);
    }

    public VllmChatCompletionRequest(List<OpenAiApi.ChatCompletionMessage> messages, String model) {
        this(messages, model, null, null, null);
    }
}