package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@Schema(description = "解题参数")
public class SolveQuestionParam implements Serializable {

    @Schema(description = "题目id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0a0-d0a0-d0a0d0a0d0a0")
    private UUID questionId;

    @Schema(description = "题目文本", implementation = String.class, example = "题目文本")
    private String questionText;

    @Schema(description = "图片ossKey列表", implementation = List.class, example = "[\"image1.jpg\", \"image2.jpg\"]")
    private List<String> objectNames;

    @Schema(description = "oss枚举类", implementation = OssEnum.class, example = "ALIYUN_EDU_KNOWLEDGE_HUB")
    private OssEnum ossEnum;

    @NotNull(message = "年级不能为空")
    @Schema(description = "年级 1-9", implementation = Integer.class, example = "1", required = true)
    private Integer grade;

    @Schema(description = "学期 1上学期 2下学期", implementation = Integer.class, example = "1", required = true)
    private Integer semester;

    @Schema(description = "prompt,暂时不用传", implementation = String.class)
    private String prompt;

    @Schema(description = "额外参数，用于扩展功能", implementation = Map.class)
    private Map<String, Object> extraParams;

}
