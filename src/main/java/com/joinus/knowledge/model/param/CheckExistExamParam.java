package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "检测是否存在试卷请求")
public class CheckExistExamParam implements Serializable {


    @Schema(description = "图片ossKey列表", implementation = List.class, example = "[\"image1.jpg\", \"image2.jpg\"]", required = true)
    private List<String> objectNames;

    @Schema(description = "oss枚举类", implementation = OssEnum.class, example = "ALIYUN_EDU_KNOWLEDGE_HUB", required = true)
    private OssEnum ossEnum;

    @Schema(description = "年级 1-9", implementation = Integer.class, example = "1", required = true)
    private Integer grade;

    @Schema(description = "学期 1上学期 2下学期", implementation = Integer.class, example = "1", required = true)
    private Integer semester;

    @Schema(description = "出版社类型", implementation = PublisherType.class, example = "BEI_SHI_DA", required = true)
    private PublisherType publisher;
}
