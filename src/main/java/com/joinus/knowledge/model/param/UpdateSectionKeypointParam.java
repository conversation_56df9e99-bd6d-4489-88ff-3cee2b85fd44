package com.joinus.knowledge.model.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

@Data
public class UpdateSectionKeypointParam implements Serializable {

    @NotNull(message = "sectionId不能为空")
    private UUID sectionId;

    @NotNull(message = "keyPointId不能为空")
    private UUID keyPointId;

    private UUID oldSectionId;

    @NotNull(message = "type不能为空")
    private String type;

    private Integer pageIndex;

    private Integer oldPageIndex;

}
