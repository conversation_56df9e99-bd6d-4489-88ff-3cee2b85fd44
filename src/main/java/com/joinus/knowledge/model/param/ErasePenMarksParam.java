package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 章节参数类，用于接收前端数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "擦除笔迹参数类")
public class ErasePenMarksParam {

    @Schema(description = "ossKey", implementation = String.class, example = "tmp/ocr/rmhw3", required = true)
    @NotEmpty(message = "ossKey不能为空")
    private String ossKey;

    @NotNull(message = "ossEnum不能为空")
    @Schema(description = "ossEnum", implementation = OssEnum.class, example = "MINIO_EDU_KNOWLEDGE_HUB", required = true)
    private OssEnum ossEnum;
}
