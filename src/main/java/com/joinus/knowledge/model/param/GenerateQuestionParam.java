package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "举一反三请求体")
public class GenerateQuestionParam implements Serializable {

    @Schema(description = "题目id", implementation = UUID.class, example = "b624dd6b-38c8-4a9d-bc5f-f1baa4b174cc")
    private UUID questionId;

}
