package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.OssEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateQuestionDetailParam implements Serializable {

    private String type ;

    private UUID keyPointId;

    private List<QuestionDetailParam> questionDetailList;

    private OssEnum ossEnum;


}
