package com.joinus.knowledge.model.dto;

import com.joinus.knowledge.enums.QuestionType;
import com.joinus.knowledge.model.param.OssFileParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "添加试卷题目参数")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AddExamQuestionParam implements Serializable {

    @Schema(description = "题目内容", implementation = String.class, example = "1+1=?")
    private String content;

    @Schema(description = "题目类型", implementation = QuestionType.class, example = "MULTIPLE_CHOICE")
    private QuestionType type;

    @Schema(description = "原图文件列表", implementation = OssFileParam.class)
    private List<OssFileParam> originalFiles;

    @Schema(description = "题目序号", implementation = Integer.class, example = "1")
    private Integer sortNo;

    @Schema(description = "题目难度", implementation = Integer.class, example = "1")
    private Integer difficulty;



}
