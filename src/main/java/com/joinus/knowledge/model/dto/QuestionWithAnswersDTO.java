package com.joinus.knowledge.model.dto;

import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.MathAnswer;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 问题和多个答案的数据传输对象
 */
@Data
public class QuestionWithAnswersDTO implements Serializable {
    /**
     * 题目信息
     */
    private MathQuestion question;

    /**
     * 答案信息列表 - 支持一题多答
     */
    private List<MathAnswer> questionAnswers;

    public QuestionWithAnswersDTO(MathQuestion question) {
        this.question = question;
        this.questionAnswers = new ArrayList<>();
    }
}
