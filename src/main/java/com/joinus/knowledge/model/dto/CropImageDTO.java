package com.joinus.knowledge.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "切图参数")
public class CropImageDTO {

    @Schema(description = "横坐标", example = "120")
    private Integer x;
    
    @Schema(description = "纵坐标", example = "85")
    private Integer y;

    @Schema(description = "宽度", example = "120")
    private Integer w;

    @Schema(description = "高度", example = "85")
    private Integer h;
}
