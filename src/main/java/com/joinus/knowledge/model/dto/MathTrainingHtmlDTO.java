package com.joinus.knowledge.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * 数学专项训练HTML DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "数学专项训练HTML")
public class MathTrainingHtmlDTO {

    @Schema(description = "题目HTML")
    private String questionHtml;

    @Schema(description = "题目答案HTML")
    private String questionWithAnswerHtml;

    @Schema(description = "答案HTML")
    private String answerHtml;
    
    @Schema(description = "题目和答案分离HTML")
    private String separateAnswersHtml;

    @Schema(description = "临时UUID，用户保证html内容与pdf内容保持一致")
    private UUID pdfUUID;

    public MathTrainingHtmlDTO(String questionHtml, String questionWithAnswerHtml, String answerHtml, String separateAnswersHtml) {
        this.questionHtml = questionHtml;
        this.questionWithAnswerHtml = questionWithAnswerHtml;
        this.answerHtml = answerHtml;
        this.separateAnswersHtml = separateAnswersHtml;
    }
}
