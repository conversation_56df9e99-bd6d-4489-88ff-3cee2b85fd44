package com.joinus.knowledge.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExamSummaryDTO implements Serializable {

    private String title;

    private String term;

    private String grade;

    private String academicYear;

    private String subject;

    private List<ExamSummaryQuestionDTO> questions;
}
