package com.joinus.knowledge.model.dto;

import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.MathAnswer;
import lombok.Data;

import java.io.Serializable;

/**
 * 问题和答案的数据传输对象
 */
@Data
public class QuestionAnswerDTO implements Serializable {

    /**
     * 题目信息
     */
    private MathQuestion question;

    /**
     * 答案信息
     */
    private MathAnswer questionAnswer;

}
