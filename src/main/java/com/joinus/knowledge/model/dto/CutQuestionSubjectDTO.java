package com.joinus.knowledge.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "一个题目切割坐标")
public class CutQuestionSubjectDTO {

    @Schema(description = "题目类型，0：选择题；1：填空题；2：阅读理解（阅读+问答选择）；3：完型填空（阅读+选择）；4：阅读填空（阅读+填空）；5：问答题；6：选择题，多选多；7：填空、选择题混合；8：应用题；9：判断题；10：作图题；11：材料题；12：计算题；13：连线题；14：作文题；15：解答题；16：其他；17：图；18：表格", example = "1")
    private Integer type;

    @Schema(description = "同类型题目中的序号", example = "0")
    private Integer index;

    @Schema(description = "题目文本", example = "1+1=?")
    private String text;

    // pos_list在JSON中是一个二维数组：[[{x,y},{x,y}...]]
    // 第一层是pos_list数组，第二层是坐标点组（表示一个多边形）
    @Schema(description = "题目坐标信息，二维数组。外层数组表示多个多边形(跨页的情况)，内层数组表示每个多边形的顶点坐标", example = "[[{\"x\":31,\"y\":372},{\"x\":695,\"y\":372},{\"x\":695,\"y\":494},{\"x\":31,\"y\":494}]]")
    private List<List<CutQuestionPosDTO>> pos_list;
}
