package com.joinus.knowledge.model.dto;

import com.joinus.knowledge.enums.OssEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImageData {
    private OssEnum dataS3Enum;
    private String dataS3key;
    private int type;
    private int sortNo;
    private String src;

    public ImageData(OssEnum dataS3Enum, String dataS3key) {
        this.dataS3Enum = dataS3Enum;
        this.dataS3key = dataS3key;
    }

    public ImageData(OssEnum dataS3Enum, String dataS3key, String src) {
        this.dataS3Enum = dataS3Enum;
        this.dataS3key = dataS3key;
        this.src = src;
    }
}
