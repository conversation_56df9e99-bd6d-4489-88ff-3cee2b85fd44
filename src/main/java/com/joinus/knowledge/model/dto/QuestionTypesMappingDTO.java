package com.joinus.knowledge.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionTypesMappingDTO implements Serializable {
    private UUID textbookId;
    private String textbookName;
    private UUID questionId;
    private UUID questionTypeId;
    private String questionTypeName;
    private UUID sectionId;
    private Integer startPage;
    private Integer endPage;
    private Integer pageOffset;
}
