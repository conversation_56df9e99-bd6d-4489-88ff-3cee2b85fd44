package com.joinus.knowledge.model.dto;

import com.joinus.knowledge.enums.OssEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "试卷中一个题目切割坐标")
public class CutExamQuestionDTO {

    @Schema(description = "所属图片ossKey", implementation = List.class, example = "[\"image1.jpg\", \"image2.jpg\"]")
    private String objectName;

    @Schema(description = "oss枚举类", implementation = OssEnum.class, example = "ALIYUN_EDU_KNOWLEDGE_HUB")
    private OssEnum ossEnum;

    @Schema(description = "题目坐标信息，二维数组。外层数组表示多个多边形(跨页的情况)，内层数组表示每个多边形的顶点坐标", example = "[[{\"x\":31,\"y\":372},{\"x\":695,\"y\":372},{\"x\":695,\"y\":494},{\"x\":31,\"y\":494}]]")
    private List<List<CutQuestionPosDTO>> positionList;
}
