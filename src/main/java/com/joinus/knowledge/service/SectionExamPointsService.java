package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.SectionExamPoints;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【section_exam_points】的数据库操作Service
* @createDate 2025-03-06 16:06:22
*/
public interface SectionExamPointsService extends IService<SectionExamPoints> {
    /**
     * 创建小节和考点的关联
     */
    boolean createAssociation(UUID sectionId, UUID examPointId);
    
    /**
     * 批量创建小节和考点的关联（一个小节关联多个考点）
     */
    boolean batchCreateAssociationsBySectionId(UUID sectionId, List<UUID> examPointIds);
    
    /**
     * 批量创建小节和考点的关联（一个考点关联多个小节）
     */
    boolean batchCreateAssociationsByExamPointId(UUID examPointId, List<UUID> sectionIds);
    
    /**
     * 删除小节和考点的关联
     */
    boolean deleteAssociation(UUID sectionId, UUID examPointId);
    
    /**
     * 根据小节ID删除所有关联
     */
    boolean deleteAssociationsBySectionId(UUID sectionId);
    
    /**
     * 根据考点ID删除所有关联
     */
    boolean deleteAssociationsByExamPointId(UUID examPointId);
    
    /**
     * 根据小节ID获取所有关联的考点ID
     */
    List<UUID> getExamPointIdsBySectionId(UUID sectionId);
    
    /**
     * 根据考点ID获取所有关联的小节ID
     */
    List<UUID> getSectionIdsByExamPointId(UUID examPointId);
}
