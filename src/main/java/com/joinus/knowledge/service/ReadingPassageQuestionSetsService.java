package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.ReadingPassageQuestionSets;

import java.util.List;
import java.util.UUID;

public interface ReadingPassageQuestionSetsService extends IService<ReadingPassageQuestionSets> {

    void delete(List<UUID> idList);

    void enable(List<UUID> idList);

    void disable(List<UUID> idList);

    void auditPass(List<UUID> idList);

    void auditNoPass(List<UUID> idList);
}