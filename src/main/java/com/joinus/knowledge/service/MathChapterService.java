package com.joinus.knowledge.service;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathChapter;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.vo.MathChapterVO;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_chapters】的数据库操作Service
* @createDate 2025-02-28 14:12:06
*/
public interface MathChapterService extends IService<MathChapter> {

    List<MathChapter> listAllChaptersByBookId(UUID id);

    List<MathChapterVO> list(String name, Integer grade, Integer semester, PublisherType publisher);
}
