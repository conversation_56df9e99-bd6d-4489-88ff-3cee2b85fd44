package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.KnowledgePointQuestionTypeRelationship;
import com.joinus.knowledge.model.entity.ReadingQuestionAnswers;

import java.util.List;
import java.util.UUID;

public interface KnowledgePointQuestionTypeRelationshipService extends IService<KnowledgePointQuestionTypeRelationship> {

    List<KnowledgePointQuestionTypeRelationship> listByKnowledgePointIds(List<UUID> knowledgePoints);
}