package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.ReadingQuestionAnswers;

import java.util.List;
import java.util.UUID;

public interface ReadingQuestionAnswersService extends IService<ReadingQuestionAnswers> {

    /**
     * 根据题目id获取题目答案
     *
     * @param questionId
     * @return
     */
    ReadingQuestionAnswers getByQuestionId(UUID questionId);

    List<ReadingQuestionAnswers> getFillInTheBlankQuestionAnswers();

}