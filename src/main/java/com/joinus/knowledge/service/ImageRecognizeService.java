package com.joinus.knowledge.service;

import com.aliyun.ocr_api20210707.models.RecognizeEduPaperStructedResponse;
import com.joinus.knowledge.model.param.CutImageFromPositionsParam;
import com.joinus.knowledge.model.param.ErasePenMarksParam;
import com.joinus.knowledge.model.param.GetCoordinateParam;
import com.joinus.knowledge.model.vo.CutQuestionVO;
import com.joinus.knowledge.model.vo.OssFileVO;
import jakarta.validation.Valid;

import java.util.UUID;

public interface ImageRecognizeService {
    String paperCut(String imageUrl, String cutType, String imageType, String subject, Boolean outputOricood);

    RecognizeEduPaperStructedResponse paperStructed(String imageUrl, String subject);

    CutQuestionVO paperStructedFromAli(String imgUrl, String type);

    OssFileVO cutImageFromPositions(CutImageFromPositionsParam param);

    OssFileVO removeHandwriting(ErasePenMarksParam param);

    CutQuestionVO paperStructed(@Valid GetCoordinateParam param);

    void ocrHtml(String imageUrl, UUID fileId);
}
