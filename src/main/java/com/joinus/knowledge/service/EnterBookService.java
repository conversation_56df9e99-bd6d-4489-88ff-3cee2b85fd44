package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.dto.DerivativeFileDTO;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.FileDerivative;
import com.joinus.knowledge.model.entity.Textbooks;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 录书service
* @createDate 2025-03-06 22:57:14
*/
public interface EnterBookService  {

    Textbooks getBookById(UUID id);
    
    /**
     * 根据ID获取文件信息
     * @param id 文件ID
     * @return 文件实体
     */
    File getFileById(UUID id);
    
    /**
     * 检查教材页面是否存在
     * @param textbookId 教材ID
     * @param pageNo 页码
     * @return 是否存在
     */
    boolean checkExistPage(UUID textbookId, int pageNo);
    
    /**
     * 保存文件并创建与教材的关联
     * @param textbookId 教材ID
     * @param fileName 文件名
     * @param fileType 文件类型
     * @param mimeType MIME类型
     * @param ossKey OSS键
     * @param ossEnum OSS枚举
     * @param pageNo 页码
     * @return 文件ID
     */
    UUID saveFile(UUID textbookId, String fileName, String fileType, String mimeType, String ossKey, OssEnum ossEnum, int pageNo);
    
    /**
     * 保存衍生文件
     * @param fileId 主文件ID
     * @param derivativeType 衍生类型
     * @param storagePath 存储路径
     * @param format 格式
     * @param width 宽度
     * @param height 高度
     * @param fileSize 文件大小
     * @return 是否成功
     */
    boolean saveFileDerivative(UUID fileId, String derivativeType, String storagePath, String format, Integer width, Integer height, Long fileSize);
}
