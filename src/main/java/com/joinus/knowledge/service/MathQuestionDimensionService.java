package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.model.entity.Conversation;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathQuestionDimension;
import com.joinus.knowledge.model.po.ContentPart;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionChunk;
import io.reactivex.Flowable;
import org.springframework.ai.chat.prompt.ChatOptions;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.UUID;

public interface MathQuestionDimensionService extends IService<MathQuestionDimension> {


    MathQuestionDimension getDimensionByQuestionId(UUID id);
}
