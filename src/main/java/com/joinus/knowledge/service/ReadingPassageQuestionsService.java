package com.joinus.knowledge.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.ReadingPassageQuestions;
import com.joinus.knowledge.model.param.ReadingPassageParam;
import com.joinus.knowledge.model.param.ReadingPassageQuestionParam;
import com.joinus.knowledge.model.param.ReadingQuestionSetItemEditParam;
import com.joinus.knowledge.model.vo.ReadingPassageQuestionsVO;

import java.util.List;
import java.util.UUID;

public interface ReadingPassageQuestionsService extends IService<ReadingPassageQuestions> {
    /**
     * 根据阅读文章id获取阅读文章题目
     *
     * @param param
     * @return
     */
    List<ReadingPassageQuestionsVO> getQuestions(ReadingPassageParam param);

    void delete(List<UUID> idList);

    void update(ReadingPassageQuestionParam param);

    void batchUpdate(List<ReadingQuestionSetItemEditParam> paramList);

    void enable(List<UUID> idList);

    void disable(List<UUID> idList);

    void auditPass(List<UUID> idList);

    void auditNoPass(List<UUID> idList);


    List<ReadingPassageQuestions> getUnsetQuestions();

    void dealUnsetQuestions();
    void dealChoiceQuestions();
    void dealFillInTheBlankQuestionAnswers();

    void updateQuestionKnowledgePoints(UUID questionId, List<UUID> knowledgeIdList);

    String getAiAuditResult(UUID questionId);
}