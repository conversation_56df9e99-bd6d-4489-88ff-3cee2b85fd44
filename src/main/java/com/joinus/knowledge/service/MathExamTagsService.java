package com.joinus.knowledge.service;

import cn.hutool.json.JSONObject;
import com.joinus.knowledge.enums.RegularExamType;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathExamTag;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.param.AddExamTagByExcelParam;
import com.joinus.knowledge.model.param.AddExamTagParam;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_exam_tags(试卷标签表)】的数据库操作Service
* @createDate 2025-07-21 09:19:09
*/
public interface MathExamTagsService extends IService<MathExamTag> {

    void addExamTags(UUID examId, List<AddExamTagParam> param);

    void addExamTagsByExcel(List<AddExamTagByExcelParam> dataList);

    /**
     * 设置主要别名标签（会将其他 ALIAS 标签的 isPrimary 设为 false）
     */
    void setPrimaryAlias(UUID examId, String aliasValue, JSONObject properties);

    MathExam checkExistByAlias(String examName);

    void addOrUpdateAliases(UUID id, List<String> aliases);

    void addOrUpdateEliteSchoolExamPaperValues(UUID id, List<String> eliteSchoolExamPaperValues);

    void addOrUpdateRegularExamType(UUID id, RegularExamType regularExamType);

    List<MathExamTag> listByExamId(UUID id);

    List<MathExamTag> listByExamIds(List<UUID> examIdList);
}
