package com.joinus.knowledge.service;

import cn.hutool.json.JSONObject;

import java.util.List;

public interface ReadingAIAbilityCallbackAsyncService {

    void aiCorrectCallback(String testId, String questionId, int resultCode);

    void weakKnowledgePointAnalysisCallback(String id, Integer type, List<String> resultList);

    void trainingSuggestionCallback(String id, Integer type, List<String> suggestions);

    void weakQuestionTypeAnalysisCallback(String id, Integer type, List<String> resultList);

    void callbackReadingCommon(String callbackId, Integer readingAIAbilityType, Object result);
}
