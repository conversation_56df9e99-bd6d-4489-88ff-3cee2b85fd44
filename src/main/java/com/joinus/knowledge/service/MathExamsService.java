package com.joinus.knowledge.service;

import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.model.dto.AddExamQuestionParam;
import com.joinus.knowledge.model.entity.MathExam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.po.ExamQuestionPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.model.vo.*;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_exams(试卷表)】的数据库操作Service
* @createDate 2025-03-20 18:15:37
*/
public interface MathExamsService extends IService<MathExam> {

    List<ExamQuestionPO> listQuestionByExamId(UUID examId);

    /**
     * 试卷分页条件查询
     */
    IPage<MathExamVO> pageQuery(Page<MathExamVO> page, MathExamPageQueryParam param);

    List<FileVO> listExamImagesByExamId(UUID id);

    MathExamVO queryExamDetailById(UUID id);

    List<MathExamQuestionVO> listMathExamQuestions(UUID id);

    QuestionDetailVO addMathExamQuestion(UUID examId, AddExamQuestionParam param);

    QuestionDetailVO updateMathExamQuestion(UUID examId, UUID questionId, AddExamQuestionParam param);

    void deleteMathExamQuestion(UUID examId, UUID questionId);

    MathExamVO updateExamDetail(UUID id, UpdateMathExamParam param);

    UUID createMathExam(CreateExamParam param);

    void updateExamByExcel(List<UpdateExamByExcelParam> dataList);

    ExamStateEnum reviewMathExam(ReviewMathExamParam param);

    MathExamVO updateExamInfo(UUID id, UpdateMathExamParam param);

    List<SchoolVO> listSchoolByExamId(UUID id);

    MathExamVO getExamTagsAndInfo(UUID id);

    void updateExamState(UUID id, UpdateMathExamParam param);
}
