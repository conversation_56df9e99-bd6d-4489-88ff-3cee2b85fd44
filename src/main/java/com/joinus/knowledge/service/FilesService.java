package com.joinus.knowledge.service;

import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.entity.File;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.vo.CutQuestionVO;
import com.joinus.knowledge.model.vo.TextbookFileVO;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【files】的数据库操作Service
* @createDate 2025-03-06 16:06:22
*/
public interface FilesService extends IService<File> {

    List<TextbookFileVO> getFilesByBookId(UUID bookId);

    List<Map<String,Object>> getFilesByBookId(String bookId);

    File save(String name, String originalImageType, String originalMimeType, String originalOssObjectName, OssEnum ossEnum);

    List<File> listQuestionOriginImages(UUID questionId);

    String getOssUrl(String name, String ossKey, String ossType, String ossBucket);

    String getOssUrl(String ossKey, OssEnum ossEnum);

    String getSectionHtml(UUID textbookId, Integer startPage, Integer endPage);

    CutQuestionVO getPositions(String ossKey, OssEnum ossEnum);

    List<String> getImgeUrls(List<String> ossKeys, OssEnum ossEnum);
}
