package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.MathAnswer;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.param.UpdateQuestionAnswerParam;
import com.joinus.knowledge.model.vo.QuestionAnswerDetailVO;

import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_answers】的数据库操作Service
* @createDate 2025-02-28 14:12:06
*/
public interface MathAnswersService extends IService<MathAnswer> {

    QuestionAnswerDetailVO updateById(UUID id, UpdateQuestionAnswerParam param);

    QuestionAnswerDetailVO getById(UUID id);

    boolean saveAnswer(MathAnswer mathAnswer);

    boolean saveAnswerAndRelation(UUID questionId, MathAnswer mathAnswer);
}
