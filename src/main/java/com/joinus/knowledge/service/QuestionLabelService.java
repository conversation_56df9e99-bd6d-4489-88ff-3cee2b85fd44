package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.QuestionLabel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_labels】的数据库操作Service
* @createDate 2025-03-26 15:05:21
*/
public interface QuestionLabelService extends IService<QuestionLabel> {

    List<UUID> listByQuestionId(UUID id);

    void deleteRelations(UUID questionId, List<UUID> labelIdList);

    void saveRelations(UUID questionId, List<UUID> labelIdList);
}
