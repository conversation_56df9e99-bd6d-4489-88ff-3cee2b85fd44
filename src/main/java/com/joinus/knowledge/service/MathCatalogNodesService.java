package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.MathCatalogNode;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.vo.MathCatalogNodeVO;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_catalog_nodes(数学目录节点表)】的数据库操作Service
* @createDate 2025-07-30 17:33:40
*/
public interface MathCatalogNodesService extends IService<MathCatalogNode> {

    List<MathCatalogNodeVO> listCatalogNodes(UUID nodeId);
}
