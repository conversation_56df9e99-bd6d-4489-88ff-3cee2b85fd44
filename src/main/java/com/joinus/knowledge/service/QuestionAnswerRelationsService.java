package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.QuestionAnswerRelation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_answers】的数据库操作Service
* @createDate 2025-03-01 10:22:43
*/
public interface QuestionAnswerRelationsService extends IService<QuestionAnswerRelation> {

    void createAssociation(UUID questionId, UUID answerId);

    void deleteAssociation(UUID questionId, UUID answerId);

}
