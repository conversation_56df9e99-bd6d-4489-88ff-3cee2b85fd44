package com.joinus.knowledge.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.ReadingPassages;
import com.joinus.knowledge.model.param.ReadingPassageParam;
import com.joinus.knowledge.model.param.ReadingPassagesParam;
import com.joinus.knowledge.model.vo.ReadingPassageVO;

import java.util.List;
import java.util.UUID;

public interface ReadingPassagesService extends IService<ReadingPassages> {
    void batchCreateReadingPassages();

    void testListMsg();

    void createPassageQuestion(UUID passageId);

    void batchCreatePassageAndQuestion(Integer grade,Integer semester,String unit,Integer num);

    void batchCreateQuestion(Integer grade,Integer semester,String unit,String genreName,UUID passageId, Integer num);

    /**
     * 获取试卷 一篇文章加五个题目
     *
     * @param param 参数
     * @return
     */
    ReadingPassageVO getPassageAndQuestions(ReadingPassageParam param);

    void delete(List<UUID> idList);

    void update(ReadingPassagesParam param);

    void enable(List<UUID> idList);

    void disable(List<UUID> idList);

    ReadingPassages query(UUID id);

    void auditPass(List<UUID> idList);

    void auditNoPass(List<UUID> idList);

    void dealClonePassageAndQuestions();

    void dealUploadData(JSONObject json);
}