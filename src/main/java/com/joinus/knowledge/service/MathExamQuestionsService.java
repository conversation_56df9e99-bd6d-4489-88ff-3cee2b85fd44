package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.MathExamQuestion;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.MathQuestion;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_exam_questions(试卷与题目关联表)】的数据库操作Service
* @createDate 2025-03-20 18:15:37
*/
public interface MathExamQuestionsService extends IService<MathExamQuestion> {

    List<MathQuestion> listQuestionsByExamId(UUID examId);

    MathExamQuestion getOne(UUID examId, UUID questionId);

    void updateSortNo(UUID examId, UUID questionId, Integer sortNo);

    void removeQuestion(UUID examId, UUID questionId);

    void createRelations(UUID id, List<UUID> questionIds);
}
