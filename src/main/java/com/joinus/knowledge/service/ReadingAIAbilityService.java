package com.joinus.knowledge.service;

import com.joinus.knowledge.model.param.ReadingAIAbilityParam;

public interface ReadingAIAbilityService {

    /**
     * AI批改
     */
    ReadingAIAbilityParam aiCorrect(ReadingAIAbilityParam param);

    /**
     * 薄弱知识点分析
     */
    void weakKnowledgePointAnalysis(ReadingAIAbilityParam param);

    /**
     * 综合训练建议
     */
    void trainingSuggestion(ReadingAIAbilityParam param);

    /**
     * 薄弱题型分析
     */
    void weakQuestionTypeAnalysis(ReadingAIAbilityParam param);

    /*
     * 为语文阅读提供AI能力
     */
    void dealByAIAbility(ReadingAIAbilityParam param);
}
