package com.joinus.knowledge.service;

import cn.hutool.json.JSONObject;
import com.joinus.knowledge.model.dto.SchoolExamDTO;
import com.joinus.knowledge.model.vo.SchoolVO;

import java.util.List;
import java.util.UUID;

public interface SmartStudyService {


    void addOrUpdateSchoolExamRelation(UUID id, List<Long> schoolIds);

    void deleteSchoolExamRelation(UUID id);

    List<JSONObject> listSchoolExamRelationByExamId(UUID id);

    List<UUID> listSchoolExamRelationBySchoolId(Long schoolId);

    List<SchoolExamDTO> listSchoolExamRelation(List<UUID> examIdList);
}
