package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.SectionKnowledgePoint;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.po.SectionKnowledgePointPO;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_section_knowledge_points】的数据库操作Service
* @createDate 2025-03-06 16:06:22
*/
public interface SectionKnowledgePointsService extends IService<SectionKnowledgePoint> {
    /**
     * 创建小节和考点的关联
     */
    boolean createAssociation(UUID sectionId, UUID knowledgePointId);

    /**
     * 批量创建小节和考点的关联（一个小节关联多个考点）
     */
    boolean batchCreateAssociationsBySectionId(UUID sectionId, List<UUID> knowledgePointIds);

    /**
     * 批量创建小节和考点的关联（一个考点关联多个小节）
     */
    boolean batchCreateAssociationsByKnowledgePointId(UUID knowledgePointId, List<UUID> sectionIds);

    /**
     * 删除小节和考点的关联
     */
    boolean deleteAssociation(UUID sectionId, UUID knowledgePointId);

    /**
     * 根据小节ID删除所有关联
     */
    boolean deleteAssociationsBySectionId(UUID sectionId);

    /**
     * 根据考点ID删除所有关联
     */
    boolean deleteAssociationsByKnowledgePointId(UUID knowledgePointId);

    /**
     * 根据小节ID获取所有关联的考点ID
     */
    List<UUID> getKnowledgePointIdsBySectionId(UUID sectionId);

    /**
     * 根据考点ID获取所有关联的小节ID
     */
    List<UUID> getSectionIdsByKnowledgePointId(UUID knowledgePointId);

    void createRelation(UUID sectionId, UUID knowledgeId, Integer pageNo);

    void deleteAssociation(UUID id, UUID keypointId, Integer pageNo);

    void removeEntity(SectionKnowledgePointPO sectionKp);
}
