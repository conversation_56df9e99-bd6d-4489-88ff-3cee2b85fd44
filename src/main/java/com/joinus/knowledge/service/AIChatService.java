package com.joinus.knowledge.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.enums.ModelThinkingType;
import com.joinus.knowledge.model.entity.Conversation;
import com.joinus.knowledge.model.po.ContentPart;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionChunk;
import io.reactivex.Flowable;
import org.springframework.ai.chat.prompt.ChatOptions;
import reactor.core.publisher.Flux;

import java.util.List;

public interface AIChatService {



    String chat(String promptText, AIModelType aiModelType);

    String chat(String promptText, String imageUrl, AIModelType aiModelType);

    String chat(String promptText, List<String> imageUrls, AIModelType aiModelType);

    String chat(String promptText, AIModelType aiModelType, ChatOptions chatOptions);

    String chat(String promptText, String imageUrl, AIModelType aiModelType, ChatOptions chatOptions);

    String chat(String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions);

    String chat(String systemPromptText, String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions, JsonNode jsonSchema);

    String chat(String promptText,  AIModelType aiModelType, ChatOptions chatOptions,Conversation conversation);

    String chatWithJsonSchema(String promptText,  AIModelType aiModelType, ChatOptions chatOptions, String jsonSchema);

    String chatWithJsonSchema(String systemPromptText,String promptText,  AIModelType aiModelType, ChatOptions chatOptions, String jsonSchema);


    Flux<String> chatStream(String promptText, List<String> imageUrls, AIModelType aiModelType);

    Flux<String> chatStream(String promptText, AIModelType aiModelType);

    Flux<String> chatStream(String promptText, String imageUrl, AIModelType aiModelType);

    Flux<String> chatStream(String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions);

    Flux<String> chatStream(String promptText, AIModelType aiModelType, ChatOptions chatOptions);

    Flux<String> chatStream(String promptText, String imageUrl, AIModelType aiModelType, ChatOptions chatOptions);

    Flowable<ChatCompletionChunk> chatStreamChunkByVolcegineSdk(String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions);

    String chat(String promptText, AIModelType aiModelType, ChatOptions chatOptions, String extraBody);

    String chatWithContentPart(List<ContentPart> contentParts, AIModelType aiModelType, ChatOptions chatOptions, JsonNode jsonSchema, ModelThinkingType modelThinkingType);

    Flux<String> chatStreamWithContentPart(List<ContentPart> contentParts, AIModelType aiModelType, ChatOptions chatOptions, JsonNode jsonSchema, ModelThinkingType modelThinkingType);
}
