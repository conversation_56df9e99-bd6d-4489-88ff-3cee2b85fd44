package com.joinus.knowledge.service;

import com.joinus.knowledge.enums.ExamFileType;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.dto.*;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.entity.MathAnswer;
import com.joinus.knowledge.model.po.ExamQuestionPO;
import com.joinus.knowledge.model.vo.*;
import jakarta.validation.Valid;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface AIAbilityService {
    String solveMathQuestion(SolveQuestionParam param);

    Flux<String> solveMathQuestionViaStream(SolveQuestionParam param);

    @Transactional(rollbackFor = Exception.class)
    String solveMathQuestionViaContentParts(SolveQuestionParam param);

    Flux<String> solveMathQuestionViaContentPartsStream(UUID questionId);

    MathAnswer parseAnswer(String inputText);

    HashMap<String, UUID> saveExamFileAndRelation(List<String> objectNames, UUID examId, OssEnum ossEnum, ExamFileType originalPaper);

    KnowledgePointsAndDifficultyVO getProblemKnowledgePoints(GenerateKnowledgePointParam param);

    KnowledgePointsAndDifficultyVO getProblemKnowledgePointsViaDB(MathQuestion mathQuestion, Integer grade, Integer semester, PublisherType publisher);

    KnowledgePointsAndDifficultyVO getProblemKnowledgePointsViaAI(MathQuestion mathQuestion, Integer grade, Integer semester, PublisherType publisher);

    KnowledgePointsAndDifficultyVO getProblemKnowledgePointsViaAI(MathQuestion mathQuestion, Integer grade, Integer semester, PublisherType publisher, int attempt);

    List<String> getQuestionOriginImagesById(UUID questionId);

    List<ImageData> getQuestionOriginImageDataById(UUID questionId);

    Boolean checkExistGraphics(String question);

    UUID getSimilarQuestionId(UUID questionId);

    @Transactional(rollbackFor = Exception.class)
    void saveExamQuestions(ExamSummaryDTO examSummaryDTO, MathExam mathExams, List<String> objectNames, List<String> handwritingRemovedObjectNames, OssEnum ossEnum);

    @Transactional(rollbackFor = Exception.class)
    KnowledgePointsAndDifficultyVO saveExamQuestionAnswerAndKnowledgePoints(MathQuestion question, MathExam exam);

    OssFileVO cutQuestionImage(List<CutQuestionPosDTO> positionDTOs, String objectName, OssEnum ossEnum);

    CheckExistExamVO checkExistExamV2(@Valid CheckExistExamParam param);

    @Transactional(rollbackFor = Exception.class)
    UUID cutExam(CutExamParam param);

    MathTrainingHtmlDTO mathSpecialTraining(SpecialTrainingParam param);

    String mathSpecialTrainingWithSeparateAnswers(Map<String, List<ExamQuestionPO>> examId);

    String mathSpecialTrainingAnswersOnly(UUID examId);

    CheckExistExamVO checkExistExamByName(String examName);

    Map<String, String> mathSpecialTrainingToPDF(@Valid SpecialTrainingParam param);

    CheckExamExistKnowledgePointsVO checkExamExistKnowledgePoints(@Valid CheckExistExamKnowledgePointsParam param);

    void ocrQuestionImageAndSaveContent(SolveQuestionParam param);

    UUID uploadMathExam(CutExamParam param);

    void cutQuestionsFromExam(MathQuestion question);

    UUID cutExamV2(CutExamParam param);

    void rebuildQuestionKnowledgePoints(UUID id);

}
