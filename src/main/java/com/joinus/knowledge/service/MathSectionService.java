package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathSection;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.vo.MathSectionVO;
import com.joinus.knowledge.model.vo.QuerySectionVideoVO;
import com.joinus.knowledge.model.vo.SectionKeypointVO;
import com.joinus.knowledge.model.vo.SectionVO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_sections】的数据库操作Service
* @createDate 2025-03-06 16:06:22
*/
public interface MathSectionService extends IService<MathSection> {

    List<MathSection> getByPageNo(Integer pageNo, UUID textbookId, UUID sectionId);

    void updateSectionKeypointMapping(UpdateSectionKeypointParam param);

    void deleteSectionKeypointMapping(@Valid DeleteSectionKeypointParam param);

    List<SectionVO> listAllSectionsByBookId(UUID id);

    List<SectionKeypointVO> listKeypointsById(UUID sectionId);

    void switchKeypointType(@Valid SwitchKeypointTypeParam param);

    UUID combineKeypoints(@Valid CombineKeypointParam param);

    void addKeypoints(@Valid AddSectionKeypointParam param);

    void seperateKnowledgePoints();

    void seperateQuestionTypes();

    void updatePageIndex(@Valid UpdateSectionKeypointParam param);

    List<SectionVO> listByTextbookId(UUID textbookId);

    List<MathSectionVO> list(String name, Integer grade, Integer semester, PublisherType publisher, String chapterName, UUID chapterId);

    List<SectionVO> listAllsectionsByExamId(UUID examId, PublisherType publisher);

    List<SectionVO> listSections(Integer grade, PublisherType publisher, Integer semester);

    List<QuerySectionVideoVO> querySectionVideos(List<UUID> sectionIds);
}
