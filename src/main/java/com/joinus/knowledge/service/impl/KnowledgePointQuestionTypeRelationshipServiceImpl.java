package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.mapper.KnowledgePointQuestionTypeRelationshipMapper;
import com.joinus.knowledge.model.entity.KnowledgePointQuestionTypeRelationship;
import com.joinus.knowledge.service.KnowledgePointQuestionTypeRelationshipService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
public class KnowledgePointQuestionTypeRelationshipServiceImpl extends ServiceImpl<KnowledgePointQuestionTypeRelationshipMapper, KnowledgePointQuestionTypeRelationship>
    implements KnowledgePointQuestionTypeRelationshipService {

    @Override
    public List<KnowledgePointQuestionTypeRelationship> listByKnowledgePointIds(List<UUID> knowledgePoints) {
        return baseMapper.listByKnowledgePointIds(knowledgePoints);
    }
}
