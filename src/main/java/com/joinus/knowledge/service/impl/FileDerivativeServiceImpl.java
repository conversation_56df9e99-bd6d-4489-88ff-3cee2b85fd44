package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.dto.DerivativeFileDTO;
import com.joinus.knowledge.model.dto.TextbookFileDTO;
import com.joinus.knowledge.model.entity.FileDerivative;
import com.joinus.knowledge.service.FileDerivativeService;
import com.joinus.knowledge.mapper.FileDerivativeMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【file_derivatives】的数据库操作Service实现
* @createDate 2025-03-06 22:57:14
*/
@Service
public class FileDerivativeServiceImpl extends ServiceImpl<FileDerivativeMapper, FileDerivative>
    implements FileDerivativeService{

    @Override
    public List<DerivativeFileDTO> listByTextbookId(UUID bookId) {
        return baseMapper.listByTextbookId(bookId);
    }
}




