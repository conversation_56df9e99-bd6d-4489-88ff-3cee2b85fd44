package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.AiChatLog;
import com.joinus.knowledge.service.AiChatLogService;
import com.joinus.knowledge.mapper.AiChatLogMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【ai_chat_log】的数据库操作Service实现
* @createDate 2025-03-28 09:52:52
*/
@Service
public class AiChatLogServiceImpl extends ServiceImpl<AiChatLogMapper, AiChatLog>
    implements AiChatLogService{

}




