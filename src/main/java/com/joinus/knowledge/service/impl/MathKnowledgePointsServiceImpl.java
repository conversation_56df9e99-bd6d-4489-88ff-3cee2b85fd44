package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.mapper.MathKnowledgePointsMapper;
import com.joinus.knowledge.mapper.QuestionKnowledgePointsMapper;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.entity.MathSection;
import com.joinus.knowledge.model.po.MathKnowledgePointPO;
import com.joinus.knowledge.model.param.SectionKnowledgePointParam;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.model.vo.MathQuestionTypeVO;
import com.joinus.knowledge.model.vo.SectionKnowledgePointVO;
import com.joinus.knowledge.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_points】的数据库操作Service实现
* @createDate 2025-02-28 14:12:06
*/
@Service
public class MathKnowledgePointsServiceImpl extends ServiceImpl<MathKnowledgePointsMapper, MathKnowledgePoint>
    implements MathKnowledgePointsService{

    @Autowired
    private QuestionKnowledgePointsMapper questionKnowledgePointsMapper;
    @Autowired
    private MathSectionService mathSectionService;
    @Autowired
    private SectionKnowledgePointsService sectionKnowledgePointsService;
    @Autowired
    private MathQuestionTypesService questionTypesService;

    @Override
    public List<MathKnowledgePoint> listByGradeAndSemester(Integer grade, Integer semester, PublisherType publisher) {
        return baseMapper.listByGradeAndSemester(grade,semester, publisher);
    }

    @Override
    public List<MathKnowledgePoint> listByQuestionId(UUID questionId) {
        return baseMapper.listByQuestionId(questionId);
    }

    @Override
    public List<MathKnowledgePoint> listByPublisher(String publisher) {
        return baseMapper.listByPublisher(publisher);
    }

    @Override
    public List<MathKnowledgePointVO> list(String name, Integer grade, Integer semester, PublisherType publisher, UUID chapterId, String chapterName, UUID sectionId, String sectionName) {
        return baseMapper.list(name, grade, semester, publisher, chapterId, chapterName, sectionId, sectionName);
    }

    @Override
    public List<MathKnowledgePoint> listByTextbookId(UUID textbookId) {
        return baseMapper.listByTextbookId(textbookId);
    }

    @Override
    public List<MathKnowledgePointPO> listByQuestionIds(List<UUID> ids) {
        return baseMapper.listByQuestionIds(ids);
    }

    @Override
    public List<MathKnowledgePointPO> listByQuestionIdsAndPublisher(List<UUID> ids, PublisherType publisher) {
        return baseMapper.listByQuestionIdsAndPublisher(ids, publisher);
    }

    @Override
    public List<MathKnowledgePointPO> listByQuestionIdsAndPublisherAndExamPoint(List<UUID> ids, PublisherType publisher) {
        return baseMapper.listByQuestionIdsAndPublisherAndExamPoint(ids, publisher);
    }

    @Override
    public List<MathKnowledgePointVO> listQuestionTypeByKnowledgePointIds(List<UUID> knowledgePoints) {
        return baseMapper.listQuestionTypeByKnowledgePointIds(knowledgePoints);
    }

    @Override
    public List<SectionKnowledgePointVO> listSectionKnowledgePointByKnowledgeIds(SectionKnowledgePointParam param) {
        return baseMapper.listSectionKnowledgePointByKnowledgeIds(param.getKnowledgePointIds(), param.getGrade(), param.getSemester(), param.getPublisher());
    }

    @Override
    public Map<String, Object> listQuestionTypeByKnowledgePointIdsV2(List<UUID> knowledgePointIds) {

        List<MathKnowledgePointVO> kpResults = new ArrayList<>();
        List<MathKnowledgePointVO> kpVOs = baseMapper.listEnableAiQuestionCountByKnowledgePointIds(knowledgePointIds);
        kpResults = kpVOs.stream().filter(kpVO -> kpVO.getEnableAiQuestionCount() > 0).toList();

        List<MathQuestionTypeVO> qtResults = new ArrayList<>();
        List<MathQuestionTypeVO> questionTypes =questionTypesService.listEnableAiQuestionCountByKnowledgePointIds(knowledgePointIds);
        qtResults = questionTypes.stream().filter(qtVO -> qtVO.getEnableAiQuestionCount() > 0).toList();

        HashMap<String, Object> result = new HashMap<String, Object>();
        result.put("knowledgePoints", kpResults);
        result.put("questionTypes", qtResults);

        return result;
    }

    @Override
    public List<MathKnowledgePointVO> listBySectionIds(List<UUID> sectionIds) {
        return baseMapper.listBySectionIds(sectionIds);
    }

    @Override
    public List<MathKnowledgePointVO> listByIdsAndPublisher(List<UUID> ids, PublisherType publisher, Integer grade, Integer semester) {
        return baseMapper.listByIdsAndPublisher(ids, publisher, grade, semester);
    }

    @Override
    public List<MathKnowledgePointVO> listByIds(List<UUID> ids) {
        return baseMapper.listByIds(ids);
    }

    @Override
    public List<MathKnowledgePointVO> listKnowledgePointByQuestionId(UUID questionId, Integer grade, Integer semester, PublisherType publisher) {
        return baseMapper.listKnowledgePointByQuestionId(questionId, grade, semester, publisher);
    }

}
