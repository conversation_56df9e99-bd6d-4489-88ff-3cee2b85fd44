package com.joinus.knowledge.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.mapper.ReadingPassageQuestionsMapper;
import com.joinus.knowledge.model.entity.ReadingPassageQuestionSetEntries;
import com.joinus.knowledge.model.entity.ReadingPassageQuestionSets;
import com.joinus.knowledge.model.entity.ReadingPassageQuestions;
import com.joinus.knowledge.model.entity.ReadingQuestionAnswers;
import com.joinus.knowledge.model.entity.ReadingQuestionKnowledgePoints;
import com.joinus.knowledge.model.param.ReadingPassageParam;
import com.joinus.knowledge.model.param.ReadingPassageQuestionParam;
import com.joinus.knowledge.model.param.ReadingQuestionSetItemEditParam;
import com.joinus.knowledge.model.vo.ReadingPassageQuestionsVO;
import com.joinus.knowledge.service.ReadingPassageQuestionSetEntriesService;
import com.joinus.knowledge.service.ReadingPassageQuestionSetsService;
import com.joinus.knowledge.service.ReadingPassageQuestionsService;
import com.joinus.knowledge.service.ReadingQuestionAnswersService;
import jakarta.annotation.Resource;
import com.joinus.knowledge.service.ReadingQuestionKnowledgePointsService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ReadingPassageQuestionsServiceImpl
        extends ServiceImpl<ReadingPassageQuestionsMapper, ReadingPassageQuestions>
        implements ReadingPassageQuestionsService {

    @Resource
    private final ReadingQuestionAnswersService readingQuestionAnswersService;
    private final ReadingQuestionKnowledgePointsService readingQuestionKnowledgePointsService;

    @Resource
    private final ReadingPassageQuestionSetEntriesService readingPassageQuestionSetEntriesService;
    @Resource
    private final ReadingPassageQuestionSetsService readingPassageQuestionSetsService;

    @Override
    public List<ReadingPassageQuestionsVO> getQuestions(ReadingPassageParam param) {
        return baseMapper.getQuestions(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassageQuestions> updateWrapper = Wrappers.lambdaUpdate(ReadingPassageQuestions.class)
                .set(BaseEntity::getDeletedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ReadingPassageQuestionParam param) {
        ReadingPassageQuestions readingPassageQuestions = new ReadingPassageQuestions();
        readingPassageQuestions.setId(param.getId());
        //readingPassageQuestions.setQuestionType(param.getQuestionType());
        readingPassageQuestions.setContent(param.getContent());
        readingPassageQuestions.setUpdatedAt(new Date());
        this.updateById(readingPassageQuestions);
        ReadingQuestionAnswers readingQuestionAnswers = readingQuestionAnswersService.getByQuestionId(param.getId());
        readingQuestionAnswers.setContent(param.getAnswerContent());
        readingQuestionAnswers.setAnswer(param.getAnswer());
        readingQuestionAnswers.setUpdatedAt(new Date());
        readingQuestionAnswersService.updateById(readingQuestionAnswers);

        // 修改题目关联知识点
        //this.updateQuestionKnowledgePoints(param.getId(), param.getKnowledgePointIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<ReadingQuestionSetItemEditParam> paramList) {
        for (ReadingQuestionSetItemEditParam param : paramList) {
            ReadingPassageQuestionParam readingPassageQuestionParam = new ReadingPassageQuestionParam();
            readingPassageQuestionParam.setId(param.getId());
            readingPassageQuestionParam.setContent(param.getContent());
            readingPassageQuestionParam.setAnswerContent(param.getAnswerContent());
            readingPassageQuestionParam.setAnswer(param.getAnswer());
            this.update(readingPassageQuestionParam);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassageQuestions> updateWrapper = Wrappers.lambdaUpdate(ReadingPassageQuestions.class)
                .set(ReadingPassageQuestions::getIsEnabled, 1)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disable(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassageQuestions> updateWrapper = Wrappers.lambdaUpdate(ReadingPassageQuestions.class)
                .set(ReadingPassageQuestions::getIsEnabled, 0)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditPass(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassageQuestions> updateWrapper = Wrappers.lambdaUpdate(ReadingPassageQuestions.class)
                .set(ReadingPassageQuestions::getIsAudit, 1)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditNoPass(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassageQuestions> updateWrapper = Wrappers.lambdaUpdate(ReadingPassageQuestions.class)
                .set(ReadingPassageQuestions::getIsAudit, 2)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    public List<ReadingPassageQuestions> getUnsetQuestions() {
        return baseMapper.getUnsetQuestions();
    }

    @Override
    public void dealUnsetQuestions() {
        //获取未绑定套题的题 并按照文章进行分组
        Map<UUID, List<ReadingPassageQuestions>> questionGroupByPassageMap =
                getUnsetQuestions().stream().collect(Collectors.groupingBy(ReadingPassageQuestions::getPassageId));

        //遍历 questionGroupByPassageMap 按文章逐篇处理
        for (Map.Entry<UUID, List<ReadingPassageQuestions>> entry : questionGroupByPassageMap.entrySet()) {
            try {
                UUID passageId = entry.getKey();
                //一篇文章的所有题目
                List<ReadingPassageQuestions> allQuestionList = entry.getValue();
                //allQuestionList 随机排个序
                Collections.shuffle(allQuestionList);
                //遍历 五个题 生成一套题
                List<ReadingPassageQuestionSetEntries> questionSetEntryList = new ArrayList<>();
                List<ReadingPassageQuestionSets> questionSetList = new ArrayList<>();
                for (int i = 0; i < allQuestionList.size(); i += 5) {
                    List<ReadingPassageQuestions> fiveQuestionList = allQuestionList.subList(i, Math.min(i + 5, allQuestionList.size()));
                    //封装ReadingPassageQuestionSets
                    UUID questionSetId = UUID.randomUUID();
                    ReadingPassageQuestionSets readingPassageQuestionSet = new ReadingPassageQuestionSets();
                    //阿拉伯数字转中文数字
                    int i1 = i / 5 + 1;
                    String name = "第" + Convert.numberToChinese(i1,false) + "套";
                    readingPassageQuestionSet.setName(name);
                    readingPassageQuestionSet.setId(questionSetId);
                    readingPassageQuestionSet.setPassageId(passageId);
                    questionSetList.add(readingPassageQuestionSet);
                    //封装ReadingPassageQuestionSetEntries
                    for (int j=0;j<fiveQuestionList.size();j++) {
                        ReadingPassageQuestions question = fiveQuestionList.get(j);

                        ReadingPassageQuestionSetEntries questionSetEntry = new ReadingPassageQuestionSetEntries();
                        questionSetEntry.setQuestionId(question.getId());
                        questionSetEntry.setPassageId(question.getPassageId());
                        questionSetEntry.setSetId(questionSetId);
                        questionSetEntry.setOrderNo(j + 1);

                        questionSetEntryList.add(questionSetEntry);
                    }
                }
                readingPassageQuestionSetsService.saveBatch(questionSetList);
                readingPassageQuestionSetEntriesService.saveBatch(questionSetEntryList);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
    }
    @Override
    public void dealChoiceQuestions() {
        List<ReadingPassageQuestions> choiceQuestionsList = baseMapper.selectList(Wrappers.lambdaQuery(ReadingPassageQuestions.class)
                .eq(ReadingPassageQuestions::getQuestionType, "CHOICE")
//                .eq(ReadingPassageQuestions::getId, UUID.fromString("8fe599ba-7ae0-4ec5-8925-4d6876f8569f"))
        );
        for (ReadingPassageQuestions question : choiceQuestionsList) {
            String content = question.getContent();
            if (StrUtil.isNotEmpty(content)) {
                JSONObject contentJson = JSONUtil.parseObj(content);
                //{"选项": ["A:拟人", "B:比喻", "C:夸张", "D:排比"], "问题": "文中\"淡黄的小花躲在叶子后面，像怕羞的小姑娘用帕子遮着脸\"运用了哪种修辞手法？"}
                //{"选项": ["A.比喻", "B.拟人", "C.夸张", "D.排比"], "问题": "文中'热油在锅里滋啦滋啦跳华尔兹'运用了："}
                JSONArray optionArray = contentJson.getJSONArray("选项");
                //只处理上述特定格式的 字符串数组 大写字母开头 后面跟 冒号 点号 或者空格
                if (optionArray == null || optionArray.isEmpty() || !optionArray.get(0).toString().matches("[A-Z][:.\\s].*")) {
                    continue;
                }
                //处理字符串数组 改成 [{"key":"A","value":"拟人"},{},{},{}]
                List<Map<String, String>> optionList = new ArrayList<>();
                for (Object option : optionArray) {
                    String optionStr = (String) option;
                    String key = optionStr.substring(0, 1);
                    String value = optionStr.substring(2);
                    Map<String, String> optionMap = new HashMap<>();
                    optionMap.put("key", key);
                    optionMap.put("value", value);
                    optionList.add(optionMap);
                }
                contentJson.set("选项", optionList);
                question.setContent(contentJson.toString()); //此行代码有无必要？
                this.updateById(question);

            }

        }
    }
    @Override
    public void dealFillInTheBlankQuestionAnswers() {
        //{"答案": [{"答案": "不规则/凌乱", "空格标识": "<cloze:1>"}, {"答案": "量身高", "空格标识": "<cloze:2>"}]}
        readingQuestionAnswersService.getFillInTheBlankQuestionAnswers().forEach(questionAnswer -> {
            try {
                String answer = questionAnswer.getAnswer();
                if (StrUtil.isNotEmpty(answer)) {
                    JSONObject answerJson = JSONUtil.parseObj(answer);
                    if (answerJson.get("答案") instanceof JSONArray) {
                        JSONArray detailAnswerArray = answerJson.getJSONArray("答案");
                        if (detailAnswerArray == null || detailAnswerArray.isEmpty()) {
                            return;
                        }
                        //将答案按空格标识排序 后 转成 | 隔开的字符串
                        detailAnswerArray.sort(Comparator.comparingInt(o -> Integer.parseInt(((JSONObject) o).getStr("空格标识").substring(7, 8))));
                        String answerStr = detailAnswerArray.stream().map(o -> ((JSONObject) o).getStr("答案")).collect(Collectors.joining("|"));
                        answerJson.set("答案",answerStr);
                        questionAnswer.setAnswer(answerJson.toString());
                        readingQuestionAnswersService.updateById(questionAnswer);
                    }
                }
            } catch (RuntimeException e) {
                log.error(e.getMessage());
            }
        });
    }

    public static void main(String[] args) {
        System.out.println(Convert.numberToChinese(2,false));
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateQuestionKnowledgePoints(UUID questionId, List<UUID> knowledgeIdList) {
        // 先删除原来的关联
        LambdaUpdateWrapper<ReadingPassageQuestions> updateWrapper = Wrappers.lambdaUpdate(ReadingPassageQuestions.class)
                .set(ReadingPassageQuestions::getId, questionId);
        // 再插入新的关联
        List<ReadingQuestionKnowledgePoints> newKnowledgePoints = knowledgeIdList.stream().map(knowledgeId -> {
            ReadingQuestionKnowledgePoints readingQuestionKnowledgePoints = new ReadingQuestionKnowledgePoints();
            readingQuestionKnowledgePoints.setQuestionId(questionId);
            readingQuestionKnowledgePoints.setKnowledgePointId(knowledgeId);
            return readingQuestionKnowledgePoints;
        }).collect(Collectors.toList());
        readingQuestionKnowledgePointsService.saveBatch(newKnowledgePoints);
    }

    @Override
    public String getAiAuditResult(UUID questionId) {
        if (questionId != null) {
            HashMap<String, Object> aiAuditResult = this.baseMapper.getAiAuditResult(questionId);
            return aiAuditResult != null ? JSONUtil.toJsonStr(aiAuditResult) : null;
        }
        return null;
    }
}