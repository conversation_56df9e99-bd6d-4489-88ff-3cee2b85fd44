package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.MathQuestionType;
import com.joinus.knowledge.model.dto.QuestionTypesMappingDTO;
import com.joinus.knowledge.model.entity.QuestionTypesMapping;
import com.joinus.knowledge.service.QuestionTypesMappingService;
import com.joinus.knowledge.mapper.QuestionTypesMappingMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_question_type_questions】的数据库操作Service实现
* @createDate 2025-03-01 10:22:43
*/
@Service
public class QuestionTypesMappingServiceImpl extends ServiceImpl<QuestionTypesMappingMapper, QuestionTypesMapping>
    implements QuestionTypesMappingService{

    @Override
    public boolean createAssociation(UUID questionId, UUID questionTypeId) {

        QuestionTypesMapping questionTypesMapping = this.lambdaQuery()
                .eq(QuestionTypesMapping::getQuestionId, questionId)
                .eq(QuestionTypesMapping::getQuestionTypeId, questionTypeId)
                .one();
        if (null == questionTypesMapping) {
            QuestionTypesMapping mapping = new QuestionTypesMapping();
            mapping.setQuestionId(questionId);
            mapping.setQuestionTypeId(questionTypeId);
            return this.save(mapping);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean batchCreateAssociationsByQuestionId(UUID questionId, List<UUID> questionTypeIds) {
        if (questionTypeIds == null || questionTypeIds.isEmpty()) {
            return false;
        }

        List<QuestionTypesMapping> mappings = questionTypeIds.stream()
                .map(typeId -> {
                    QuestionTypesMapping mapping = new QuestionTypesMapping();
                    mapping.setQuestionId(questionId);
                    mapping.setQuestionTypeId(typeId);
                    return mapping;
                })
                .collect(Collectors.toList());
        
        return this.saveBatch(mappings);
    }

    @Override
    @Transactional
    public boolean batchCreateAssociationsByQuestionTypeId(UUID questionTypeId, List<UUID> questionIds) {
        if (questionIds == null || questionIds.isEmpty()) {
            return false;
        }

        List<QuestionTypesMapping> mappings = questionIds.stream()
                .map(qId -> {
                    QuestionTypesMapping mapping = new QuestionTypesMapping();
                    mapping.setQuestionId(qId);
                    mapping.setQuestionTypeId(questionTypeId);
                    return mapping;
                })
                .collect(Collectors.toList());
        
        return this.saveBatch(mappings);
    }

    @Override
    public boolean deleteAssociation(UUID questionId, UUID questionTypeId) {
        LambdaQueryWrapper<QuestionTypesMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionTypesMapping::getQuestionId, questionId)
                .eq(QuestionTypesMapping::getQuestionTypeId, questionTypeId);
        return this.remove(queryWrapper);
    }

    @Override
    public boolean deleteAssociationsByQuestionId(UUID questionId) {
        LambdaQueryWrapper<QuestionTypesMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionTypesMapping::getQuestionId, questionId);
        return this.remove(queryWrapper);
    }

    @Override
    public boolean deleteAssociationsByQuestionTypeId(UUID questionTypeId) {
        LambdaQueryWrapper<QuestionTypesMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionTypesMapping::getQuestionTypeId, questionTypeId);
        return this.remove(queryWrapper);
    }

    @Override
    public List<UUID> listQuestionTypeIdsByQuestionId(UUID questionId) {
        LambdaQueryWrapper<QuestionTypesMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionTypesMapping::getQuestionId, questionId);
        
        return this.list(queryWrapper).stream()
                .map(QuestionTypesMapping::getQuestionTypeId)
                .collect(Collectors.toList());
    }

    @Override
    public List<UUID> listQuestionIdsByQuestionTypeId(UUID questionTypeId) {
        List<QuestionTypesMapping> mappings = this.baseMapper.selectByQuestionTypeId(questionTypeId);

        return mappings.stream()
                .map(QuestionTypesMapping::getQuestionId)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<QuestionTypesMapping> listQuestionTypeMappings(List<UUID> questionIds) {
        return lambdaQuery().in(QuestionTypesMapping::getQuestionId, questionIds).list();
    }

    @Override
    public List<QuestionTypesMappingDTO> listQuestionTypesMappingDTOByQuestionId(UUID questionId) {
        return baseMapper.listQuestionTypesMappingDTOByQuestionId(questionId);
    }
}
