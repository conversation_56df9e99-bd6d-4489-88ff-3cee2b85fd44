package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.model.dto.QuestionKnowledgePointDTO;
import com.joinus.knowledge.model.dto.QuestionTypesMappingDTO;
import com.joinus.knowledge.model.dto.pdf.*;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.vo.MathAnswerVO;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.model.vo.MathQuestionTypeVO;
import com.joinus.knowledge.model.vo.SectionVO;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.utils.ConverterUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class OnlyOnceServiceImpl {

    @Resource
    private TextbooksService textbooksService;
    @Resource
    private TextbookFileService textbookFileService;
    @Resource
    private FilesService filesService;
    @Resource
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Resource
    private MathQuestionTypesService mathQuestionTypesService;
    @Resource
    private SectionKnowledgePointsService sectionKnowledgePointsService;
    @Resource
    private SectionQuestionTypesService sectionQuestionTypesService;
    @Resource
    private MathSectionService mathSectionService;
    @Resource
    private AIChatService aiChatService;
    @Resource
    private MathQuestionsService mathQuestionsService;
    @Resource
    private QuestionKnowledgePointsService questionKnowledgePointsService;
    @Resource
    private QuestionTypesMappingService questionTypesMappingService;
    @Autowired
    private MathChapterService mathChapterService;
    @Autowired
    private MathAnswersService mathAnswersService;

    /**
     * 更新知识点、题型、考点的页码
     */
    public void updatePageIndex(UUID textbookId) {
        Textbooks textbook = textbooksService.getById(textbookId);
        Integer bookPageOffset = textbook.getPageOffset();
        // 查询一本书所有的小节
        List<SectionVO> sectionList = mathSectionService.listByTextbookId(textbookId);

        // 所有的知识点和小节关联关系
        List<SectionKnowledgePoint> sectionKnowledgePoints = sectionKnowledgePointsService.lambdaQuery()
                .in(SectionKnowledgePoint::getSectionId, sectionList.stream().map(SectionVO::getSectionId).toList())
                .list();

        // 所有的题型和小节关联关系
        List<SectionQuestionType> sectionQuestionTypes = sectionQuestionTypesService.lambdaQuery()
                .in(SectionQuestionType::getSectionId, sectionList.stream().map(SectionVO::getSectionId).toList())
                .list();

        // 转换成key为sectionId，value为知识点id的list的map
        Map<UUID, List<UUID>> sectionKnowledgePointMap = sectionKnowledgePoints.stream()
                .collect(Collectors.groupingBy(SectionKnowledgePoint::getSectionId, Collectors.mapping(SectionKnowledgePoint::getKnowledgePointId, Collectors.toList())));
        Map<UUID, List<UUID>> sectionQuestionTypeMap = sectionQuestionTypes.stream()
                .collect(Collectors.groupingBy(SectionQuestionType::getSectionId, Collectors.mapping(SectionQuestionType::getQuestionTypeId, Collectors.toList())));

        // 循环每个小节
        for (SectionVO sectionVO : sectionList) {
            UUID sectionId = sectionVO.getSectionId();
            // 查询这个小节所有的页面
            List<TextbookFile> textbookFileList = textbookFileService.lambdaQuery()
                    .eq(TextbookFile::getTextbookId, textbookId)
                    .between(TextbookFile::getPageNo, sectionVO.getStartPage() + bookPageOffset, sectionVO.getEndPage() + bookPageOffset)
                    .orderByAsc(TextbookFile::getPageNo)
                    .list();

            List<File> fileList = filesService.lambdaQuery()
                    .in(File::getId, textbookFileList.stream().map(TextbookFile::getFileId).toList())
                    .list();

            Map<UUID, Integer> fileIdPageMap = textbookFileList.stream().collect(Collectors.toMap(TextbookFile::getFileId, TextbookFile::getPageNo));

            // 查询这个小节相关的知识点/考点和题型
            List<MathKnowledgePoint> mathKnowledgePointList = mathKnowledgePointsService.lambdaQuery()
                    .in(MathKnowledgePoint::getId, sectionKnowledgePointMap.get(sectionId))
                    .list();
            List<MathQuestionType> mathQuestionTypeList = mathQuestionTypesService.lambdaQuery()
                    .in(MathQuestionType::getId, sectionQuestionTypeMap.get(sectionId))
                    .list();

            Map<String, UUID> knowledgePointMap = mathKnowledgePointList.stream().collect(Collectors.toMap(MathKnowledgePoint::getName, MathKnowledgePoint::getId));
            Map<String, UUID> questionTypeMap = mathQuestionTypeList.stream().collect(Collectors.toMap(MathQuestionType::getName, MathQuestionType::getId));

            // 循环这个小节的每一页，匹配这一页的知识点/考点/题型，更新关系表的页码
            for (File file : fileList) {
                //updateKnowledgePointPage(file, mathKnowledgePointList, fileIdPageMap, knowledgePointMap, sectionKnowledgePoints, sectionId);
                //updateQuestionTypePage(file, mathQuestionTypeList, fileIdPageMap, questionTypeMap, sectionQuestionTypes, sectionId);

                String promptTemplate = """
                    {}
                    以上这个页面的html内容，请提取此页面<h2>标签中知识点、题型、考点，按以下json数组格式输出
                        {
                            "知识点": [
                                "知识点1"
                            ],
                            "题型": [
                                "题型1",
                                "题型2"
                            ],
                            "考点": [
                                "考点1"
                            ]
                        }
                    """;
                ChatOptions chatOptions = ChatOptions.builder().temperature(0D).build();
                String result = aiChatService.chat(StrUtil.format(promptTemplate, file.getOcrHtml()), AIModelType.GEMINI_2_5_FLASH, chatOptions);
                String jsonResult = ConverterUtils.convertToJsonStr(result);
                jsonResult = ConverterUtils.fixInvalidBackslashesInJson(jsonResult);
                JSONObject jsonObject = JSONUtil.parseObj(jsonResult);
                Integer pageNo = fileIdPageMap.get(file.getId());
                log.info("第{}页有以下几个关键点\n{}", pageNo, jsonObject);
            }
        }

    }

    private void updateKnowledgePointPage(File file, List<MathKnowledgePoint> knowledgePointList, Map<UUID, Integer> fileIdPageMap, Map<String, UUID> knowledgePointMap, List<SectionKnowledgePoint> sectionKnowledgePoints, UUID sectionId) {
        String promptTemplate = """
            {}
            以上这个页面的html内容，判断以下哪个知识点或考点在这个页面的h2标签中
            {}
            请将出现在此页面的知识点按以下json数组格式给出
            ["知识点1","知识点2"]
            """;
        ChatOptions chatOptions = ChatOptions.builder().temperature(0D).build();
        String result = aiChatService.chat(StrUtil.format(promptTemplate, file.getOcrHtml(), knowledgePointList.stream().map(MathKnowledgePoint::getName).toList()), AIModelType.JYSD_QWEN_3, chatOptions);
        String jsonResult = ConverterUtils.convertToJsonStr(result);
        jsonResult = ConverterUtils.fixInvalidBackslashesInJson(jsonResult);
        JSONArray jsonArray = JSONUtil.parseArray(jsonResult);
        Integer pageNo = fileIdPageMap.get(file.getId());

        // 循环出现的知识点
        jsonArray.forEach(item -> {
            String knowledgePointName = item.toString();
            UUID knowledgePointId = knowledgePointMap.get(knowledgePointName);
            sectionKnowledgePoints.stream()
                    .filter(sectionKnowledgePoint -> sectionKnowledgePoint.getKnowledgePointId().equals(knowledgePointId) && sectionKnowledgePoint.getSectionId().equals(sectionId))
                    .findFirst()
                    .ifPresent(sectionKnowledgePoint -> {
                        if (!sectionKnowledgePoint.getPageIndex().equals(pageNo)) {
                            log.info("知识点/考点[{}]所在页码从第{}页修改至第{}页", knowledgePointName, sectionKnowledgePoint.getPageIndex(), pageNo);
                            /*sectionKnowledgePointsService.deleteAssociation(sectionId, knowledgePointId);
                            sectionKnowledgePointsService.createRelation(sectionId, knowledgePointId, pageNo);*/
                        }
                    });
        });
    }

    private void updateQuestionTypePage(File file, List<MathQuestionType> questionTypeList, Map<UUID, Integer> fileIdPageMap, Map<String, UUID> questionTypeMap, List<SectionQuestionType> sectionQuestionTypes, UUID sectionId) {
        String promptTemplate = """
            {}
            以上这个页面的html内容，判断以下哪个题型在这个页面的h2标签中
            {}
            请将出现在此页面的题型按以下json数组格式给出
            ["题型1","题型2"]
            """;
        ChatOptions chatOptions = ChatOptions.builder().temperature(0D).build();
        String result = aiChatService.chat(StrUtil.format(promptTemplate, file.getOcrHtml(), questionTypeList.stream().map(MathQuestionType::getName).toList()), AIModelType.JYSD_QWEN_3, chatOptions);
        String jsonResult = ConverterUtils.convertToJsonStr(result);
        jsonResult = ConverterUtils.fixInvalidBackslashesInJson(jsonResult);
        JSONArray jsonArray = JSONUtil.parseArray(jsonResult);
        Integer pageNo = fileIdPageMap.get(file.getId());

        // 循环出现的题型
        jsonArray.forEach(item -> {
            String questionTypeName = item.toString();
            UUID questionTypeId = questionTypeMap.get(questionTypeName);
            sectionQuestionTypes.stream()
                    .filter(sectionQuestionType -> sectionQuestionType.getQuestionTypeId().equals(questionTypeId) && sectionQuestionType.getSectionId().equals(sectionId))
                    .findFirst()
                    .ifPresent(sectionQuestionType -> {
                        if (!sectionQuestionType.getPageIndex().equals(pageNo)) {
                            log.info("题型[{}]所在页码从第{}页修改至第{}页", questionTypeName, sectionQuestionType.getPageIndex(), pageNo);
                            /*sectionQuestionTypesService.deleteRelation(sectionId, questionTypeId, sectionQuestionType.getPageIndex());
                            sectionKnowledgePointsService.createRelation(sectionId, questionTypeId, pageNo);*/
                        }
                    });
        });
    }


    /**
     * 修复重复的知识点与题目的关系
     */
    public void fixMultiQuestionKnowledgePointRelation() {
        //查询BOOK来源有多个知识点关系的题目
        List<MathQuestion> mathQuestions = mathQuestionsService.listMultiKnowledgePointQuestionFromBook();
        for (MathQuestion mathQuestion : mathQuestions) {
            List<QuestionKnowledgePointDTO> questionKnowledgePointDTOList = questionKnowledgePointsService.listKnowledgePointDTOByQuestionId(mathQuestion.getId());
            for (QuestionKnowledgePointDTO dto : questionKnowledgePointDTOList) {
                try {
                    //查询这个知识点关联的小节的所有页面的html拼接结果
                    Integer startPage = dto.getStartPage() + dto.getPageOffset();
                    Integer endPage = dto.getEndPage() + dto.getPageOffset();
                    String sectionHtml = filesService.getSectionHtml(dto.getTextbookId(), startPage, endPage);
                    String promptTemplate = """
                        - 题目:{}
                        判断以上这道题是否在以下页面中，请回答"是"或者"否"。
                        {}
                        """;
                    String prompt = StrUtil.format(promptTemplate, mathQuestion.getContent(), sectionHtml);
                    ChatOptions chatOptions = ChatOptions.builder().temperature(0D).build();
                    String result = aiChatService.chat(prompt, AIModelType.GEMINI_2_5_FLASH, chatOptions);
                    if (result.endsWith("是")) {
                        log.info("修复题目知识点关系1:题目 {} 属于{}第{}到{}页中知识点【{}】下例题", dto.getQuestionId(), dto.getTextbookName(), startPage, endPage, dto.getKnowledgePointName());
                    } else if (result.endsWith("否")) {
                        log.info("修复题目知识点关系2:DELETE FROM math_knowledge_point_questions WHERE question_id = '{}' AND knowledge_point_id = '{}';", dto.getQuestionId(), dto.getKnowledgePointId());
                        questionKnowledgePointsService.deleteAssociation(dto.getQuestionId(), dto.getKnowledgePointId());
                    } else {
                        log.info("修复题目知识点关系3:输出不合法,输出结果[{}]", result);
                    }
                } catch (Exception e) {
                    log.error("修复题目知识点关系报错：{}", e.getMessage());
                }
            }
        }
    }


    public void fixMultiQuestionTypesMapping() {
        //查询BOOK来源有多个题型关系的题目
        List<MathQuestion> mathQuestions = mathQuestionsService.listMultiQuestionTypesMappingFromBook();
        for (MathQuestion mathQuestion : mathQuestions) {
            List<QuestionTypesMappingDTO> questionTypesMappingDTOList = questionTypesMappingService.listQuestionTypesMappingDTOByQuestionId(mathQuestion.getId());
            for (QuestionTypesMappingDTO dto : questionTypesMappingDTOList) {
                try {
                    //查询这个知识点关联的小节的所有页面的html拼接结果
                    Integer startPage = dto.getStartPage() + dto.getPageOffset();
                    Integer endPage = dto.getEndPage() + dto.getPageOffset();
                    String sectionHtml = filesService.getSectionHtml(dto.getTextbookId(), startPage, endPage);
                    String promptTemplate = """
                        - 题目:{}
                        判断以上这道题是否在以下页面中，请回答"是"或者"否"。
                        {}
                        """;
                    String prompt = StrUtil.format(promptTemplate, mathQuestion.getContent(), sectionHtml);
                    ChatOptions chatOptions = ChatOptions.builder().temperature(0D).build();
                    String result = aiChatService.chat(prompt, AIModelType.GEMINI_2_5_FLASH, chatOptions);
                    if (result.endsWith("是")) {
                        log.info("修复题目题型关系1:题目 {} 属于{}第{}到{}页中题型【{}】下例题", dto.getQuestionId(), dto.getTextbookName(), startPage, endPage, dto.getQuestionTypeName());
                    } else if (result.endsWith("否")) {
                        log.info("修复题目题型关系2:DELETE FROM math_question_type_questions WHERE question_id = '{}' AND question_type_id = '{}';", dto.getQuestionId(), dto.getQuestionTypeId());
                        questionTypesMappingService.deleteAssociation(dto.getQuestionId(), dto.getQuestionTypeId());
                    } else {
                        log.info("修复题目题型关系3:输出不合法,输出结果[{}]", result);
                    }
                } catch (Exception e) {
                    log.error("修复题目题型关系报错：{}", e.getMessage());
                }
            }
        }
    }

    public PdfParam generatePdfParameters(UUID chapterId) {
        AtomicInteger totalQuestionCount = new AtomicInteger(0);
        AtomicInteger totalKnowledgePointCount = new AtomicInteger(0);
        AtomicInteger totalQuestionTypeCount = new AtomicInteger(0);
        MathChapter mathChapter = mathChapterService.getById(chapterId);
        Textbooks book = textbooksService.getById(mathChapter.getTextbookId());
        List<PdfBook> books = Stream.of(book)
                .map(textbook -> {
                    PdfBook pdfBook = new PdfBook();
                    BeanUtil.copyProperties(textbook, pdfBook);
                    pdfBook.setBookStr(textbook.getName());
                    List<PdfChapter> chapterList = Stream.of(mathChapter).map(chapter -> {
                        PdfChapter pdfChapter = new PdfChapter();
                        pdfChapter.setBookStr(textbook.getName());
                        pdfChapter.setChapterId(chapter.getId().toString());
                        pdfChapter.setChapterSortNo(chapter.getSortNo());
                        pdfChapter.setChapterName(chapter.getName());
                        List<MathSection> sections = mathSectionService.lambdaQuery()
                                .eq(MathSection::getChapterId, chapter.getId())
                                .orderByAsc(MathSection::getSortNo)
                                .list();
                        List<PdfSection> sectionList = sections.stream()
                                .map(section -> {
                                    PdfSection pdfSection = new PdfSection();
                                    pdfSection.setChapterId(chapter.getId().toString());
                                    pdfSection.setSectionId(section.getId().toString());
                                    pdfSection.setSectionSortNo(section.getSortNo());
                                    pdfSection.setSectionName(section.getSectionName());
                                    List<MathKnowledgePointVO> knowledgePoints = mathKnowledgePointsService.listBySectionIds(List.of(section.getId()));
                                    totalKnowledgePointCount.addAndGet(knowledgePoints.size());
                                    List<PdfKnowledgePoint> knowledgePointList = knowledgePoints.stream()
                                            .map(knowledgePoint -> {
                                                PdfKnowledgePoint pdfKnowledgePoint = new PdfKnowledgePoint();
                                                pdfKnowledgePoint.setKnowledgePointId(knowledgePoint.getId().toString());
                                                pdfKnowledgePoint.setKnowledgePointName(knowledgePoint.getName());
                                                pdfKnowledgePoint.setSectionId(section.getId().toString());
                                                List<MathQuestion> questions = mathQuestionsService.listAvailableAIQuestionsByKnowledgePointId(knowledgePoint.getId());
                                                PdfQuestion pdfQuestion = questions.stream().findFirst().map(question -> {
                                                    PdfQuestion pQuestion = new PdfQuestion();
                                                    pQuestion.setSectionId(section.getId().toString());
                                                    pQuestion.setKnowledgePointId(knowledgePoint.getId().toString());
                                                    pQuestion.setKnowledgePointName(knowledgePoint.getName());
                                                    pQuestion.setQuestionId(question.getId().toString());
                                                    pQuestion.setQuestionContent(question.getContent());
                                                    pQuestion.setSortNo(totalQuestionCount.get() + 1);
                                                    List<MathAnswerVO> mathAnswers = mathQuestionsService.listAnswersByQuestionId(question.getId());
                                                    if (!mathAnswers.isEmpty()) {
                                                        pQuestion.setAnswer(mathAnswers.getFirst().getAnswer());
                                                        pQuestion.setAnalyzeContent(mathAnswers.getFirst().getContent());
                                                    }
                                                    return pQuestion;
                                                }).orElse(null);
                                                if (pdfQuestion != null) {
                                                    totalQuestionCount.getAndIncrement();
                                                }
                                                pdfKnowledgePoint.setQuestionList(pdfQuestion == null? List.of() : List.of(pdfQuestion));
                                                return pdfKnowledgePoint;
                                            }).toList();
                                    pdfSection.setKnowledgePoints(knowledgePointList);
                                    List<MathQuestionTypeVO> questionTypes = mathQuestionTypesService.list(null, null, null, null, null ,null, section.getId(), null);
                                    totalQuestionTypeCount.addAndGet(questionTypes.size());
                                    List<PdfQuestionType> questionTypeList = questionTypes.stream()
                                            .map(questionType -> {
                                                PdfQuestionType pdfQuestionType = new PdfQuestionType();
                                                pdfQuestionType.setQuestionTypeId(questionType.getId().toString());
                                                pdfQuestionType.setQuestionTypeName(questionType.getName());
                                                pdfQuestionType.setSectionId(section.getId().toString());
                                                List<MathQuestion> questions = mathQuestionsService.listAvailableAIQuestionsByQuestionTypeId(questionType.getId());
                                                PdfQuestion pdfQuestion = questions.stream().findFirst().map(question -> {
                                                    PdfQuestion pQuestion = new PdfQuestion();
                                                    pQuestion.setSectionId(section.getId().toString());
                                                    pQuestion.setQuestionTypeId(questionType.getId().toString());
                                                    pQuestion.setQuestionType(questionType.getName());
                                                    pQuestion.setQuestionId(question.getId().toString());
                                                    pQuestion.setQuestionContent(question.getContent());
                                                    pQuestion.setSortNo(totalQuestionCount.get() + 1);
                                                    List<MathAnswerVO> mathAnswers = mathQuestionsService.listAnswersByQuestionId(question.getId());
                                                    if (!mathAnswers.isEmpty()) {
                                                        pQuestion.setAnswer(mathAnswers.getFirst().getAnswer());
                                                        pQuestion.setAnalyzeContent(mathAnswers.getFirst().getContent());
                                                    }
                                                    return pQuestion;
                                                }).orElse(null);
                                                if (pdfQuestion != null) {
                                                    totalQuestionCount.getAndIncrement();
                                                }
                                                pdfQuestionType.setQuestionList(pdfQuestion == null? List.of() : List.of(pdfQuestion));
                                                return pdfQuestionType;
                                            }).toList();
                                    pdfSection.setQuestionTypes(questionTypeList);
                                    return pdfSection;
                                }).toList();
                        pdfChapter.setSectionList(sectionList);
                        return pdfChapter;
                    }).toList();
                    pdfBook.setChapterList(chapterList);
                    return pdfBook;
                })
                .toList();

        PdfParam pdfParam = new PdfParam();
        pdfParam.setBookList(books);
        pdfParam.setTotalQuestions(totalQuestionCount.get());
        pdfParam.setTotalQuestionTypes(totalQuestionTypeCount.get());
        pdfParam.setTotalKnowledgePoints(totalKnowledgePointCount.get());
        pdfParam.setReportName(StrUtil.format("{} {}",book.getName(), mathChapter.getName()));
        pdfParam.setPaperName(pdfParam.getReportName());
        pdfParam.setPaperSn("test");
        pdfParam.setPaperType("题型和知识点");
        pdfParam.setContents(List.of("1","3","4"));
        pdfParam.setType("1");
        pdfParam.setExamId("08ebb090-4cda-4365-9b18-227585dc6325");
        return pdfParam;

    }
}
