package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.MathCatalogNode;
import com.joinus.knowledge.model.vo.MathCatalogNodeVO;
import com.joinus.knowledge.service.MathCatalogNodesService;
import com.joinus.knowledge.mapper.MathCatalogNodesMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_catalog_nodes(数学目录节点表)】的数据库操作Service实现
* @createDate 2025-07-30 17:33:40
*/
@Service
public class MathCatalogNodesServiceImpl extends ServiceImpl<MathCatalogNodesMapper, MathCatalogNode>
    implements MathCatalogNodesService{

    @Override
    public List<MathCatalogNodeVO> listCatalogNodes(UUID nodeId) {
        List<MathCatalogNode> nodes = baseMapper.listCatalogNodes(nodeId);

        // 转换为VO列表
        List<MathCatalogNodeVO> nodeVOs = nodes.stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());

        // 构建树形结构
        // 先建立id到节点的映射，方便查找
        java.util.Map<UUID, MathCatalogNodeVO> nodeMap = new java.util.HashMap<>();
        for (MathCatalogNodeVO vo : nodeVOs) {
            nodeMap.put(vo.getId(), vo);
        }

        // 构建父子关系
        List<MathCatalogNodeVO> result = new java.util.ArrayList<>();
        for (MathCatalogNodeVO vo : nodeVOs) {
            UUID parentId = vo.getParentId();
            if (parentId == null) {
                // 没有父节点的为根节点
                result.add(vo);
            } else {
                // 找到父节点，建立父子关系
                MathCatalogNodeVO parent = nodeMap.get(parentId);
                if (parent != null) {
                    if (CollUtil.isEmpty(parent.getSubNodes())) {
                        parent.setSubNodes(new java.util.ArrayList<>());
                    }
                    parent.getSubNodes().add(vo);
                }
            }
        }

        return result;
    }

    /**
     * 将实体对象转换为VO对象
     */
    private MathCatalogNodeVO convertToVO(MathCatalogNode node) {
        MathCatalogNodeVO vo = new MathCatalogNodeVO();
        vo.setId(node.getId());
        vo.setName(node.getName());
        return vo;
    }
}




