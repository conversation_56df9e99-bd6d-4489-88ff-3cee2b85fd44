package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.MathCatalogNode;
import com.joinus.knowledge.model.vo.MathCatalogNodeVO;
import com.joinus.knowledge.service.MathCatalogNodesService;
import com.joinus.knowledge.mapper.MathCatalogNodesMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_catalog_nodes(数学目录节点表)】的数据库操作Service实现
* @createDate 2025-07-30 17:33:40
*/
@Service
public class MathCatalogNodesServiceImpl extends ServiceImpl<MathCatalogNodesMapper, MathCatalogNode>
    implements MathCatalogNodesService{

    @Override
    public List<MathCatalogNodeVO> listCatalogNodes(UUID nodeId) {
        List<MathCatalogNode> nodes = baseMapper.listCatalogNodes(nodeId);

        if (CollUtil.isEmpty(nodes)) {
            return new java.util.ArrayList<>();
        }

        // 转换为VO列表并建立映射关系（一次遍历完成两个操作）
        java.util.Map<UUID, MathCatalogNodeVO> nodeMap = new java.util.HashMap<>(nodes.size());
        List<MathCatalogNodeVO> rootNodes = new java.util.ArrayList<>();

        // 第一次遍历：转换为VO并建立映射
        for (MathCatalogNode node : nodes) {
            MathCatalogNodeVO vo = convertToVO(node);
            nodeMap.put(vo.getId(), vo);
        }

        // 第二次遍历：构建父子关系
        for (MathCatalogNodeVO vo : nodeMap.values()) {
            UUID parentId = vo.getParentId();
            if (parentId == null) {
                // 根节点
                rootNodes.add(vo);
            } else {
                // 子节点，添加到父节点的子节点列表中
                MathCatalogNodeVO parent = nodeMap.get(parentId);
                if (parent != null) {
                    if (parent.getSubNodes() == null) {
                        parent.setSubNodes(new java.util.ArrayList<>());
                    }
                    parent.getSubNodes().add(vo);
                }
            }
        }

        // 对每个节点的子节点排序并设置叶子节点标识
        sortSubNodes(rootNodes);
        setLeafFlags(rootNodes);

        return rootNodes;
    }

    /**
     * 递归排序子节点
     */
    private void sortSubNodes(List<MathCatalogNodeVO> nodes) {
        if (CollUtil.isEmpty(nodes)) {
            return;
        }

        for (MathCatalogNodeVO node : nodes) {
            if (CollUtil.isNotEmpty(node.getSubNodes())) {
                // 按level和sortNo排序（如果数据库查询没有排序的话）
                node.getSubNodes().sort((a, b) -> {
                    int levelCompare = Integer.compare(
                        a.getLevel() != null ? a.getLevel() : 0,
                        b.getLevel() != null ? b.getLevel() : 0
                    );
                    if (levelCompare != 0) {
                        return levelCompare;
                    }
                    // 如果level相同，可以按name排序或者其他字段
                    return a.getName().compareTo(b.getName());
                });

                // 递归排序子节点
                sortSubNodes(node.getSubNodes());
            }
        }
    }

    /**
     * 递归设置叶子节点标识
     */
    private void setLeafFlags(List<MathCatalogNodeVO> nodes) {
        if (CollUtil.isEmpty(nodes)) {
            return;
        }

        for (MathCatalogNodeVO node : nodes) {
            boolean isLeaf = CollUtil.isEmpty(node.getSubNodes());
            node.setIsLeaf(isLeaf);

            if (!isLeaf) {
                // 递归设置子节点的叶子标识
                setLeafFlags(node.getSubNodes());
            }
        }
    }

    /**
     * 将实体对象转换为VO对象
     */
    private MathCatalogNodeVO convertToVO(MathCatalogNode node) {
        return MathCatalogNodeVO.builder()
                .id(node.getId())
                .parentId(node.getParentId())
                .name(node.getName())
                .level(node.getLevel())
                .isLeaf(false) // 默认为false，后续可以根据是否有子节点来设置
                .subNodes(null) // 初始化为null，有子节点时再创建列表
                .build();
    }

    /**
     * 更优化的树形结构构建方法（使用Stream API）
     * 这是一个备选方案，性能更好，代码更简洁
     */
    public List<MathCatalogNodeVO> listCatalogNodesOptimized(UUID nodeId) {
        List<MathCatalogNode> nodes = baseMapper.listCatalogNodes(nodeId);

        if (CollUtil.isEmpty(nodes)) {
            return new java.util.ArrayList<>();
        }

        // 转换为VO并建立映射
        Map<UUID, MathCatalogNodeVO> nodeMap = nodes.stream()
                .map(this::convertToVO)
                .collect(Collectors.toMap(MathCatalogNodeVO::getId, vo -> vo));

        // 构建父子关系并收集根节点
        List<MathCatalogNodeVO> rootNodes = nodeMap.values().stream()
                .peek(vo -> {
                    UUID parentId = vo.getParentId();
                    if (parentId != null) {
                        MathCatalogNodeVO parent = nodeMap.get(parentId);
                        if (parent != null) {
                            if (parent.getSubNodes() == null) {
                                parent.setSubNodes(new java.util.ArrayList<>());
                            }
                            parent.getSubNodes().add(vo);
                        }
                    }
                })
                .filter(vo -> vo.getParentId() == null)
                .collect(Collectors.toList());

        // 排序和设置叶子节点标识
        sortSubNodes(rootNodes);
        setLeafFlags(rootNodes);

        return rootNodes;
    }
}




