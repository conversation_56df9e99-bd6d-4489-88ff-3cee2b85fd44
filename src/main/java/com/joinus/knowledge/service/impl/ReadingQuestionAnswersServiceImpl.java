package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.mapper.ReadingQuestionAnswersMapper;
import com.joinus.knowledge.model.entity.ReadingQuestionAnswers;
import com.joinus.knowledge.service.ReadingQuestionAnswersService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
public class ReadingQuestionAnswersServiceImpl
        extends ServiceImpl<ReadingQuestionAnswersMapper, ReadingQuestionAnswers>
        implements ReadingQuestionAnswersService {

    @Override
    public ReadingQuestionAnswers getByQuestionId(UUID questionId) {
        LambdaQueryWrapper<ReadingQuestionAnswers> queryWrapper = Wrappers.lambdaQuery(ReadingQuestionAnswers.class)
                .eq(ReadingQuestionAnswers::getQuestionId, questionId);
        List<ReadingQuestionAnswers> list = this.list(queryWrapper);
        return list.stream()
                .filter(readingQuestionAnswers -> readingQuestionAnswers.getAnsweringFormulaId() != null)
                .findFirst()
                .orElse(list.getFirst());
    }

    @Override
    public List<ReadingQuestionAnswers> getFillInTheBlankQuestionAnswers() {
        return baseMapper.getFillInTheBlankQuestionAnswers();
    }
}