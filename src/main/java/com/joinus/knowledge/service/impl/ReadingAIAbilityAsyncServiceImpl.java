package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.enums.PromptEnum;
import com.joinus.knowledge.enums.ReadingAIAbilityType;
import com.joinus.knowledge.enums.ReadingQuestionType;
import com.joinus.knowledge.model.entity.PromptTemplate;
import com.joinus.knowledge.model.entity.ReadingPassageQuestions;
import com.joinus.knowledge.model.entity.ReadingQuestionAnswers;
import com.joinus.knowledge.model.param.ReadingAIAbilityItemParam;
import com.joinus.knowledge.model.param.ReadingAIAbilityParam;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.utils.PromptUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.MatchResult;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Service("readingAIAbilityAsyncService")
public class ReadingAIAbilityAsyncServiceImpl implements ReadingAIAbilityAsyncService {

    private PromptUtils promptUtils;
    private AIChatService aiChatService;
    private ReadingAIAbilityCallbackAsyncService readingAIAbilityCallbackAsyncService;
    private ReadingPassageQuestionsService readingPassageQuestionsService;
    private ReadingQuestionAnswersService readingQuestionAnswersService;
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    final static String S_CHANGE_LINE = "\n";
    final static String S_VERTICAL_LINE = "\\|";

    @Override
    public BigDecimal aiCorrect(String testId, String questionId, String answer) {
        BigDecimal result = null;
        if (StrUtil.isNotBlank(answer)) {
            String promptTextTemplate = promptUtils.getPromptTemplate(PromptEnum.READING_AI_CORRECT);
            ReadingPassageQuestions question = readingPassageQuestionsService.getById(UUID.fromString(questionId));
            String questionContent = getQuestionContent(question);
            ReadingQuestionAnswers questionAnswers = readingQuestionAnswersService.getByQuestionId(question.getId());
            String questionAnswer = getQuestionAnswer(question, questionAnswers);

            // 处理用户回答
            answer = getUserAnswer(question,questionAnswers, answer);

            String promptText = String.format(promptTextTemplate, questionContent, questionAnswer,
                    questionAnswers.getContent(), answer);
            // 1、调用AI批改答案
            String responseResult = aiChatService.chat(promptText, AIModelType.DOUBAO_LITE,ChatOptions.builder().temperature(0D).build());
            //用正则表达式提取responseResult的数字部分
            Pattern pattern = Pattern.compile("[-+]?\\d*\\.?\\d+");
            Matcher matcher = pattern.matcher(responseResult);
            //matcher 转成数组
            String[] matcherResult = matcher.results().map(MatchResult::group).toArray(String[]::new);
            if (matcherResult.length > 0) {
                result = new BigDecimal(matcherResult[matcherResult.length - 1]);
            }
        }
        return result;
    }

    @Override
    public void weakKnowledgePointAnalysis(ReadingAIAbilityParam param) {
        log.info("AI语文阅读-开始执行薄弱知识点分析-callbackId-{}", param.getId());
        param.setReadingAIAbilityType(ReadingAIAbilityType.WEAK_KNOWLEDGE_POINT_ANALYSIS.getType());
        dealByAIAbility(param);
    }

    @Override
    public void trainingSuggestion(ReadingAIAbilityParam param) {
        log.info("AI语文阅读-开始执行综合训练建议-callbackId-{}", param.getId());
        param.setReadingAIAbilityType(ReadingAIAbilityType.TRAINING_SUGGESTION.getType());
        dealByAIAbility(param);
    }

    @Override
    public void weakQuestionTypeAnalysis(ReadingAIAbilityParam param) {
        log.info("AI语文阅读-开始执行薄弱题型分析-callbackId-{}", param.getId());
        param.setReadingAIAbilityType(ReadingAIAbilityType.WEAK_QUESTION_TYPE_ANALYSIS.getType());
        dealByAIAbility(param);
    }

    @Override
    public void dealByAIAbility(ReadingAIAbilityParam param) {
        log.info("AI语文阅读-dealByAIAbility-开始执行-readingAIAbilityType-{}-callbackId-{}", param.getReadingAIAbilityType(), param.getId());
        Integer readingAIAbilityType = param.getReadingAIAbilityType();
        ReadingAIAbilityType readingAIAbility = ReadingAIAbilityType.getByType(readingAIAbilityType);
        if (Objects.isNull(readingAIAbility)) {
            throw new BusinessException("AI能力类型不存在");
        }
        PromptTemplate promptTemplate = promptUtils.getPromptTemplateEntity(readingAIAbility.getPromptEnum());
        if (readingAIAbilityType == 5) {
            //答案比对
            String dataPromptTemplateText = """
                    ---------
                    题目：%s
                    用户输入：%s
                    参考答案：%s
                    """;
            List<CompletableFuture<String>> futureResults = param.getItems().stream()
                    .map(item -> CompletableFuture.supplyAsync(() -> {
                        ReadingPassageQuestions question = readingPassageQuestionsService.getById(UUID.fromString(item.getId()));
                        String questionContent = getQuestionContent(question);
                        ReadingQuestionAnswers questionAnswers = readingQuestionAnswersService.getByQuestionId(question.getId());
                        String questionAnswer = getQuestionAnswer(question, questionAnswers);
                        String dataPromptText = String.format(dataPromptTemplateText, questionContent, item.getAnswer(), questionAnswer);
                        String promptText = promptTemplate.getContent() + dataPromptText;

                        return item.getId() + "###" + callAIAbility(param, promptText, promptTemplate);
                    }, threadPoolTaskExecutor).exceptionally(throwable -> {
                        log.error("AI语文阅读-dealByAIAbility-readingAIAbilityType-{}-callbackId-{}-questionId-{}-error-{}", param.getReadingAIAbilityType(),
                                param.getId(), item.getId(), throwable.getMessage());
                        return item.getId() + "###" + "error";
                    })).toList();

            List<String> results = CompletableFuture.allOf(futureResults.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futureResults.stream().map(CompletableFuture::join).collect(Collectors.toList()))
                    .join();
            log.info("AI语文阅读-dealByAIAbility-readingAIAbilityType-{}-callbackId-{}-results-{}", param.getReadingAIAbilityType(),
                    param.getId(),results);
            // 2、回调业务端接口，通知分析结果
            readingAIAbilityCallbackAsyncService.callbackReadingCommon(param.getId(), param.getReadingAIAbilityType(), results);
        } else if (readingAIAbilityType == 6) {
            //次报告
            String dataPromptTemplateText = """
                    ---------
                    题目：%s
                    题型：%s
                    考查知识点：%s
                    答案：%s
                    解析：%s
                    用户回答：%s
                    用户自判：%s
                    """;
            StringBuffer dataPromptText = new StringBuffer();
            recombineItems(param);
            param.getItems().forEach(item -> {
                ReadingPassageQuestions question = readingPassageQuestionsService.getById(UUID.fromString(item.getId()));
                String questionContent = getQuestionContent(question);
                ReadingQuestionAnswers questionAnswers = readingQuestionAnswersService.getByQuestionId(question.getId());
                String questionAnswer = getQuestionAnswer(question, questionAnswers);
                String userDecisionResult = getUserDecisionResult(item.getResult());

                dataPromptText.append(String.format(dataPromptTemplateText,
                        questionContent,
                        item.getQuestionType(),
                        item.getKnowledgePoint(),
                        questionAnswer,
                        questionAnswers.getContent(),
                        item.getAnswer(),
                        userDecisionResult)).append("\n");

            });
            String promptText = promptTemplate.getContent() + dataPromptText;

            String result = callAIAbility(param, promptText, promptTemplate);
            // 2、回调业务端接口，通知分析结果
            readingAIAbilityCallbackAsyncService.callbackReadingCommon(param.getId(), param.getReadingAIAbilityType(), result);

        } else {
            //周报告 以及 其它
            if (StrUtil.isNotBlank(param.getDirectMessage())) {
                String promptText = promptTemplate.getContent() + "\n---------\n" +  param.getDirectMessage();
                String result = callAIAbility(param, promptText, promptTemplate);
                // 2、回调业务端接口，通知分析结果
                readingAIAbilityCallbackAsyncService.callbackReadingCommon(param.getId(), param.getReadingAIAbilityType(), result);
            } else {
                log.info("AI语文阅读-dealByAIAbility-readingAIAbilityType-{}-callbackId-{}-directMessage-为空", param.getReadingAIAbilityType(), param.getId());
            }
        }

        log.info("AI语文阅读-dealByAIAbility-执行完成-readingAIAbilityType-{}-callbackId-{}", param.getReadingAIAbilityType(), param.getId());
    }

    public String callAIAbility(ReadingAIAbilityParam param, String promptText, PromptTemplate promptTemplate) {
        final int maxRetries = 3;
        int retryCount = 0;
        ObjectMapper mapper = new ObjectMapper();

        while (retryCount < maxRetries) {
            try {
                log.info("AI语文阅读-dealByAIAbility-readingAIAbilityType-{}-callbackId-{}-promptText-{}-第{}次", param.getReadingAIAbilityType(),
                        param.getId(), promptText , retryCount + 1);
                JsonNode jsonNode = mapper.readTree(promptTemplate.getStructuredOutputJsonSchema());
                String result = aiChatService.chat(promptTemplate.getSystemPrompt(), promptText, null,
                        AIModelType.DOUBAO_DEEPSEEK_R1, ChatOptions.builder().temperature(0.6).build(), jsonNode);
                log.info("AI语文阅读-dealByAIAbility-readingAIAbilityType-{}-callbackId-{}-result-{}-第{}次", param.getReadingAIAbilityType(),
                        param.getId(), result,  retryCount + 1);
                mapper.readTree(result); // Verify if result can be converted to JSON
                return result;
            } catch (Exception e) {
                log.error("Error ", e);
                retryCount++;
                if (retryCount == maxRetries) {
                    throw new RuntimeException("Failed after max retries", e);
                }
                log.info("Retrying... Attempt number: {}", retryCount);
            }
        }
        throw new IllegalStateException("Should not reach here");
    }


    /**
     * 重组题目，一个题目可能有多个知识点，这样的题目按知识点拆分成多个题目，拆分后的题目知识点不同，内容相同
     *
     * @param param
     */
    private void recombineItems(ReadingAIAbilityParam param) {
        List<ReadingAIAbilityItemParam> items = param.getItems().stream()
                .flatMap(item -> {
                    String knowledgePoint = StrUtil.replace(item.getKnowledgePoint(), "，", ",");
                    // 按逗号拆分知识点字段
                    String[] knowledgePoints = knowledgePoint.split(",");
                    // 为每个拆分后的知识点创建新对象
                    return Arrays.stream(knowledgePoints)
                            .map(kp -> {
                                ReadingAIAbilityItemParam newItem = BeanUtil.copyProperties(item, ReadingAIAbilityItemParam.class);
                                newItem.setKnowledgePoint(kp);
                                return newItem;
                            });
                })
                .toList();
        param.setItems(items);
    }

    /**
     * 获取题目内容
     *
     * @param question
     * @return
     */
    private String getQuestionContent(ReadingPassageQuestions question) {
        String questionContent = question.getContent();
        if (StrUtil.equals(question.getQuestionType(), ReadingQuestionType.FILL_IN_THE_BLANK.toString())) {
            questionContent = questionContent.replaceAll("<cloze:\\d+>", "______");
        }
        JSONObject jsonObject = JSONUtil.parseObj(questionContent);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(jsonObject.getStr("问题"));
        if (jsonObject.containsKey("选项")) {
            stringBuilder.append("\n");
            JSONArray options = jsonObject.getJSONArray("选项");
            for (int i = 0; i < options.size(); i++) {
                stringBuilder.append(options.getStr(i)).append("\n");
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }
        return stringBuilder.toString();
    }

    /**
     * 获取题目答案
     *
     * @param question
     * @return
     */
    private String getQuestionAnswer(ReadingPassageQuestions question, ReadingQuestionAnswers questionAnswers) {
        StringBuilder stringBuilder = new StringBuilder();
        JSONObject jsonObject = JSONUtil.parseObj(questionAnswers.getAnswer());
        //填空题答案格式修改，无需单独处理
        if (StrUtil.equals(question.getQuestionType(), ReadingQuestionType.FILL_IN_THE_BLANK.toString())) {
            List<String> answers = List.of(jsonObject.getStr("答案").split(S_VERTICAL_LINE));
            int i = 0;
            for (String answerItem : answers) {
                i++;
                stringBuilder.append(StrUtil.C_LF).append("填空").append(i).append("答案：").append(answerItem).append(StrUtil.C_LF);
            }
        } else {
            stringBuilder.append(jsonObject.getStr("答案"));
        }
        return stringBuilder.toString();
    }

    /**
     * @Description 获取用户答案
     * <AUTHOR>
     * @date 2025/5/19
     */
    private String getUserAnswer(ReadingPassageQuestions question, ReadingQuestionAnswers questionAnswers, String userAnswers) {
        StringBuilder stringBuilder = new StringBuilder();
        if (StrUtil.equals(question.getQuestionType(), ReadingQuestionType.FILL_IN_THE_BLANK.toString())) {
            JSONObject jsonObject = JSONUtil.parseObj(questionAnswers.getAnswer());
            List<String> questionAnswer = List.of(jsonObject.getStr("答案").split(S_VERTICAL_LINE));
            List<String> userAnswer = List.of(userAnswers.split(S_VERTICAL_LINE));

            int count = 0;
            for (int i = 0; i < questionAnswer.size(); i++) {
                count++;
                if (i < userAnswer.size()) {
                    stringBuilder.append(StrUtil.C_LF).append("用户回答填空").append(count).append("答案：").append(userAnswer.get(i)).append(StrUtil.C_LF);
                }else {
                    stringBuilder.append(StrUtil.C_LF).append("用户回答填空").append(count).append("答案：").append(StrUtil.C_SPACE).append(StrUtil.C_LF);
                }
            }
        }else {
            stringBuilder.append(userAnswers);
        }
        return stringBuilder.toString();
    }

    /**
     * @description: 获取用户自判断结果
     * @author: lifengxu
     * @date: 2025/6/24 17:37
     */
    private String getUserDecisionResult(BigDecimal result) {
        // result 小于1 为错，大于1为对
        if (result.compareTo(BigDecimal.ONE) < 0) {
            return "错";
        } else {
            return "对";
        }
    }
}
