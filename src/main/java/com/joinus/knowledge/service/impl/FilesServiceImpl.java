package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.dto.DerivativeFileDTO;
import com.joinus.knowledge.model.dto.TextbookFileDTO;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.vo.CutQuestionVO;
import com.joinus.knowledge.model.vo.TextbookFileVO;
import com.joinus.knowledge.service.FileDerivativeService;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.mapper.FilesMapper;
import com.joinus.knowledge.utils.AliOssUtils;
import com.joinus.knowledge.utils.FileUtil;
import com.joinus.knowledge.utils.MinioUtils;
import com.joinus.knowledge.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【files】的数据库操作Service实现
* @createDate 2025-03-06 16:06:22
*/
@Service
@Slf4j
public class FilesServiceImpl extends ServiceImpl<FilesMapper, File> implements FilesService{

    @Autowired
    private FileDerivativeService derivativeService;
    @Autowired
    private MinioUtils minioUtils;
    @Autowired
    private AliOssUtils aliOssUtils;

    @Override
    public List<TextbookFileVO> getFilesByBookId(UUID bookId) {
        List<TextbookFileDTO> files =baseMapper.listFilesByBookId(bookId);
        if (CollUtil.isEmpty(files)) {
            return new ArrayList<>();
        }
        List<DerivativeFileDTO> derivativeFiles = derivativeService.listByTextbookId(bookId);

        Map<UUID, List<DerivativeFileDTO>> collect = derivativeFiles.stream().collect(Collectors.groupingBy(DerivativeFileDTO::getFileId));

        List<TextbookFileVO> textbookFiles = new ArrayList<TextbookFileVO>();
        files.stream().sorted((o1, o2) -> o1.getPageNo().compareTo(o2.getPageNo()))
                .forEach(file -> {
                    List<DerivativeFileDTO> derivativeFileDTOs = collect.get(file.getFileId());
                    if (CollUtil.isNotEmpty(derivativeFileDTOs)) {
                        DerivativeFileDTO jpgFileDTO = derivativeFileDTOs.stream()
                                .filter(dto -> "original".equals(dto.getDerivativeType()))
                                .findFirst()
                                .orElse(null);
                        if (null != jpgFileDTO) {
                            file.setJpgOssKey(jpgFileDTO.getOssKey());
                            file.setJpgWidth(jpgFileDTO.getWidth());
                            file.setJpgHeight(jpgFileDTO.getHeight());
                        }
                        file.setThumbnailOssKey(
                                derivativeFileDTOs.stream()
                                        .filter(dto -> "thumbnail".equals(dto.getDerivativeType()))
                                        .findFirst()
                                        .map(DerivativeFileDTO::getOssKey)
                                        .orElse("")
                        );
                        file.setPreviewOssKey(
                                derivativeFileDTOs.stream()
                                        .filter(dto -> "preview".equals(dto.getDerivativeType()))
                                        .findFirst()
                                        .map(DerivativeFileDTO::getOssKey)
                                        .orElse("")
                        );
                    }
                    textbookFiles.add(TextbookFileVO.ofTextbookFileDTO(file, minioUtils));
                });
        return textbookFiles.stream().sorted((o1, o2) -> o1.getPageNo().compareTo(o2.getPageNo())).collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getFilesByBookId(String bookId) {
        return baseMapper.getFilesByBookId(UUID.fromString(bookId));
    }

    @Override
    public File save(String name, String originalImageType, String originalMimeType, String ossKey, OssEnum ossEnum) {
        if (ossEnum == null) {
            ossEnum = OssEnum.MINIO_EDU_KNOWLEDGE_HUB;
        }
        LambdaQueryWrapper<File> wrapper = Wrappers.lambdaQuery(File.class)
                .eq(File::getOssUrl, ossKey);
        File existFile = baseMapper.selectOne(wrapper);
        if (null != existFile) {
            return existFile;
        }
        File fileEntity = new File();
        fileEntity.setId(UUID.randomUUID());
        fileEntity.setName(name);
        fileEntity.setType(originalImageType);
        fileEntity.setMimeType(originalMimeType);
        fileEntity.setOssUrl(ossKey); // 存储OSS对象名称
        fileEntity.setOssType(ossEnum.getType());
        fileEntity.setOssBucket(ossEnum.getBucket());
        baseMapper.insert(fileEntity);
        return fileEntity;
    }

    @Override
    public List<File> listQuestionOriginImages(UUID questionId) {
        return baseMapper.listQuestionOriginImages(questionId);
    }

    @Override
    public String getOssUrl(String name, String ossKey, String ossType, String ossBucket) {
        if (OssEnum.MINIO_EDU_KNOWLEDGE_HUB.getType().equals(ossType) && OssEnum.MINIO_EDU_KNOWLEDGE_HUB.getBucket().equals(ossBucket)) {
            return minioUtils.getPresignedDownloadUrl(ossBucket, ossKey, name);
        } else if (OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getType().equals(ossType) && OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getBucket().equals(ossBucket)) {
            return aliOssUtils.generatePresignedUrl(ossBucket, ossKey);
        }
        return null;
    }

    @Override
    public String getOssUrl(String ossKey, OssEnum ossEnum) {
        String fileName = Paths.get(ossKey).getFileName().toString();
        if (OssEnum.MINIO_EDU_KNOWLEDGE_HUB == ossEnum) {
            return minioUtils.getPresignedDownloadUrl(ossEnum.getBucket(), ossKey, fileName);
        } else if (OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB == ossEnum) {
            return aliOssUtils.generatePresignedUrl(ossEnum.getBucket(), ossKey);
        }
        return null;
    }



    public static void main(String[] args) {
        String ossKey = "prod/geogebra/2025/06/26/664218a2-b341-490a-9529-51e9b7437711/base64-1750922964572.png";
        String ossKey1 = "prod/geogebra/2025/06/26/37f8d3d9-4fea-428d-8036-02a1bd62e44d/base64-1750923005408.png";
        String ossKey2 = "prod/geogebra/2025/06/26/14a5885e-8345-44bb-b97f-ccb2ba0a2baa/base64-1750923117053.png";
        String ossKey3 = "prod/geogebra/2025/06/26/a2e2d398-0bdb-45a8-a323-2752785ec5c8/base64-1750923141027.png";
        String fileName = Paths.get(ossKey).getFileName().toString();
        String fileName1 = Paths.get(ossKey1).getFileName().toString();
        String fileName2 = Paths.get(ossKey2).getFileName().toString();
        String fileName3 = Paths.get(ossKey3).getFileName().toString();
        System.out.println(fileName);
        System.out.println(fileName1);
        System.out.println(fileName2);
        System.out.println(fileName3);
    }
    @Override
    public String getSectionHtml(UUID textbookId, Integer startPage, Integer endPage) {
        return baseMapper.getSectionHtml(textbookId, startPage, endPage);
    }

    @Override
    public CutQuestionVO getPositions(String ossKey, OssEnum ossEnum) {
        List<File> files = lambdaQuery().eq(File::getOssUrl, ossKey)
                .eq(File::getOssType, ossEnum.getType())
                .eq(File::getOssBucket, ossEnum.getBucket())
                .isNull(File::getDeletedAt)
                .list();
        if (CollUtil.isNotEmpty(files)) {
            return StrUtil.isNotBlank(files.get(0).getPositions()) ? JSONUtil.toBean(files.get(0).getPositions(), CutQuestionVO.class) : null;
        }
        return null;
    }

    @Override
    public List<String> getImgeUrls(List<String> ossKeys, OssEnum ossEnum) {
        List<String> ossUrls = new ArrayList<>();
        ossKeys.stream().forEach(ossKey -> {
            if (OssEnum.MINIO_EDU_KNOWLEDGE_HUB == ossEnum) {
                ossUrls.add(minioUtils.getPresignedDownloadUrl(ossEnum.getBucket(), ossKey, FileUtil.getFileNameFromOssKey(ossKey)));
            } else if (OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB == ossEnum) {
                ossUrls.add(aliOssUtils.generatePresignedUrl(ossEnum.getBucket(), ossKey));
            }
        });
        return ossUrls;
    }

}




