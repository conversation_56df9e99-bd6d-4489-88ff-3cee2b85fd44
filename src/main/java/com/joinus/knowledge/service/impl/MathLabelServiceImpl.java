package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.GradeType;
import com.joinus.knowledge.enums.MathLabelTypeEnum;
import com.joinus.knowledge.enums.QuestionSourceType;
import com.joinus.knowledge.enums.SemesterType;
import com.joinus.knowledge.mapper.MathQuestionsMapper;
import com.joinus.knowledge.model.entity.MathLabel;
import com.joinus.knowledge.model.param.MathQuestionErrorLabelParam;
import com.joinus.knowledge.model.po.QuestionPublishInfoPO;
import com.joinus.knowledge.model.vo.MathLabelVO;
import com.joinus.knowledge.model.vo.MathQuestionErrorLabelVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import com.joinus.knowledge.service.MathLabelService;
import com.joinus.knowledge.mapper.MathLabelMapper;
import com.joinus.knowledge.service.QuestionLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
* <AUTHOR>
* @description 针对表【math_labels(标签表)】的数据库操作Service实现
* @createDate 2025-03-26 15:05:21
*/
@Service
public class MathLabelServiceImpl extends ServiceImpl<MathLabelMapper, MathLabel>
    implements MathLabelService{

    @Autowired
    private QuestionLabelService questionLabelService;
    @Autowired
    private MathQuestionsMapper mathQuestionsMapper;

    @Override
    public List<MathQuestionErrorLabelVO> listErrorLabels(UUID id) {
        List<UUID> questionLabelIds =questionLabelService.listByQuestionId(id);

        List<MathLabel> questionErrorLabels = lambdaQuery().in(MathLabel::getType, GlobalConstants.QUESTION_ERROR_LABEL_TYPES)
                .list();
        return questionErrorLabels.stream().map(questionLabel ->{
            MathQuestionErrorLabelVO mathQuestionErrorLabelVO = BeanUtil.copyProperties(questionLabel, MathQuestionErrorLabelVO.class);
            mathQuestionErrorLabelVO.setHasLabel(questionLabelIds.contains(questionLabel.getId()));
            return mathQuestionErrorLabelVO;
        }).toList();
    }

    @Override
    public List<Map<String, List<MathQuestionErrorLabelVO>>> listErrorLabelsV1(UUID id) {
        List<UUID> questionLabelIds =questionLabelService.listByQuestionId(id);

        List<MathLabel> questionErrorLabels = lambdaQuery().in(MathLabel::getType, GlobalConstants.QUESTION_ERROR_LABEL_TYPES)
                .list();
        List<MathQuestionErrorLabelVO> collect = questionErrorLabels.stream().map(questionLabel ->{
            MathQuestionErrorLabelVO mathQuestionErrorLabelVO = BeanUtil.copyProperties(questionLabel, MathQuestionErrorLabelVO.class);
            mathQuestionErrorLabelVO.setHasLabel(questionLabelIds.contains(questionLabel.getId()));
            return mathQuestionErrorLabelVO;
        }).toList();

        Map<String, List<MathQuestionErrorLabelVO>> collectMap = collect.stream().collect(Collectors.groupingBy(MathQuestionErrorLabelVO::getType));

        List<Map<String, List<MathQuestionErrorLabelVO>>> result = new ArrayList<>();
        for (String type : collectMap.keySet()) {
            HashMap item = new HashMap<>();
            item.put("key", type);
            item.put("value", MathLabelTypeEnum.ofType(type).getDescription());
            item.put("names", collectMap.get(type));
            result.add(item);
        }
        return result;
    }

    @Override
    public List<String> listCommonLabels(UUID id) {

        QuestionDetailVO detailById = mathQuestionsMapper.getDetailById(id);
        if (null == detailById) {
            throw new BusinessException("题目不存在");
        }

        List<UUID> questionLabelIds =questionLabelService.listByQuestionId(id);
        List<MathLabel> allLabels = list();
        List<MathLabel> existCommonLabels = allLabels.stream()
                .filter(questionLabel -> questionLabelIds.contains(questionLabel.getId()))
                .toList();

        List<String> labels = new ArrayList<>();
        //题目来源方式
        labels.add(detailById.getSource().getDescription());

        //题目所述教材信息
        List<QuestionPublishInfoPO> questionPublishInfoPOs = mathQuestionsMapper.listPublishInfo(id);
        if (CollUtil.isNotEmpty(questionPublishInfoPOs)) {
            questionPublishInfoPOs.stream().forEach(questionPublishInfoPO -> {
                String label = StrUtil.format("{}-{}-{}-{}-{}", questionPublishInfoPO.getPublisher().getValue(),
                        null != GradeType.of(questionPublishInfoPO.getGrade()) ? GradeType.of(questionPublishInfoPO.getGrade()).getDescription() : "未知年级",
                        null != SemesterType.of(questionPublishInfoPO.getSemester()) ? SemesterType.of(questionPublishInfoPO.getSemester()).getDescription() : "未知学期",
                        questionPublishInfoPO.getChapterName(),  questionPublishInfoPO.getSectionName());
                labels.add(label);
            });
        }
        //题目相关标签
        if (CollUtil.isNotEmpty(existCommonLabels)) {
            labels.addAll(existCommonLabels.stream().map(MathLabel::getName).toList());
        }

        return labels;
    }

    @Override
    public List<MathLabelVO> listCommonLabelsV1(UUID id) {
        QuestionDetailVO detailById = mathQuestionsMapper.getDetailById(id);
        if (null == detailById) {
            throw new BusinessException("题目不存在");
        }

        List<MathLabelVO> labels = new ArrayList<>();
        //题目来源方式
        if (null != detailById.getSource()) {
            if (QuestionSourceType.ZHONG_KAO_EXAM == detailById.getSource()) {
                QuestionDetailVO detailVO = mathQuestionsMapper.getExamInfo(id);
                String examInfo = StrUtil.format("{}-{}-第{}题", QuestionSourceType.ZHONG_KAO_EXAM.getDescription(), detailVO.getExamName(), detailVO.getSortNoInExam());
                labels.add(MathLabelVO.builder().name(examInfo).build());
            } else {
                labels.add(MathLabelVO.builder().name(detailById.getSource().getDescription()).build());
            }
        }

        //题目所属教材信息
//        List<QuestionPublishInfoPO> questionPublishInfoPOs = mathQuestionsMapper.listPublishInfo(id);
//        if (CollUtil.isNotEmpty(questionPublishInfoPOs)) {
//            questionPublishInfoPOs.stream().forEach(questionPublishInfoPO -> {
//                String label = StrUtil.format("{}-{}-{}-{}-{}", questionPublishInfoPO.getPublisher().getValue(),
//                        null != GradeType.of(questionPublishInfoPO.getGrade()) ? GradeType.of(questionPublishInfoPO.getGrade()).getDescription() : "未知年级",
//                        null != SemesterType.of(questionPublishInfoPO.getSemester()) ? SemesterType.of(questionPublishInfoPO.getSemester()).getDescription() : "未知学期",
//                        questionPublishInfoPO.getChapterName(),  questionPublishInfoPO.getSectionName());
//                labels.add(MathLabelVO.builder().name(label).build());
//            });
//        }
        //题目相关标签
        List<UUID> questionLabelIds =questionLabelService.listByQuestionId(id);
        List<MathLabel> allLabels = list();
        List<MathLabel> existCommonLabels = allLabels.stream()
                .filter(questionLabel -> questionLabelIds.contains(questionLabel.getId()))
                .toList();
        if (CollUtil.isNotEmpty(existCommonLabels)) {
            labels.addAll(existCommonLabels.stream().map(MathLabelVO::ofLabel).toList());
        }
        return labels;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MathQuestionErrorLabelVO> updateErrorLabels(UUID id, List<MathQuestionErrorLabelParam> params) {
        questionLabelService.deleteRelations(id, params.stream().filter(param -> !param.getHasLabel()).map(MathQuestionErrorLabelParam::getId).toList());
        questionLabelService.saveRelations(id, params.stream().filter(param -> param.getHasLabel()).map(MathQuestionErrorLabelParam::getId).toList());
        return this.listErrorLabels(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Map<String, List<MathQuestionErrorLabelVO>>> updateErrorLabelsV1(UUID id, List<MathQuestionErrorLabelParam> params) {
        questionLabelService.deleteRelations(id, params.stream().filter(param -> !param.getHasLabel()).map(MathQuestionErrorLabelParam::getId).toList());
        questionLabelService.saveRelations(id, params.stream().filter(param -> param.getHasLabel()).map(MathQuestionErrorLabelParam::getId).toList());
        return this.listErrorLabelsV1(id);
    }

    @Override
    public List<MathLabel> listKnowledgeDomain() {
        return lambdaQuery().eq(MathLabel::getType, "KNOWLEDGE_DOMAIN").list();
    }

    @Override
    public List<MathLabelVO> getLabelsWithNull() {
        List<MathLabelVO> labelVOs = new ArrayList<>(list().stream().map(MathLabelVO::ofLabel).toList());
        List<MathLabelVO> nullLabels = labelVOs.stream()
                .map(MathLabelVO::getType)
                .distinct()
                .map(type -> MathLabelVO.builder().name("null").type(type).build())
                .toList();
        return Stream.concat(labelVOs.stream(), nullLabels.stream())
                .sorted(Comparator.comparing(MathLabelVO::getType)
                        .thenComparing((label1, label2) -> {
                            // 让name为"null"的项排在最后面
                            if ("null".equals(label1.getName()) && !"null".equals(label2.getName())) {
                                return 1; // label1排在后面
                            } else if (!"null".equals(label1.getName()) && "null".equals(label2.getName())) {
                                return -1; // label2排在后面
                            } else {
                                return label1.getName().compareTo(label2.getName()); // 正常比较
                            }
                        }))
                .collect(Collectors.toList());
    }

    @Override
    public List<MathLabelVO> getLabelsWithNullV1() {
        List<MathLabelVO> labelVOs = new ArrayList<>(list().stream().map(MathLabelVO::ofLabel).toList());
        List<MathLabelVO> nullLabels = labelVOs.stream()
                .map(MathLabelVO::getType)
                .distinct()
                .map(type -> MathLabelVO.builder().id(UUID.fromString("00000000-0000-0000-0000-000000000000")).name("null").type(type).build())
                .toList();
        return Stream.concat(labelVOs.stream(), nullLabels.stream())
                .sorted(Comparator.comparing(MathLabelVO::getType)
                        .thenComparing((label1, label2) -> {
                            // 让name为"null"的项排在最后面
                            if ("null".equals(label1.getName()) && !"null".equals(label2.getName())) {
                                return 1; // label1排在后面
                            } else if (!"null".equals(label1.getName()) && "null".equals(label2.getName())) {
                                return -1; // label2排在后面
                            } else {
                                return label1.getName().compareTo(label2.getName()); // 正常比较
                            }
                        }))
                .collect(Collectors.toList());
    }
}




