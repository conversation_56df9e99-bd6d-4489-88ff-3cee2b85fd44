package com.joinus.knowledge.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.mapper.TextbooksMapper;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathChapter;
import com.joinus.knowledge.model.entity.TextbookFile;
import com.joinus.knowledge.model.entity.Textbooks;
import com.joinus.knowledge.model.param.QueryTextbookParam;
import com.joinus.knowledge.model.vo.SectionKeypointVO;
import com.joinus.knowledge.model.vo.SectionVO;
import com.joinus.knowledge.model.vo.TextbookPointVO;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.utils.MinioUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_textbooks】的数据库操作Service实现
* @createDate 2023-05-08 22:29:03
*/
@Slf4j
@Service
@RequiredArgsConstructor
public class TextbooksServiceImpl extends ServiceImpl<TextbooksMapper, Textbooks>
    implements TextbooksService{

    private final TextbooksMapper textbooksMapper;
    private final MinioUtils minioUtils;
    private final TextbookFileService textbookFileService;
    private final EnterBookService enterBookService;
    private final MathChapterService mathChapterService;
    private final MathSectionService mathSectionService;
    private final FilesService filesService;

    @Override
    public List<Textbooks> findByNameLike(String name) {
        LambdaQueryWrapper<Textbooks> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(Textbooks::getName, name);
        queryWrapper.isNull(Textbooks::getDeletedAt);
        queryWrapper.orderByDesc(Textbooks::getUpdatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public List<Textbooks> findByPublisher(List<String> publishers) {
        LambdaQueryWrapper<Textbooks> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Textbooks::getPublisher, publishers);
        queryWrapper.isNull(Textbooks::getDeletedAt);
        queryWrapper.orderByDesc(Textbooks::getUpdatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public List<Textbooks> findBySubject(String subject) {
        LambdaQueryWrapper<Textbooks> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Textbooks::getSubject, subject);
        queryWrapper.isNull(Textbooks::getDeletedAt);
        queryWrapper.orderByDesc(Textbooks::getUpdatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public boolean logicalDelete(UUID id) {
        Textbooks textbook = this.getById(id);
        if (textbook == null) {
            return false;
        }
        
        // 已经被逻辑删除
        if (textbook.getDeletedAt() != null) {
            return true;
        }
        
        // 设置删除时间
        textbook.setDeletedAt(new Date());
        return this.updateById(textbook);
    }

    @Override
    public boolean restore(UUID id) {
        Textbooks textbook = this.getById(id);
        if (textbook == null) {
            return false;
        }
        
        // 已经是正常状态
        if (textbook.getDeletedAt() == null) {
            return true;
        }
        
        // 恢复状态
        textbook.setDeletedAt(null);
        textbook.setUpdatedAt(new Date());
        return this.updateById(textbook);
    }
    
    @Override
    public int importImagesAndLinkToTextbook(UUID textbookId, String folderPath) throws Exception {
        // 验证教材是否存在
        Textbooks textbook = enterBookService.getBookById(textbookId);
        if (textbook == null || textbook.getDeletedAt() != null) {
            throw new IllegalArgumentException("教材不存在或已被删除");
        }
        
        Path rootPath = Paths.get(folderPath);
        if (!Files.exists(rootPath) || !Files.isDirectory(rootPath)) {
            throw new IllegalArgumentException("指定路径不存在或不是目录");
        }
        
        // 获取所有页码文件夹
        List<Path> pageDirs;
        try {
            pageDirs = Files.list(rootPath)
                    .filter(Files::isDirectory)
                    .collect(Collectors.toList());
        } catch (IOException e) {
            log.error("读取目录失败: {}", e.getMessage());
            throw new RuntimeException("读取目录失败: " + e.getMessage(), e);
        }

        log.info("发现 {} 个页码文件夹", pageDirs.size());
        
        // 按页码排序文件夹
        List<Map.Entry<Integer, Path>> sortedPages = new ArrayList<>();
        for (Path pageDir : pageDirs) {
            String pageDirName = pageDir.getFileName().toString();
            try {
                int pageNo = Integer.parseInt(pageDirName);
                sortedPages.add(new AbstractMap.SimpleEntry<>(pageNo, pageDir));
            } catch (NumberFormatException e) {
                log.warn("跳过非法页码文件夹: {}", pageDirName);
            }
        }
        
        // 按页码数字排序
        sortedPages.sort(Comparator.comparing(Map.Entry::getKey));
        log.info("按页码排序后开始导入 {} 个有效页面", sortedPages.size());
        
        int importCount = 0;
        
        // 遍历每个页码文件夹（已排序）
        for (Map.Entry<Integer, Path> entry : sortedPages) {
            int pageNo = entry.getKey();
            Path pageDir = entry.getValue();
            
            try {
                // 首先查找original.png文件
                Path originalPngPath = pageDir.resolve("original.png");
                if (Files.exists(originalPngPath) && Files.isRegularFile(originalPngPath)) {
                    // 导入图片文件 - 使用单独的事务
                    boolean success = importPageImageWithNewTransaction(textbookId, pageNo, originalPngPath, pageDir);
                    if (success) {
                        importCount++;
                        log.info("成功导入第 {} 页，进度: {}/{}", pageNo, importCount, sortedPages.size());
                    }
                }
            } catch (Exception e) {
                log.error("处理页面 {} 失败: {}", pageNo, e.getMessage(), e);
                // 继续处理下一个页面，不中断整个过程
            }
        }
        
        return importCount;
    }

    /**
     * 在新事务中导入单个页面图片 - 用于解决事务嵌套问题
     */
//    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean importPageImageWithNewTransaction(UUID textbookId, int pageNo, Path originalImagePath, Path pageDir) {
        return importPageImage(textbookId, pageNo, originalImagePath, pageDir);
    }

    /**
     * 导入单个页面图片并建立与教材的关联
     * 
     * @param textbookId 教材ID
     * @param pageNo 页码
     * @param originalImagePath 原始图片文件路径
     * @param pageDir 页面目录，包含其他衍生图片
     * @return 是否成功导入
     */
    public boolean importPageImage(UUID textbookId, int pageNo, Path originalImagePath, Path pageDir) {
        try {

            // 首先检查是否已存在相同教材ID和页码的记录
            boolean existPage = enterBookService.checkExistPage(textbookId, pageNo);
            if (existPage) {
                log.info("教材 {} 的第 {} 页已存在关联记录，跳过导入", textbookId, pageNo);
                return true; // 已存在记录，视为成功，不重复导入
            }
            
            java.io.File originalImageFile = originalImagePath.toFile();
            String originalImageType = originalImagePath.getFileName().toString().endsWith(".jpg") ? "jpg" : "png";
            String originalMimeType = originalImageType.equals("jpg") ? "image/jpeg" : "image/png";
            
            // 读取图片信息
            BufferedImage image;
            try {
                image = ImageIO.read(originalImageFile);
                if (image == null) {
                    log.warn("无法读取图片文件: {}", originalImageFile.getName());
                    return false;
                }
            } catch (IOException e) {
                log.error("读取图片文件失败: {}, 错误: {}", originalImageFile.getName(), e.getMessage());
                return false;
            }
            
            // 上传原始图片到OSS
            String ossKeyPrefix = StrUtil.format("{}/{}/", textbookId, pageNo);
            String originalOssObjectName = ossKeyPrefix + originalImagePath.getFileName();
            try {
                minioUtils.uploadFile(GlobalConstants.MINIO_BUCKET_NAME, originalOssObjectName, originalImagePath.toString());
            } catch (Exception e) {
                log.error("上传文件到OSS失败: {}, 错误: {}", originalImagePath, e.getMessage());
                throw new RuntimeException("上传文件失败", e);
            }

            // 1. 保存文件记录并创建与教材的关联
            String fileName = "page_" + pageNo + "." + originalImageType;
            UUID fileId = enterBookService.saveFile(textbookId, fileName, originalImageType, originalMimeType, 
                    originalOssObjectName, OssEnum.MINIO_EDU_KNOWLEDGE_HUB, pageNo);
            
            if (fileId == null) {
                log.error("保存文件记录失败");
                return false;
            }

            log.info("保存页面 {} 的原始图片记录, 文件ID: {}, OSS路径: {}", pageNo, fileId, originalOssObjectName);

            // 3. 处理衍生文件
            // 处理预览图（original.jpg）
            try {
                Path jpgPath = pageDir.resolve("original.jpg");
                if (Files.exists(jpgPath) && Files.isRegularFile(jpgPath)) {
                    processDerivativeFile(ossKeyPrefix, fileId, jpgPath, "original", "jpeg", "image/jpeg");
                }
            } catch (Exception e) {
                log.error("处理原始JPG文件失败: {}", e.getMessage());
                // 继续处理其他衍生文件
            }

            // 处理预览图（preview.avif）
            try {
                Path previewPath = pageDir.resolve("preview.avif");
                if (Files.exists(previewPath) && Files.isRegularFile(previewPath)) {
                    processDerivativeFile(ossKeyPrefix, fileId, previewPath, "preview", "avif", "image/avif");
                }
            } catch (Exception e) {
                log.error("处理预览图失败: {}", e.getMessage());
                // 继续处理其他衍生文件
            }
            
            // 处理缩略图（thumbnail.webp）
            try {
                Path thumbnailPath = pageDir.resolve("thumbnail.webp");
                if (Files.exists(thumbnailPath) && Files.isRegularFile(thumbnailPath)) {
                    processDerivativeFile(ossKeyPrefix, fileId, thumbnailPath, "thumbnail", "webp", "image/webp");
                }
            } catch (Exception e) {
                log.error("处理缩略图失败: {}", e.getMessage());
                // 已完成主要处理，不影响结果
            }
            
            return true;
        } catch (Exception e) {
            log.error("导入页面 {} 图片失败: {}", pageNo, e.getMessage(), e);
            // 确保事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
    }

    /**
     * 处理衍生文件，上传到OSS并保存到file_derivatives表
     */
    private void processDerivativeFile(String ossKeyPrefix, UUID fileId, Path filePath, String derivativeType, String format, String mimeType) {
        try {
            java.io.File file = filePath.toFile();
            long fileSize = file.length();
            
            // 获取图片宽度和高度，对于不支持的格式会忽略
            Integer width = null;
            Integer height = null;
            
            // 仅对支持的格式读取图片信息
            if (!format.equalsIgnoreCase("avif") && !format.equalsIgnoreCase("webp")) {
                try {
                    BufferedImage image = ImageIO.read(file);
                    if (image != null) {
                        width = image.getWidth();
                        height = image.getHeight();
                    } else {
                        log.warn("无法读取图片信息: {}", file.getName());
                    }
                } catch (Exception e) {
                    log.warn("读取图片信息失败: {}, 错误: {}", file.getName(), e.getMessage());
                }
            } else {
                log.info("跳过不支持的图片格式({})的宽高读取: {}", format, file.getName());
            }
            
            // 上传文件到OSS
            String originalOssKey = ossKeyPrefix + filePath.getFileName();
            minioUtils.uploadFile(GlobalConstants.MINIO_BUCKET_NAME, originalOssKey, filePath.toString());

            // 保存衍生文件记录
            boolean success = enterBookService.saveFileDerivative(fileId, derivativeType, originalOssKey, format, width, height, fileSize);
            
            if (success) {
                log.info("保存衍生文件记录，主文件ID: {}, 衍生类型: {}, OSS路径: {}", fileId, derivativeType, originalOssKey);
            } else {
                log.error("保存衍生文件记录失败，主文件ID: {}, 衍生类型: {}", fileId, derivativeType);
            }
        } catch (Exception e) {
            log.error("处理衍生文件失败: {}", e.getMessage(), e);
            // 不抛出异常，允许继续处理
        }
    }

    @Override
    public List<Map<String, Object>> getTextbookPages(UUID textbookId) {
        // 验证教材是否存在
        Textbooks textbook = this.getById(textbookId);
        if (textbook == null || textbook.getDeletedAt() != null) {
            throw new IllegalArgumentException("教材不存在或已被删除");
        }
        
        // 查询教材关联的所有页面文件
        LambdaQueryWrapper<TextbookFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TextbookFile::getTextbookId, textbookId);
        queryWrapper.isNull(TextbookFile::getDeletedAt);
        queryWrapper.orderByAsc(TextbookFile::getPageNo);
        
        List<TextbookFile> textbookFiles = textbookFileService.list(queryWrapper);
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (TextbookFile textbookFile : textbookFiles) {
            UUID fileId = textbookFile.getFileId();
            
            // 查询文件信息
            File fileEntity = enterBookService.getFileById(fileId);
            if (fileEntity != null) {
                Map<String, Object> pageInfo = new HashMap<>();
                pageInfo.put("pageNo", textbookFile.getPageNo());
                pageInfo.put("fileId", fileId);
                pageInfo.put("fileName", fileEntity.getName());
                pageInfo.put("fileType", fileEntity.getType());
                pageInfo.put("fileUrl", fileEntity.getOssUrl());
                
                // 可以进一步查询文件的衍生文件，如预览图和缩略图
                // 这里暂时只返回原图信息
                
                result.add(pageInfo);
            }
        }
        
        return result;
    }

    @Override
    public List<TextbookPointVO> getKeypointsByBookIdAndPageNo(String bookId, Integer pageNo) {
        return textbooksMapper.getKeypointsByBookIdAndPageNo(UUID.fromString(bookId), pageNo);
    }

    @Override
    public TextbookPointVO getKeypointsByKeypointId(String type, UUID keypointId, UUID textbookId, Integer pageNo) {
        if (type.equals("question_type")) {
            return textbooksMapper.getQuestionTypeByKeypointId(keypointId, textbookId, pageNo);
        } else {
            return textbooksMapper.getKnowledgePointByKeypointId(keypointId, textbookId, pageNo);
        }
    }

    @Override
    public boolean checkExistPage(UUID textbookId, int pageNo) {
        LambdaQueryWrapper<TextbookFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TextbookFile::getTextbookId, textbookId);
        queryWrapper.eq(TextbookFile::getPageNo, pageNo);
        queryWrapper.isNull(TextbookFile::getDeletedAt); // 只检查未删除的记录

        long count = textbookFileService.count(queryWrapper);
        if (count > 0) {
            return true;
        }
        return false;
    }

    @Override
    public List<SectionKeypointVO> listAllKnowledgePointsAndQuestionTypes(UUID id) {
        return baseMapper.listAllKnowledgePointsAndQuestionTypes(id);
    }

    @Override
    public List<MathChapter> listAllChapters(UUID id) {
        return mathChapterService.listAllChaptersByBookId(id);
    }

    @Override
    public List<SectionVO> listAllSections(UUID id) {
        return mathSectionService.listAllSectionsByBookId(id);
    }

    @Override
    public List<Textbooks> listTextbook(QueryTextbookParam queryTextbookParam) {
        return lambdaQuery().eq(Textbooks::getPublisher, queryTextbookParam.getPublisher())
                .eq(Textbooks::getGrade, queryTextbookParam.getGrade())
                .eq(Textbooks::getSemester, queryTextbookParam.getSemester())
                .list();
    }

    @Override
    public Map<String, Object> queryElectronicTextbook(PublisherType publisher, Integer grade, Integer semester) {
        Textbooks textbook = lambdaQuery().eq(Textbooks::getPublisher, publisher)
                .eq(Textbooks::getGrade, grade)
                .eq(Textbooks::getSemester, semester)
                .one();
        if (null != textbook && null != textbook.getEbookFileId()) {
            File file = filesService.getById(textbook.getEbookFileId());
            return MapUtil.builder(new HashMap<String, Object>())
                    .put("ossEnum", OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket()))
                    .put("ossKey", file.getOssUrl())
                    .build();
        }
        return null;
    }
}
