package com.joinus.knowledge.service.impl.tag;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 标签约束验证结果
 */
@Data
@AllArgsConstructor
public class TagConstraintResult {
    
    /**
     * 是否通过验证
     */
    private boolean valid;
    
    /**
     * 验证失败时的消息
     */
    private String message;
    
    /**
     * 创建成功的验证结果
     */
    public static TagConstraintResult success() {
        return new TagConstraintResult(true, null);
    }
    
    /**
     * 创建失败的验证结果
     */
    public static TagConstraintResult failure(String message) {
        return new TagConstraintResult(false, message);
    }
}
