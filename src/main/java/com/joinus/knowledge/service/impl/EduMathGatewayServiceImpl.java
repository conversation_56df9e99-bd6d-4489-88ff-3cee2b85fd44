package com.joinus.knowledge.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.model.dto.ExamSummaryDTO;
import com.joinus.knowledge.service.EduMathGatewayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class EduMathGatewayServiceImpl implements EduMathGatewayService {

    @Value("${server.domain.edu.math.gateway:https://edu-math-gateway.uat.ijiaxiao.net}")
    private String eduMathGatewayServerDomain;
    private final Integer CONNECTIONG_TIMEOUT = 5 * 1000;
    private final Integer READ_TIMEOUT = 60 * 1000;

    @Override
    public ExamSummaryDTO summaryExam(List<String> imageUrls) {
        try {
            String url = eduMathGatewayServerDomain + "/summary";
            JSONObject jsonObject = new JSONObject()
                    .putOnce("images", imageUrls);
            
            long startTime = System.currentTimeMillis();
            HttpResponse response = HttpUtil.createPost(url)
                    .body(jsonObject.toString())
                    .setConnectionTimeout(CONNECTIONG_TIMEOUT)
                    .setReadTimeout(READ_TIMEOUT)
                    .execute();
            long endTime = System.currentTimeMillis();
            
            log.info("获取考试信息响应: request={}, status={}, body={} 耗时={}ms", jsonObject, response.getStatus(), response.body(), endTime - startTime);
            if (response.isOk()) {
                if (StrUtil.isEmpty(response.body()) || "null".equals(response.body())) {
                    return new ExamSummaryDTO();
                }
                JSONObject responseBody = JSONUtil.parseObj(response.body());
                if (null != responseBody.get("question_summary")) {
                    return JSONUtil.toBean(responseBody.getJSONObject("question_summary"), ExamSummaryDTO.class);
                }
            }
            return null;
        } catch (Exception e) {
            log.error("调用第三方接口异常: {}", e.getMessage());
            throw e;
        }
    }

    public static void main(String[] args) {
        String body = "{\"title\":\"\",\"term\":\"\",\"grade\":\"初中\",\"academic_year\":\"2018\",\"subject\":\"数学\",\"questions\":[{\"topic\":\"选择题\",\"numbers\":[1,2,3,4,5,6,7,8,9]}]}}";
        ExamSummaryDTO bean = JSONUtil.toBean(body, ExamSummaryDTO.class);
        System.out.println(bean);
    }
}
