package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.mapper.MathLabelMapper;
import com.joinus.knowledge.model.entity.MathLabel;
import com.joinus.knowledge.model.entity.QuestionLabel;
import com.joinus.knowledge.service.QuestionLabelService;
import com.joinus.knowledge.mapper.QuestionLabelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_labels】的数据库操作Service实现
* @createDate 2025-03-26 15:05:21
*/
@Service
public class QuestionLabelServiceImpl extends ServiceImpl<QuestionLabelMapper, QuestionLabel>
    implements QuestionLabelService{

    @Autowired
    private MathLabelMapper mathLabelMapper;

    @Override
    public List<UUID> listByQuestionId(UUID id) {
        LambdaQueryWrapper<QuestionLabel> wrapper = Wrappers.lambdaQuery(QuestionLabel.class)
                .eq(QuestionLabel::getQuestionId, id);
        List<QuestionLabel> questionLabels = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(questionLabels)) {
            return List.of();
        }
        return questionLabels.stream().map(QuestionLabel::getLabelId).toList();
    }

    @Override
    public void deleteRelations(UUID questionId, List<UUID> labelIdList) {
        baseMapper.delete(Wrappers.lambdaQuery(QuestionLabel.class)
                            .eq(QuestionLabel::getQuestionId, questionId)
                            .in(QuestionLabel::getLabelId, labelIdList));
    }

    @Override
    public void saveRelations(UUID questionId, List<UUID> labelIdList) {
        if (CollUtil.isEmpty(labelIdList)) {
            return ;
        }
        List<QuestionLabel> existQuestionLabels = baseMapper.selectList(Wrappers.lambdaQuery(QuestionLabel.class)
                .eq(QuestionLabel::getQuestionId, questionId)
                .in(QuestionLabel::getLabelId, labelIdList));
        List<UUID> labelIds = labelIdList.stream().filter(labelId -> existQuestionLabels.stream().noneMatch(questionLabel -> questionLabel.getLabelId().equals(labelId)))
                .toList();
        if (CollUtil.isNotEmpty(labelIds)) {
            LambdaQueryWrapper<MathLabel> labelWrapper = Wrappers.lambdaQuery(MathLabel.class)
                    .in(MathLabel::getId, labelIds);
            List<MathLabel> mathLabels = mathLabelMapper.selectList(labelWrapper);

            List<QuestionLabel> questionLabels = mathLabels.stream().map(label -> QuestionLabel.builder()
                    .questionId(questionId)
                    .labelId(label.getId())
                    .labelType(label.getType())
                    .build()).toList();
            saveBatch(questionLabels);
        }
    }
}




