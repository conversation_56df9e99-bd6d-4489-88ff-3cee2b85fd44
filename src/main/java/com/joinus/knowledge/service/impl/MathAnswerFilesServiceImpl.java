package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.joinus.knowledge.model.dto.ImageData;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathAnswerFiles;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.service.MathAnswerFilesService;
import com.joinus.knowledge.mapper.MathAnswerFilesMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_answer_files(答案与文件关联表)】的数据库操作Service实现
* @createDate 2025-05-22 18:38:54
*/
@Service
public class MathAnswerFilesServiceImpl extends ServiceImpl<MathAnswerFilesMapper, MathAnswerFiles>
    implements MathAnswerFilesService{

    @Resource
    private FilesService filesService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UUID> saveAnswerFileAndRelation(List<ImageData> imageDataList, UUID answerId) {
        List<UUID> fileIds = new ArrayList<>();
        for (int i = 0; i < imageDataList.size(); i++) {
            String objectName = imageDataList.get(i).getDataS3key();
            String originalImageType = objectName.endsWith(".jpg") ? "jpg" : "png";
            String originalMimeType = originalImageType.equals("jpg") ? "image/jpeg" : "image/png";
            com.joinus.knowledge.model.entity.File file = filesService.save(objectName.substring(objectName.lastIndexOf("/") + 1), originalImageType, originalMimeType, objectName, imageDataList.getFirst().getDataS3Enum());
            MathAnswerFiles answerFile = MathAnswerFiles.builder()
                    .answerId(answerId)
                    .fileId(file.getId())
                    .type(imageDataList.get(i).getType())
                    .sortNo(imageDataList.get(i).getSortNo())
                    .build();
            this.save(answerFile);
            fileIds.add(file.getId());
        }
        return fileIds;
    }

    @Override
    public void removeByAnswerId(UUID answerId) {
        List<MathAnswerFiles> answerFiles = lambdaQuery().eq(MathAnswerFiles::getAnswerId, answerId).list();
        if (CollUtil.isNotEmpty(answerFiles)) {
            filesService.getBaseMapper().delete(filesService.lambdaQuery().in(File::getId, answerFiles.stream().map(MathAnswerFiles::getFileId).toList()).getWrapper());
            baseMapper.delete(lambdaQuery().eq(MathAnswerFiles::getAnswerId, answerId).getWrapper());
        }
    }

}




