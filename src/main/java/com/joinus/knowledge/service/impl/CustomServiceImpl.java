package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.enums.*;
import com.joinus.knowledge.model.dto.ImageData;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.param.TextbookParam;
import com.joinus.knowledge.model.po.KeyPointInfo;
import com.joinus.knowledge.model.po.MathSecondLevelCatalogInfo;
import com.joinus.knowledge.model.po.MathThirdLevelCatalogInfo;
import com.joinus.knowledge.model.vo.OssFileVO;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.utils.ImageTagExtractor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomServiceImpl {
    @Resource
    private TextbooksService textbooksService;
    @Resource
    private OssService ossService;
    @Resource
    private MathQuestionsService mathQuestionsService;
    @Resource
    private MathAnswersService mathAnswersService;
    @Resource
    private QuestionFileService questionFileService;
    @Resource
    private MathAnswerFilesService mathAnswerFilesService;
    @Resource
    private FilesService filesService;
    @Resource
    private MathCatalogNodesService mathCatalogNodesService;
    @Resource
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Resource
    private SectionKnowledgePointsService sectionKnowledgePointsService;
    @Resource
    private MathQuestionTypesService mathQuestionTypesService;
    @Resource
    private QuestionAnswerRelationsService questionAnswerRelationsService;
    @Resource
    private QuestionKnowledgePointsService questionKnowledgePointsService;
    @Resource
    private QuestionTypesMappingService questionTypesMappingService;
    @Resource
    private SectionQuestionTypesService sectionQuestionTypesService;
    @Value("${textbook.image.local.path.template:/Users/<USER>/WorkSpace/小学数学/人教/{}/主书/{}、{}/{}}")
    private String imageLocalPathTpl;

    @Transactional(rollbackFor = Exception.class)
    public UUID saveChapterInfo(List<String> list, UUID chapterId) {
        MathCatalogNode rootNode = mathCatalogNodesService.getById(chapterId);
        Assert.isTrue(rootNode != null, "章节id错误", chapterId);
        Textbooks textbook = textbooksService.getById(rootNode.getTextbookId());
        for (String str: list) {
            JSONObject jsonObject = JSONUtil.parseObj(str);
            //章的标题
            /*if (jsonObject.containsKey("chapterName")) {
                chapter = mathChapterService.lambdaQuery()
                        .eq(MathChapter::getName, jsonObject.getStr("chapterName"))
                        .one();
                textbook = textbooksService.getById(chapter.getTextbookId());
            }*/
            //小节内容
            if (jsonObject.containsKey("secondLevelCatalogName")) {
                saveSecondLevelCatalogInfo(jsonObject, textbook, rootNode);
            }
            if (jsonObject.containsKey("keyPointName")) {
                saveKeyPoints(textbook, rootNode, CollUtil.newArrayList(JSONUtil.toBean(jsonObject, KeyPointInfo.class)), rootNode);
            }
        }
        return rootNode.getId();
    }

    /**
     * 保存二级小节相关内容
     * @param jsonObject
     * @param textbook
     * @param rootNode
     */
    private void saveSecondLevelCatalogInfo(JSONObject jsonObject, Textbooks textbook, MathCatalogNode rootNode) {
        MathSecondLevelCatalogInfo secondLevelCatalogInfo = JSONUtil.toBean(jsonObject, MathSecondLevelCatalogInfo.class);
        MathCatalogNode secondLevelCatalogNode = MathCatalogNode.builder()
                .parentId(rootNode.getId())
                .name(secondLevelCatalogInfo.getSecondLevelCatalogName())
                .sortNo(secondLevelCatalogInfo.getSortNo())
                .startPage(secondLevelCatalogInfo.getStartPage())
                .endPage(secondLevelCatalogInfo.getEndPage())
                .build();
        mathCatalogNodesService.save(secondLevelCatalogNode);
        log.info("2级catalogNode:{}", secondLevelCatalogNode);

        if (CollUtil.isNotEmpty(secondLevelCatalogInfo.getKeyPoints())) {
            saveKeyPoints(textbook, rootNode, secondLevelCatalogInfo.getKeyPoints(), secondLevelCatalogNode);
        }

        if (CollUtil.isNotEmpty(secondLevelCatalogInfo.getChildren())) {
            for (MathThirdLevelCatalogInfo thirdLevelCatalogInfo : secondLevelCatalogInfo.getChildren()) {
                MathCatalogNode thirdLevelCatalogNode = MathCatalogNode.builder()
                        .parentId(secondLevelCatalogNode.getId())
                        .name(thirdLevelCatalogInfo.getThirdLevelCatalogName())
                        .sortNo(thirdLevelCatalogInfo.getSortNo())
                        .startPage(thirdLevelCatalogInfo.getStartPage())
                        .endPage(thirdLevelCatalogInfo.getEndPage())
                        .build();
                mathCatalogNodesService.save(thirdLevelCatalogNode);

                if (CollUtil.isNotEmpty(thirdLevelCatalogInfo.getKeyPoints())) {
                    saveKeyPoints(textbook, rootNode, thirdLevelCatalogInfo.getKeyPoints(), thirdLevelCatalogNode);
                }
            }
        }
    }

    private void saveKeyPoints(Textbooks textbook, MathCatalogNode rootNode, List<KeyPointInfo> keyPointInfos, MathCatalogNode currentNode) {
        for (KeyPointInfo keyPointInfo : keyPointInfos) {
            if ("知识点".equals(keyPointInfo.getCategory())) {
                MathKnowledgePoint mathKnowledgePoint = new MathKnowledgePoint();
                mathKnowledgePoint.setName(keyPointInfo.getKeyPointName());
                mathKnowledgePoint.setSortNo(keyPointInfo.getSortNo());
                mathKnowledgePointsService.save(mathKnowledgePoint);
                log.info("mathKnowledgePoint:{}", mathKnowledgePoint);
                sectionKnowledgePointsService.createRelation(currentNode.getId(), mathKnowledgePoint.getId(), null);

                saveQuestionAndPointsRelation(keyPointInfo, mathKnowledgePoint.getId(), null, textbook, rootNode);
            } else {
                MathQuestionType mathQuestionType = new MathQuestionType();
                mathQuestionType.setName(keyPointInfo.getKeyPointName());
                mathQuestionType.setSortNo(keyPointInfo.getSortNo());
                mathQuestionType.setCategory(keyPointInfo.getCategory());
                mathQuestionTypesService.save(mathQuestionType);
                log.info("mathQuestionType:{}", mathQuestionType);
                sectionQuestionTypesService.createAssociation(currentNode.getId(), mathQuestionType.getId());

                saveQuestionAndPointsRelation(keyPointInfo, null , mathQuestionType.getId(), textbook, rootNode);
            }
        }
    }

    /**
     * 保存知识点或者题型、题目及关系
     * @param keyPointInfo
     * @param knowledgePointId
     * @param questionTypeId
     * @param textbook
     * @param chapter
     */
    private void saveQuestionAndPointsRelation(KeyPointInfo keyPointInfo, UUID knowledgePointId, UUID questionTypeId, Textbooks textbook, MathCatalogNode chapter) {
        keyPointInfo.getQuestions().forEach(question -> {
            List<UUID> questionFileIds = new ArrayList<>();
            String questionContent = question.getQuestionContent();
            if (CollUtil.isNotEmpty(question.getQuestionImages())) {
                for (String questionImage: question.getQuestionImages()) {
                    String imageLocalPath = StrUtil.format(imageLocalPathTpl, transTextbookName2Directory(textbook.getName()), chapter.getSortNo(), chapter.getName(), questionImage);
                    java.io.File file = FileUtil.file(imageLocalPath);
                    try {
                        OssFileVO ossFileVO = ossService.uploadInputStreamToOss(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB, "math-book-question", file.getName(), FileUtil.getInputStream(imageLocalPath), file.length());
                        String objectName = ossFileVO.getKey();
                        String originalImageType = objectName.endsWith(".jpg") ? "jpg" : "png";
                        String originalMimeType = originalImageType.equals("jpg") ? "image/jpeg" : "image/png";
                        File fileEntity = filesService.save(objectName.substring(objectName.lastIndexOf("/") + 1), originalImageType, originalMimeType, objectName, ossFileVO.getOssEnum());
                        questionFileIds.add(fileEntity.getId());
                        ImageData imageData = new ImageData(ossFileVO.getOssEnum(), objectName);
                        questionContent = questionContent.concat(ImageTagExtractor.buildImageTagHtmlNoSrc(imageData));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }

            MathQuestion mathQuestion = new MathQuestion();
            mathQuestion.setQuestionType(QuestionType.getByType(question.getQuestionType()));
            mathQuestion.setContent(questionContent);
            mathQuestion.setSource(QuestionSourceType.BOOK);
            mathQuestion.setEnabled(false);
            mathQuestion.setExistGraphics(CollUtil.isNotEmpty(questionFileIds));
            log.info("mathQuestion:{}", mathQuestion);
            mathQuestionsService.save(mathQuestion);
            if (knowledgePointId != null) {
                questionKnowledgePointsService.createAssociation(mathQuestion.getId(), knowledgePointId);
            }
            if (questionTypeId != null) {
                questionTypesMappingService.createAssociation(mathQuestion.getId(), questionTypeId);
            }

            if (CollUtil.isNotEmpty(questionFileIds)) {
                for (int i = 0; i < questionFileIds.size(); i++) {
                    questionFileService.save(QuestionFile.builder()
                            .fileId(questionFileIds.get(i))
                            .questionId(mathQuestion.getId())
                            .type(QuestionFileType.ATTACHMENT.getValue())
                            .sortNo(i + 1)
                            .build());
                }
            }

            List<UUID> answerFileIds = new ArrayList<>();
            String answerContent = question.getAnswerContent();
            if (CollUtil.isNotEmpty(question.getAnswerImages())) {
                for (String answerImage: question.getAnswerImages()) {
                    String imageLocalPath = StrUtil.format(imageLocalPathTpl, transTextbookName2Directory(textbook.getName()), chapter.getSortNo(), chapter.getName(), answerImage);
                    java.io.File file = FileUtil.file(imageLocalPath);
                    try {
                        OssFileVO ossFileVO = ossService.uploadInputStreamToOss(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB, "math-book-answer", file.getName(), FileUtil.getInputStream(imageLocalPath), file.length());
                        String objectName = ossFileVO.getKey();
                        String originalImageType = objectName.endsWith(".jpg") ? "jpg" : "png";
                        String originalMimeType = originalImageType.equals("jpg") ? "image/jpeg" : "image/png";
                        File fileEntity = filesService.save(objectName.substring(objectName.lastIndexOf("/") + 1), originalImageType, originalMimeType, objectName, ossFileVO.getOssEnum());
                        answerFileIds.add(fileEntity.getId());
                        ImageData imageData = new ImageData(ossFileVO.getOssEnum(), objectName);
                        answerContent = answerContent.concat(ImageTagExtractor.buildImageTagHtmlNoSrc(imageData));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }

            List<UUID> thinkingFileIds = new ArrayList<>();
            String thinkingContent = question.getThinkingContent();
            if (CollUtil.isNotEmpty(question.getThinkingImages())) {
                for (String thinkingImage: question.getThinkingImages()) {
                    String imageLocalPath = StrUtil.format(imageLocalPathTpl, transTextbookName2Directory(textbook.getName()), chapter.getSortNo(), chapter.getName(), thinkingImage);
                    java.io.File file = FileUtil.file(imageLocalPath);
                    try {
                        OssFileVO ossFileVO = ossService.uploadInputStreamToOss(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB, "math-book-answer", file.getName(), FileUtil.getInputStream(imageLocalPath), file.length());
                        String objectName = ossFileVO.getKey();
                        String originalImageType = objectName.endsWith(".jpg") ? "jpg" : "png";
                        String originalMimeType = originalImageType.equals("jpg") ? "image/jpeg" : "image/png";
                        File fileEntity = filesService.save(objectName.substring(objectName.lastIndexOf("/") + 1), originalImageType, originalMimeType, objectName, ossFileVO.getOssEnum());
                        thinkingFileIds.add(fileEntity.getId());
                        ImageData imageData = new ImageData(ossFileVO.getOssEnum(), objectName);
                        thinkingContent = thinkingContent.concat(ImageTagExtractor.buildImageTagHtmlNoSrc(imageData));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }

            MathAnswer mathAnswer = new MathAnswer();
            mathAnswer.setAnswer(answerContent);
            mathAnswer.setContent(thinkingContent);
            log.info("mathAnswer:{}", mathAnswer);
            mathAnswersService.save(mathAnswer);

            if (CollUtil.isNotEmpty(answerFileIds)) {
                for (int i = 0; i < answerFileIds.size(); i++) {
                    mathAnswerFilesService.save(MathAnswerFiles.builder()
                            .fileId(answerFileIds.get(i))
                            .answerId(mathAnswer.getId())
                            .type(AnswerFileType.ANSWER_ATTACHMENT.getType())
                            .sortNo(i + 1)
                            .build());
                }
            }
            if (CollUtil.isNotEmpty(thinkingFileIds)) {
                for (int i = 0; i < thinkingFileIds.size(); i++) {
                    mathAnswerFilesService.save(MathAnswerFiles.builder()
                            .fileId(thinkingFileIds.get(i))
                            .answerId(mathAnswer.getId())
                            .type(AnswerFileType.ANALYSIS_ATTACHMENT.getType())
                            .sortNo(i + 1)
                            .build());
                }
            }

            questionAnswerRelationsService.createAssociation(mathQuestion.getId(), mathAnswer.getId());
        });
    }

    private String transTextbookName2Directory(String name) {
        String gradeSemesterStr = name.split(" ")[2];
        return StrUtil.format("{}年级{}册", gradeSemesterStr.charAt(0), gradeSemesterStr.charAt(1));
    }

    @Transactional(rollbackFor = Throwable.class)
    public UUID saveTextbookInfo(TextbookParam textbookParam) {
        Textbooks textbook = textbooksService.lambdaQuery()
                .eq(Textbooks::getName, textbookParam.getTextbook())
                .eq(Textbooks::getGrade, textbookParam.getGrade())
                .eq(Textbooks::getSemester, textbookParam.getSemester())
                .eq(Textbooks::getPublisher, textbookParam.getPublisher())
                .one();
        if (textbook == null) {
            textbook = new Textbooks();
            textbook.setName(textbookParam.getTextbook());
            textbook.setSubject(textbookParam.getSubject());
            textbook.setPublisher(textbookParam.getPublisher());
            textbook.setGrade(textbookParam.getGrade());
            textbook.setSemester(textbookParam.getSemester());
            textbooksService.save(textbook);
        }

        final Textbooks finalTextbook = textbook;
        Set<MathCatalogNode> catalogNodes = textbookParam.getChapters()
                .stream()
                .map(chapterInfo -> {
                    return MathCatalogNode.builder()
                            .name(chapterInfo.getChapterName())
                            .sortNo(chapterInfo.getSortNo())
                            .textbookId(finalTextbook.getId())
                            .build();
                })
                .collect(Collectors.toSet());
        mathCatalogNodesService.saveBatch(catalogNodes);
        return textbook.getId();
    }
}
