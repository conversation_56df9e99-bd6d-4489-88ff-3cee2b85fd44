package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.mapper.MathQuestionDimensionMapper;
import com.joinus.knowledge.model.entity.MathQuestionDimension;
import com.joinus.knowledge.service.MathQuestionDimensionService;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class MathQuestionDimensionServiceImpl extends ServiceImpl<MathQuestionDimensionMapper, MathQuestionDimension> implements MathQuestionDimensionService {


    @Override
    public MathQuestionDimension getDimensionByQuestionId(UUID id) {
        return lambdaQuery().eq(MathQuestionDimension::getQuestionId, id)
                .one();
    }
}




