package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.mapper.ReadingPassageQuestionSetsMapper;
import com.joinus.knowledge.model.entity.ReadingPassageQuestionSets;
import com.joinus.knowledge.service.ReadingPassageQuestionSetsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class ReadingPassageQuestionSetsServiceImpl extends ServiceImpl<ReadingPassageQuestionSetsMapper, ReadingPassageQuestionSets> implements ReadingPassageQuestionSetsService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassageQuestionSets> updateWrapper = Wrappers.lambdaUpdate(ReadingPassageQuestionSets.class)
                .set(BaseEntity::getDeletedAt, LocalDateTime.now())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassageQuestionSets> updateWrapper = Wrappers.lambdaUpdate(ReadingPassageQuestionSets.class)
                .set(ReadingPassageQuestionSets::getIsEnabled, 1)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disable(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassageQuestionSets> updateWrapper = Wrappers.lambdaUpdate(ReadingPassageQuestionSets.class)
                .set(ReadingPassageQuestionSets::getIsEnabled, 0)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditPass(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassageQuestionSets> updateWrapper = Wrappers.lambdaUpdate(ReadingPassageQuestionSets.class)
                .set(ReadingPassageQuestionSets::getIsAudit, 1)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditNoPass(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassageQuestionSets> updateWrapper = Wrappers.lambdaUpdate(ReadingPassageQuestionSets.class)
                .set(ReadingPassageQuestionSets::getIsAudit, 2)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);


    }
}