package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.MathExamQuestion;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.service.MathExamQuestionsService;
import com.joinus.knowledge.mapper.MathExamQuestionsMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_exam_questions(试卷与题目关联表)】的数据库操作Service实现
* @createDate 2025-03-20 18:15:37
*/
@Service
public class MathExamQuestionsServiceImpl extends ServiceImpl<MathExamQuestionsMapper, MathExamQuestion>
    implements MathExamQuestionsService{

    @Override
    public List<MathQuestion> listQuestionsByExamId(UUID examId) {
        return baseMapper.listQuestionsByExamId(examId);
    }

    @Override
    public MathExamQuestion getOne(UUID examId, UUID questionId) {
        return lambdaQuery().eq(MathExamQuestion::getExamId, examId)
                .eq(MathExamQuestion::getQuestionId, questionId)
                .one();
    }

    @Override
    public void updateSortNo(UUID examId, UUID questionId, Integer sortNo) {
        lambdaUpdate().eq(MathExamQuestion::getExamId, examId)
                .eq(MathExamQuestion::getQuestionId, questionId)
                .set(MathExamQuestion::getSortNo, sortNo)
                .update();
    }

    @Override
    public void removeQuestion(UUID examId, UUID questionId) {
        remove(lambdaQuery()
                .eq(MathExamQuestion::getExamId, examId)
                .eq(MathExamQuestion::getQuestionId, questionId)
                .getWrapper());
    }

    @Override
    public void createRelations(UUID id, List<UUID> questionIds) {
        List<MathExamQuestion> existRelations = lambdaQuery().eq(MathExamQuestion::getExamId, id)
                .list();
        if (CollUtil.isNotEmpty(existRelations)) {
            remove(lambdaQuery().eq(MathExamQuestion::getExamId, id).getWrapper());
        }
        if (CollUtil.isNotEmpty(questionIds)) {
            List<MathExamQuestion> relations = questionIds.stream().map(questionId -> {
                MathExamQuestion relation = new MathExamQuestion();
                relation.setExamId(id);
                relation.setQuestionId(questionId);
                relation.setSortNo(questionIds.indexOf(questionId) + 1);
                return relation;
            }).toList();
            saveBatch(relations);
        }
    }
}




