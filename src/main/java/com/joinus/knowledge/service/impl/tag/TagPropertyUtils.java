package com.joinus.knowledge.service.impl.tag;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 标签属性工具类
 */
@Slf4j
public class TagPropertyUtils {
    
    /**
     * 检查标签的 properties 中 isPrimary 是否为 true
     */
    public static boolean isPrimaryTag(Object properties) {
        if (properties == null) {
            return false;
        }

        try {
            JSONObject jsonObject;
            if (properties instanceof JSONObject) {
                jsonObject = (JSONObject) properties;
            } else if (properties instanceof String) {
                String propertiesStr = (String) properties;
                if (propertiesStr.trim().isEmpty()) {
                    return false;
                }
                jsonObject = JSONUtil.parseObj(propertiesStr);
            } else {
                return false;
            }

            // 检查 isPrimary 字段
            return jsonObject.getBool("isPrimary", false);
        } catch (Exception e) {
            log.warn("解析 properties 时出错: {}", properties, e);
            return false;
        }
    }
}
