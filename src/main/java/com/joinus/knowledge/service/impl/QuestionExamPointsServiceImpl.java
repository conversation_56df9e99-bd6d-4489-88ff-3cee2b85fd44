package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.QuestionExamPoints;
import com.joinus.knowledge.service.QuestionExamPointsService;
import com.joinus.knowledge.mapper.QuestionExamPointsMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【question_exam_points】的数据库操作Service实现
* @createDate 2025-03-01 10:22:43
*/
@Service
public class QuestionExamPointsServiceImpl extends ServiceImpl<QuestionExamPointsMapper, QuestionExamPoints>
    implements QuestionExamPointsService{

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createAssociation(UUID questionId, UUID examPointId) {
        // 先检查是否已存在
        LambdaQueryWrapper<QuestionExamPoints> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionExamPoints::getQuestionId, questionId)
                  .eq(QuestionExamPoints::getExamPointId, examPointId);
                  
        QuestionExamPoints relation = getOne(queryWrapper);
        
        if (relation != null) {
            // 已存在则返回成功
            return true;
        }
        
        // 不存在则创建
        return baseMapper.insertQuestionExamPoints(questionId, examPointId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateAssociationsByQuestionId(UUID questionId, List<UUID> examPointIds) {
        // 删除该题目的所有关联
        baseMapper.deleteByQuestionId(questionId);
        
        // 没有新的关联
        if (examPointIds == null || examPointIds.isEmpty()) {
            return true;
        }
        
        // 添加新的关联
        for (UUID examPointId : examPointIds) {
            baseMapper.insertQuestionExamPoints(questionId, examPointId);
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateAssociationsByExamPointId(UUID examPointId, List<UUID> questionIds) {
        // 删除该考点的所有关联
        baseMapper.deleteByExamPointId(examPointId);
        
        // 没有新的关联
        if (questionIds == null || questionIds.isEmpty()) {
            return true;
        }
        
        // 添加新的关联
        for (UUID questionId : questionIds) {
            baseMapper.insertQuestionExamPoints(questionId, examPointId);
        }
        
        return true;
    }

    @Override
    public boolean deleteAssociation(UUID questionId, UUID examPointId) {
        return baseMapper.deleteByQuestionIdAndExamPointId(questionId, examPointId) > 0;
    }

    @Override
    public boolean deleteAssociationsByQuestionId(UUID questionId) {
        return baseMapper.deleteByQuestionId(questionId) > 0;
    }

    @Override
    public boolean deleteAssociationsByExamPointId(UUID examPointId) {
        return baseMapper.deleteByExamPointId(examPointId) > 0;
    }

    @Override
    public List<UUID> getExamPointIdsByQuestionId(UUID questionId) {
        List<QuestionExamPoints> relations = baseMapper.selectByQuestionId(questionId);
        return relations.stream()
                .map(relation -> (UUID) relation.getExamPointId())
                .collect(Collectors.toList());
    }

    @Override
    public List<UUID> getQuestionIdsByExamPointId(UUID examPointId) {
        List<QuestionExamPoints> relations = baseMapper.selectByExamPointId(examPointId);
        return relations.stream()
                .map(relation -> (UUID) relation.getQuestionId())
                .collect(Collectors.toList());
    }
}
