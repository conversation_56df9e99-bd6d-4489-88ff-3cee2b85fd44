package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.TextbookFile;
import com.joinus.knowledge.service.TextbookFileService;
import com.joinus.knowledge.mapper.TextbookFileMapper;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_textbook_files】的数据库操作Service实现
* @createDate 2025-03-07 10:56:25
*/
@Service
public class TextbookFileServiceImpl extends ServiceImpl<TextbookFileMapper, TextbookFile>
    implements TextbookFileService{

    @Override
    public void saveRelation(UUID textbookId, UUID fileId, int pageNo) {
        LambdaQueryWrapper<TextbookFile> wrapper = Wrappers.lambdaQuery(TextbookFile.class)
                .eq(TextbookFile::getTextbookId, textbookId)
                .eq(TextbookFile::getFileId, fileId);
        TextbookFile textbookFile = baseMapper.selectOne(wrapper);
        if (null == textbookFile) {
            textbookFile = TextbookFile.builder()
                    .textbookId(textbookId)
                    .fileId(fileId)
                    .pageNo(pageNo)
                    .build();
            baseMapper.insert(textbookFile);
        }
    }
}




