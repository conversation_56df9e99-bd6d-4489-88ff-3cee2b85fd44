package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.AnswerFileType;
import com.joinus.knowledge.model.dto.ImageData;
import com.joinus.knowledge.model.entity.MathAnswer;
import com.joinus.knowledge.model.param.UpdateQuestionAnswerParam;
import com.joinus.knowledge.model.vo.QuestionAnswerDetailVO;
import com.joinus.knowledge.service.MathAnswerFilesService;
import com.joinus.knowledge.service.MathAnswersService;
import com.joinus.knowledge.mapper.MathAnswersMapper;
import com.joinus.knowledge.service.MathQuestionsService;
import com.joinus.knowledge.service.QuestionAnswerRelationsService;
import com.joinus.knowledge.utils.ConverterUtils;
import com.joinus.knowledge.utils.ImageTagExtractor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.IntStream;

/**
* <AUTHOR>
* @description 针对表【math_answers】的数据库操作Service实现
* @createDate 2025-02-28 14:12:06
*/
@Service
public class MathAnswersServiceImpl extends ServiceImpl<MathAnswersMapper, MathAnswer>
    implements MathAnswersService {

    @Autowired
    private MathQuestionsService mathQuestionsService;
    @Autowired
    private MathAnswerFilesService mathAnswerFilesService;
    @Autowired
    private QuestionAnswerRelationsService questionAnswerRelationsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QuestionAnswerDetailVO updateById(UUID id, UpdateQuestionAnswerParam param) {
        String answer = param.getAnswer();
        if (StrUtil.isNotBlank(answer)) {
            answer = ConverterUtils.clearImgSrc(answer);
        }

        String content = param.getContent();
        if (StrUtil.isNotBlank(content)) {
            content = ConverterUtils.clearImgSrc(content);
        }

        mathAnswerFilesService.removeByAnswerId(id);
        List<ImageData> imageDataList = ImageTagExtractor.extractImageData(answer);
        IntStream.range(0, imageDataList.size()).forEach(i -> {
            ImageData data = imageDataList.get(i);
            data.setType(AnswerFileType.ANSWER_ATTACHMENT.getType());
            data.setSortNo(i + 1);
        });

        List<ImageData> analysisImageDataList = ImageTagExtractor.extractImageData(content);
        IntStream.range(0, analysisImageDataList.size()).forEach(i -> {
            ImageData data = analysisImageDataList.get(i);
            data.setType(AnswerFileType.ANALYSIS_ATTACHMENT.getType());
            data.setSortNo(i + 1);
        });

        imageDataList.addAll(analysisImageDataList);
        if (CollUtil.isNotEmpty(imageDataList)) {
            mathAnswerFilesService.saveAnswerFileAndRelation(imageDataList, id);
        }

        lambdaUpdate().eq(MathAnswer::getId, id)
                .set(StrUtil.isNotBlank(answer) , MathAnswer::getAnswer, answer)
                .set(StrUtil.isNotBlank(content), MathAnswer::getContent, content)
                .set(MathAnswer::getUpdatedAt, new Date())
                .update();
        return getById(id);
    }

    @Override
    public QuestionAnswerDetailVO getById(UUID id) {
        MathAnswer mathAnswer = baseMapper.selectById(id);
        QuestionAnswerDetailVO questionAnswerDetailVO = BeanUtil.copyProperties(mathAnswer, QuestionAnswerDetailVO.class);

        if (StrUtil.isNotBlank(mathAnswer.getContent())) {
            questionAnswerDetailVO.setContent(mathQuestionsService.decodeContentV2(mathAnswer.getContent()));
        }
        if (StrUtil.isNotBlank(mathAnswer.getAnswer())) {
            questionAnswerDetailVO.setAnswer(mathQuestionsService.decodeContentV2(mathAnswer.getAnswer()));
        }

        return questionAnswerDetailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAnswer(MathAnswer mathAnswer) {
        String answer = mathAnswer.getAnswer();

        if (StrUtil.isNotBlank(answer)) {
            answer = ConverterUtils.clearImgSrc(answer);
            mathAnswer.setAnswer(answer);
        }

        String content = mathAnswer.getContent();
        if (StrUtil.isNotBlank(content)) {
            content = ConverterUtils.clearImgSrc(content);
            mathAnswer.setContent(content);
        }

        boolean result = this.save(mathAnswer);

        List<ImageData> imageDataList = ImageTagExtractor.extractImageData(answer);
        IntStream.range(0, imageDataList.size()).forEach(i -> {
            ImageData data = imageDataList.get(i);
            data.setType(AnswerFileType.ANSWER_ATTACHMENT.getType());
            data.setSortNo(i + 1);
        });

        List<ImageData> analysisImageDataList = ImageTagExtractor.extractImageData(content);
        IntStream.range(0, analysisImageDataList.size()).forEach(i -> {
            ImageData data = analysisImageDataList.get(i);
            data.setType(AnswerFileType.ANALYSIS_ATTACHMENT.getType());
            data.setSortNo(i + 1);
        });

        imageDataList.addAll(analysisImageDataList);
        if (CollUtil.isNotEmpty(imageDataList)) {
            mathAnswerFilesService.saveAnswerFileAndRelation(imageDataList, mathAnswer.getId());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAnswerAndRelation(UUID questionId, MathAnswer mathAnswer) {
        String answer = mathAnswer.getAnswer();

        if (StrUtil.isNotBlank(answer)) {
            answer = ConverterUtils.clearImgSrc(answer);
            mathAnswer.setAnswer(answer);
        }

        String content = mathAnswer.getContent();
        if (StrUtil.isNotBlank(content)) {
            content = ConverterUtils.clearImgSrc(content);
            mathAnswer.setContent(content);
        }

        boolean result = this.save(mathAnswer);

        List<ImageData> imageDataList = ImageTagExtractor.extractImageData(answer);
        IntStream.range(0, imageDataList.size()).forEach(i -> {
            ImageData data = imageDataList.get(i);
            data.setType(AnswerFileType.ANSWER_ATTACHMENT.getType());
            data.setSortNo(i + 1);
        });

        List<ImageData> analysisImageDataList = ImageTagExtractor.extractImageData(content);
        IntStream.range(0, analysisImageDataList.size()).forEach(i -> {
            ImageData data = analysisImageDataList.get(i);
            data.setType(AnswerFileType.ANALYSIS_ATTACHMENT.getType());
            data.setSortNo(i + 1);
        });

        imageDataList.addAll(analysisImageDataList);
        if (CollUtil.isNotEmpty(imageDataList)) {
            mathAnswerFilesService.saveAnswerFileAndRelation(imageDataList, mathAnswer.getId());
        }

        questionAnswerRelationsService.createAssociation(questionId, mathAnswer.getId());
        return result;
    }

}




