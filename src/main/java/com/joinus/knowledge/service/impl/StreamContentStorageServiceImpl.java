package com.joinus.knowledge.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.knowledge.model.entity.UserLog;
import com.joinus.knowledge.model.param.SolveQuestionParam;
import com.joinus.knowledge.service.StreamContentStorageService;
import com.joinus.knowledge.service.UserLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import jakarta.annotation.Resource;

import java.util.Map;

/**
 * 流式内容存储服务实现
 */
@Slf4j
@Service
public class StreamContentStorageServiceImpl implements StreamContentStorageService {
    
    @Resource
    private UserLogService userLogService;
    
    @Override
    public Mono<String> storeContent(
            String requestId,
            SolveQuestionParam param,
            String content,
            long processingTime,
            String sourceIp,
            String sourcePath) {

        // 使用 Schedulers.boundedElastic() 确保在单独的线程池中执行，不阻塞主线程
        return Mono.fromCallable(() -> {
            // 打印收集到的完整内容和请求参数（仅用于演示）
            log.info("============ 流式响应完整内容 ============");
            log.info("请求ID: {}", requestId);
            log.info("提示词: {}", param.getPrompt());
            log.info("处理时间: {}ms", processingTime);
            log.info("请求参数: {}", JSONUtil.toJsonStr(param));
            log.info("响应内容长度: {} 字符", content.length());
            log.info("响应内容摘要: {}", content.length() > 100 ? content.substring(0, 100) + "..." : content);

            // 根据前端传入的存储目标参数决定存储位置
            String result = saveToDatabase(requestId, param, content, processingTime, sourceIp, sourcePath);

            return result != null ? result : "logged-only";
        }).subscribeOn(Schedulers.boundedElastic()); // 确保在单独的线程池中执行
    }

    private String saveToDatabase(String requestId, SolveQuestionParam param, String content, long processingTime, String sourceIp, String sourcePath) throws JsonProcessingException {
        log.info("将流式响应内容存储到数据库，请求ID: {}", requestId);

        Map<Object, Object> map = MapUtil.builder()
                .put("content", content)
                .build();
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonContent = objectMapper.writeValueAsString(map);
        UserLog userLog = UserLog.builder()
                .name(requestId)
                .content(jsonContent)
                .build();
        userLogService.save(userLog);
        return "database-" + requestId;
    }

    /**
     * 示例方法：存储到用户日志表
     */
    private String saveToUserLog(String requestId, SolveQuestionParam param, String content) {
        log.info("将流式响应内容存储到用户日志表，请求ID: {}", requestId);
        // 这里只是示例，实际实现需要根据UserLogService的具体方法
        // 例如：return userLogService.saveLog(requestId, content);
        return "user-log-" + requestId;
    }
    
    /**
     * 示例方法：存储到自定义表
     */
    private String saveToCustomTable(String requestId, SolveQuestionParam param, String content, long processingTime) {
        log.info("将流式响应内容存储到自定义表，请求ID: {}, 自定义参数: {}", requestId);
        // 这里只是示例，实际实现需要根据具体的自定义表和服务
        return "custom-" + requestId;
    }
}
