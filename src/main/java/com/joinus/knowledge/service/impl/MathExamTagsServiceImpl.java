package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.ExamTagType;
import com.joinus.knowledge.enums.RegularExamType;
import com.joinus.knowledge.mapper.MathExamTagsMapper;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathExamTag;
import com.joinus.knowledge.model.param.AddExamTagByExcelParam;
import com.joinus.knowledge.model.param.AddExamTagParam;
import com.joinus.knowledge.service.MathExamTagsService;
import com.joinus.knowledge.service.impl.tag.TagConstraintResult;
import com.joinus.knowledge.service.impl.tag.TagConstraintValidator;
import com.joinus.knowledge.service.impl.tag.TagPropertyUtils;
import com.joinus.knowledge.utils.ConverterUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_exam_tags(试卷标签表)】的数据库操作Service实现
* @createDate 2025-07-21 09:19:09
*/
@Slf4j
@Service
public class MathExamTagsServiceImpl extends ServiceImpl<MathExamTagsMapper, MathExamTag>
    implements MathExamTagsService{

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void addExamTags(UUID examId, List<AddExamTagParam> params) {
        if (examId == null || params == null || params.isEmpty()) {
            return;
        }

        // 1. 获取该试卷的所有现有标签（使用 FOR UPDATE 锁定，防止并发插入）
        List<MathExamTag> existTagsList = lambdaQuery()
                .eq(MathExamTag::getExamId, examId)
                .last("FOR UPDATE")
                .list();

        // 2. 将现有标签转换为Set，用于快速查找（基于type和value的组合）
        Set<String> existTagKeys = new HashSet<>();
        for (MathExamTag tag : existTagsList) {
            String normalizedProperties = normalizePropertiesString(tag.getProperties());
            existTagKeys.add(buildTagKey(tag.getType(), tag.getValue(), normalizedProperties));
        }

        // 3. 初始化标签约束验证器
        TagConstraintValidator constraintValidator = new TagConstraintValidator(existTagsList);

        // 4. 过滤出需要新增的标签，同时避免本次请求中的重复标签
        List<MathExamTag> tagsToAdd = new ArrayList<>();
        Set<String> currentBatchKeys = new HashSet<>();

        for (AddExamTagParam param : params) {
            if (param == null || param.getType() == null || param.getValue() == null) {
                continue;
            }

            String propertiesStr = normalizePropertiesString(param.getProperties());
            String tagKey = buildTagKey(param.getType(), param.getValue(), propertiesStr);

            // 检查是否已存在于数据库中，或者在当前批次中已经添加过
            if (!existTagKeys.contains(tagKey) && !currentBatchKeys.contains(tagKey)) {

                // 使用约束验证器检查业务约束
                TagConstraintResult constraintResult = constraintValidator.validateTag(examId, param);
                if (!constraintResult.isValid()) {
                    log.warn("标签约束验证失败: {}", constraintResult.getMessage());
                    throw new BusinessException(constraintResult.getMessage());
                }

                // 创建新标签
                MathExamTag newTag = new MathExamTag();
                newTag.setExamId(examId);
                newTag.setType(param.getType());
                newTag.setValue(param.getValue());
                newTag.setProperties(propertiesStr);

                tagsToAdd.add(newTag);
                currentBatchKeys.add(tagKey);

                // 更新约束验证器状态
                constraintValidator.recordAddedTag(param);
            } else {
                log.warn("标签已存在，跳过插入: examId={}, type={}, value={}",
                        examId, param.getType(), param.getValue());
            }
        }

        // 4. 批量保存不存在的标签
        if (!tagsToAdd.isEmpty()) {
            // 在保存前再次检查，防止在锁定期间有其他事务插入了相同数据
            insertTagsWithDuplicateCheck(tagsToAdd);
        }
    }

    /**
     * 带重复检查的插入方法
     */
    private void insertTagsWithDuplicateCheck(List<MathExamTag> tagsToAdd) {
        for (MathExamTag tag : tagsToAdd) {
            try {
                // 逐个插入，如果出现重复则忽略
                save(tag);
            } catch (Exception e) {
                // 如果是重复键异常，记录日志但不抛出异常
                if (e.getMessage() != null && (e.getMessage().contains("duplicate") ||
                    e.getMessage().contains("Duplicate") || e.getMessage().contains("重复"))) {
                    log.warn("标签已存在，跳过插入: examId={}, type={}, value={}",
                            tag.getExamId(), tag.getType(), tag.getValue());
                } else {
                    // 其他异常继续抛出
                    throw e;
                }
            }
        }
    }



    /**
     * 标准化 properties 字符串，确保格式一致
     */
    private String normalizePropertiesString(Object properties) {
        if (properties == null) {
            return null;
        }

        if (properties instanceof JSONObject) {
            // 如果是 JSONObject，转换为标准化的 JSON 字符串
            return ((JSONObject) properties).toString();
        } else if (properties instanceof String) {
            String propertiesStr = (String) properties;
            if (propertiesStr.trim().isEmpty()) {
                return null;
            }
            try {
                // 尝试解析为 JSONObject 再转换回字符串，确保格式一致
                JSONObject jsonObject = JSONUtil.parseObj(propertiesStr);
                return jsonObject.toString();
            } catch (Exception e) {
                // 如果解析失败，直接返回原字符串
                log.warn("无法解析 properties 为 JSON: {}", propertiesStr);
                return propertiesStr;
            }
        } else {
            return properties.toString();
        }
    }

    /**
     * 构建标签的唯一键，用于去重判断
     */
    private String buildTagKey(ExamTagType type, String value, String properties) {
        return type + "|" + value + "|" + (properties == null ? "" : properties);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addExamTagsByExcel(List<AddExamTagByExcelParam> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 按试卷ID分组，批量处理相同试卷的标签
        Map<UUID, List<AddExamTagParam>> examTagsMap = dataList.stream()
                .filter(data -> data != null && data.getExamId() != null &&
                               data.getType() != null && data.getValue() != null)
                .collect(Collectors.groupingBy(
                    AddExamTagByExcelParam::getExamId,
                    Collectors.mapping(data -> AddExamTagParam.builder()
                            .type(data.getType())
                            .value(data.getValue())
                            .properties(data.getProperties())
                            .build(),
                    Collectors.toList())
                ));

        // 为每个试卷批量添加标签
        for (Map.Entry<UUID, List<AddExamTagParam>> entry : examTagsMap.entrySet()) {
            addExamTags(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 设置主要别名标签（会将其他 ALIAS 标签的 isPrimary 设为 false）
     */
    @Transactional(rollbackFor = Exception.class)
    public void setPrimaryAlias(UUID examId, String aliasValue, JSONObject properties) {
        if (examId == null || aliasValue == null) {
            throw new IllegalArgumentException("examId 和 aliasValue 不能为空");
        }

        // 1. 将现有的所有 ALIAS 标签的 isPrimary 设为 false
        List<MathExamTag> existingAliasTags = lambdaQuery()
                .eq(MathExamTag::getExamId, examId)
                .eq(MathExamTag::getType, ExamTagType.ALIAS)
                .list();

        for (MathExamTag tag : existingAliasTags) {
            if (TagPropertyUtils.isPrimaryTag(tag.getProperties())) {
                // 更新 properties，将 isPrimary 设为 false
                JSONObject updatedProperties = updateIsPrimary(tag.getProperties(), false);
                tag.setProperties(normalizePropertiesString(updatedProperties));
                updateById(tag);
                log.info("将试卷 {} 的 ALIAS 标签 '{}' 的 isPrimary 设为 false", examId, tag.getValue());
            }
        }

        // 2. 添加新的主要别名标签
        JSONObject newProperties = properties != null ? properties : new JSONObject();
        newProperties.put("isPrimary", true);

        AddExamTagParam primaryAliasParam = AddExamTagParam.builder()
                .type(ExamTagType.ALIAS)
                .value(aliasValue)
                .properties(newProperties)
                .build();

        addExamTags(examId, Collections.singletonList(primaryAliasParam));
        log.info("为试卷 {} 设置新的主要别名: '{}'", examId, aliasValue);
    }

    @Override
    public MathExam checkExistByAlias(String examName) {
        return baseMapper.checkExistByAlias(examName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdateAliases(UUID id, List<String> aliases) {
        if (id == null || CollUtil.isEmpty(aliases)) {
            return;
        }

        List<String> rebuildedAliases = aliases.stream().map(alias -> ConverterUtils.rebuildExamName(alias)).toList();

        List<MathExamTag> aliasTags = lambdaQuery().eq(MathExamTag::getExamId, id)
                .eq(MathExamTag::getType, ExamTagType.ALIAS)
                .list();

        if (CollUtil.isEmpty(aliasTags)) {
            // 添加别名，aliases的第一个设置为主别名，其他设置为辅别名
            addNewAliases(id, rebuildedAliases);
        } else {
            // 比较aliasTags和aliases，aliasTags按照isPrimary排序（主别名在前面），
            // aliasTags和aliases一致的话，不做任何修改，如果不一致，则把aliasTags全部删了，然后添加新的别名组

            // 按照isPrimary排序，主别名在前面
            List<String> existingAliasValues = aliasTags.stream()
                    .sorted((tag1, tag2) -> {
                        boolean isPrimary1 = TagPropertyUtils.isPrimaryTag(tag1.getProperties());
                        boolean isPrimary2 = TagPropertyUtils.isPrimaryTag(tag2.getProperties());
                        // 主别名排在前面
                        return Boolean.compare(isPrimary2, isPrimary1);
                    })
                    .map(MathExamTag::getValue)
                    .collect(Collectors.toList());

            // 比较现有别名和新别名是否一致
            if (!existingAliasValues.equals(rebuildedAliases)) {
                // 不一致，删除所有现有别名标签
                baseMapper.delete(new QueryWrapper<MathExamTag>().eq("exam_id", id).eq("type", ExamTagType.ALIAS));
                log.info("删除试卷 {} 的所有现有别名标签", id);

                // 添加新的别名组
                addNewAliases(id, rebuildedAliases);
            } else {
                log.info("试卷 {} 的别名列表未发生变化，无需更新", id);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdateEliteSchoolExamPaperValues(UUID id, List<String> eliteSchoolExamPaperValues) {
        if (id == null || CollUtil.isEmpty(eliteSchoolExamPaperValues)) {
            return;
        }

        List<MathExamTag> existTags = lambdaQuery().eq(MathExamTag::getExamId, id)
                .eq(MathExamTag::getType, ExamTagType.ELITE_SCHOOL_EXAM_PAGERS)
                .list();

        if (CollUtil.isEmpty(existTags)) {
            // 添加新的精英学校试卷标签
            addNewExamTags(id, eliteSchoolExamPaperValues, ExamTagType.ELITE_SCHOOL_EXAM_PAGERS);
        } else {
            // 比较existTags和eliteSchoolExamPaperValues，如果里面的值都一样，则不做任何修改，如果不一致，则把existTags全部删了，然后添加新的标签组

            // 获取现有标签的值列表
            List<String> existingValues = existTags.stream()
                    .map(MathExamTag::getValue)
                    .sorted() // 排序以便比较
                    .collect(Collectors.toList());

            // 对新值列表也进行排序
            List<String> sortedNewValues = eliteSchoolExamPaperValues.stream()
                    .filter(value -> value != null && !value.trim().isEmpty())
                    .map(String::trim)
                    .sorted()
                    .collect(Collectors.toList());

            // 比较两个列表是否一致
            if (!existingValues.equals(sortedNewValues)) {
                // 不一致，删除所有现有标签
                baseMapper.delete(new QueryWrapper<MathExamTag>().eq("exam_id", id).eq("type", ExamTagType.ELITE_SCHOOL_EXAM_PAGERS));
                log.info("删除试卷 {} 的所有现有精英学校试卷标签", id);

                // 添加新的标签组
                addNewExamTags(id, eliteSchoolExamPaperValues, ExamTagType.ELITE_SCHOOL_EXAM_PAGERS);
            } else {
                log.info("试卷 {} 的精英学校试卷标签列表未发生变化，无需更新", id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addOrUpdateRegularExamType(UUID id, RegularExamType regularExamType) {
        if (id == null || null == regularExamType) {
            return;
        }

        List<MathExamTag> existTags = lambdaQuery().eq(MathExamTag::getExamId, id)
                .eq(MathExamTag::getType, ExamTagType.REGULAR_EXAM_TYPE)
                .list();

        if (CollUtil.isEmpty(existTags)) {
            // 添加新的精英学校试卷标签
            addNewExamTags(id, CollUtil.toList(regularExamType.getValue()), ExamTagType.REGULAR_EXAM_TYPE);
        } else {
            if (existTags.size()> 1 || !existTags.get(0).getValue().equals(regularExamType.getValue())) {
                baseMapper.delete(new QueryWrapper<MathExamTag>().eq("exam_id", id).eq("type", ExamTagType.REGULAR_EXAM_TYPE));
                addNewExamTags(id, CollUtil.toList(regularExamType.getValue()), ExamTagType.REGULAR_EXAM_TYPE);
            }
        }
    }

    @Override
    public List<MathExamTag> listByExamId(UUID id) {
        return lambdaQuery().eq(MathExamTag::getExamId, id).list();
    }

    @Override
    public List<MathExamTag> listByExamIds(List<UUID> examIdList) {
        return lambdaQuery().in(MathExamTag::getExamId, examIdList).list();
    }

    /**
     * 添加新的精英学校试卷标签组
     */
    private void addNewExamTags(UUID examId, List<String> values, ExamTagType examTagType) {
        List<MathExamTag> newTags = new ArrayList<>();

        for (String value : values) {
            if (value == null || value.trim().isEmpty()) {
                continue;
            }

            MathExamTag newTag = MathExamTag.builder()
                    .examId(examId)
                    .type(examTagType)
                    .value(value.trim())
                    .build();

            newTags.add(newTag);
        }

        if (!newTags.isEmpty()) {
            saveBatch(newTags);
            log.info("为试卷 {} 添加了 {} 个试卷标签", examId, newTags.size());
        }
    }

    /**
     * 添加新的别名组，第一个设置为主别名，其他设置为辅别名
     */
    private void addNewAliases(UUID examId, List<String> aliases) {
        List<AddExamTagParam> aliasParams = new ArrayList<>();

        for (int i = 0; i < aliases.size(); i++) {
            String alias = aliases.get(i);
            if (alias == null || alias.trim().isEmpty()) {
                continue;
            }

            // 第一个别名设置为主别名，其他设置为辅别名
            boolean isPrimary = (i == 0);
            JSONObject properties = new JSONObject();
            properties.put("isPrimary", isPrimary);

            AddExamTagParam aliasParam = AddExamTagParam.builder()
                    .type(ExamTagType.ALIAS)
                    .value(ConverterUtils.rebuildExamName(alias))
                    .properties(properties)
                    .build();

            aliasParams.add(aliasParam);
        }

        if (!aliasParams.isEmpty()) {
            addExamTags(examId, aliasParams);
            log.info("为试卷 {} 添加了 {} 个别名，主别名: '{}'", examId, aliasParams.size(), aliases.get(0));
        }
    }


    /**
     * 更新 properties 中的 isPrimary 字段
     */
    private JSONObject updateIsPrimary(Object properties, boolean isPrimary) {
        JSONObject jsonObject;

        if (properties instanceof JSONObject) {
            jsonObject = new JSONObject(((JSONObject) properties));
        } else if (properties instanceof String) {
            try {
                jsonObject = JSONUtil.parseObj((String) properties);
            } catch (Exception e) {
                jsonObject = new JSONObject();
            }
        } else {
            jsonObject = new JSONObject();
        }

        jsonObject.put("isPrimary", isPrimary);
        return jsonObject;
    }
}




