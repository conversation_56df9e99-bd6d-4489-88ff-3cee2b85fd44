package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.PromptTemplate;
import com.joinus.knowledge.service.PromptTemplateService;
import com.joinus.knowledge.mapper.PromptTemplateMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【prompt_templates(提示词模版)】的数据库操作Service实现
* @createDate 2025-03-18 09:30:37
*/
@Service
public class PromptTemplateServiceImpl extends ServiceImpl<PromptTemplateMapper, PromptTemplate>
    implements PromptTemplateService{

}




