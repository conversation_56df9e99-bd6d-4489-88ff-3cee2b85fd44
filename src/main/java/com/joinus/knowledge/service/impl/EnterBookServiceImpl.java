package com.joinus.knowledge.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.Textbooks;
import com.joinus.knowledge.model.vo.EnterBookSaveDerivativeParam;
import com.joinus.knowledge.model.vo.EnterBookSaveFileParam;
import com.joinus.knowledge.service.EnterBookService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class EnterBookServiceImpl implements EnterBookService {

    @Value("${server.domain.edu-knowledge-hub.pro:https://edu-knowledge-hub.qingyulan.net}")
    private String domain;

    private static Integer readTimeoutSeconds = 10 * 1000;
    @Override
    public Textbooks getBookById(UUID id) {
        String url = StrUtil.format("{}/api/edu-knowledge-hub/textbooks/{}", domain, id);
        log.info("获取教材信息请求: URL={}, ID={}", url, id);
        
        HttpResponse response = HttpUtil.createGet(url)
                .setReadTimeout(readTimeoutSeconds)
                .execute();
        
        log.info("获取教材信息响应: status={}, body={}", response.getStatus(), response.body());
        
        if (response.isOk()) {
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            if (jsonObject.get("code").equals(200)) {
                return JSONUtil.toBean(jsonObject.getJSONObject("data"), Textbooks.class);
            }
        }
        return null;
    }
    
    @Override
    public File getFileById(UUID id) {
        String url = StrUtil.format("{}/api/edu-knowledge-hub/textbooks/entering-books/file/{}", domain, id);
        log.info("获取文件信息请求: URL={}, ID={}", url, id);
        
        HttpResponse response = HttpUtil.createGet(url)
                .setReadTimeout(readTimeoutSeconds)
                .execute();
        
        log.info("获取文件信息响应: status={}, body={}", response.getStatus(), response.body());
        
        if (response.isOk()) {
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            if (jsonObject.get("code").equals(200)) {
                return JSONUtil.toBean(jsonObject.getJSONObject("data"), File.class);
            }
        }
        log.error("获取文件信息失败: {}", response.body());
        return null;
    }
    
    @Override
    public boolean checkExistPage(UUID textbookId, int pageNo) {
        String url = StrUtil.format("{}/api/edu-knowledge-hub/textbooks/entering-books/{}/check", domain, textbookId);
        log.info("检查页面是否存在请求: URL={}, textbookId={}, pageNo={}", url, textbookId, pageNo);
        
        HttpResponse response = HttpUtil.createGet(url)
                .form("pageNo", pageNo)
                .setReadTimeout(readTimeoutSeconds)
                .execute();
        
        log.info("检查页面是否存在响应: status={}, body={}", response.getStatus(), response.body());
        
        if (response.isOk()) {
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            if (jsonObject.get("code").equals(200)) {
                return jsonObject.getBool("data", false);
            }
        }
        return false;
    }
    
    @Override
    public UUID saveFile(UUID textbookId, String fileName, String fileType, String mimeType, String ossKey, OssEnum ossEnum, int pageNo) {
        String url = StrUtil.format("{}/api/edu-knowledge-hub/textbooks/entering-books/{}/file", domain, textbookId);
        
        EnterBookSaveFileParam param = new EnterBookSaveFileParam();
        param.setOssEnum(ossEnum);
        param.setOssKey(ossKey);
        param.setPageNo(pageNo);
        param.setOriginalImageType(fileType);
        param.setOriginalMimeType(mimeType);
        
        String requestBody = JSONUtil.toJsonStr(param);
        log.info("保存文件请求: URL={}, textbookId={}, requestBody={}", url, textbookId, requestBody);
        
        HttpResponse response = HttpUtil.createPost(url)
                .body(requestBody)
                .header("Content-Type", "application/json")
                .setReadTimeout(readTimeoutSeconds)
                .execute();
        
        log.info("保存文件响应: status={}, body={}", response.getStatus(), response.body());
        
        if (response.isOk()) {
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            if (jsonObject.get("code").equals(200)) {
                String fileId = jsonObject.getStr("data");
                return UUID.fromString(fileId);
            }
        }
        log.error("保存文件失败: {}", response.body());
        return null;
    }
    
    @Override
    public boolean saveFileDerivative(UUID fileId, String derivativeType, String storagePath, String format, Integer width, Integer height, Long fileSize) {
        String url = StrUtil.format("{}/api/edu-knowledge-hub/textbooks/entering-books/file-derivative", domain);
        
        EnterBookSaveDerivativeParam param = new EnterBookSaveDerivativeParam();
        param.setFileId(fileId);
        param.setDerivativeType(derivativeType);
        param.setStoragePath(storagePath);
        param.setFormat(format);
        param.setWidth(width);
        param.setHeight(height);
        param.setFileSize(fileSize);
        
        String requestBody = JSONUtil.toJsonStr(param);
        log.info("保存衍生文件请求: URL={}, fileId={}, requestBody={}", url, fileId, requestBody);
        
        HttpResponse response = HttpUtil.createPost(url)
                .body(requestBody)
                .header("Content-Type", "application/json")
                .setReadTimeout(readTimeoutSeconds)
                .execute();
        
        log.info("保存衍生文件响应: status={}, body={}", response.getStatus(), response.body());
        
        if (response.isOk()) {
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            if (jsonObject.get("code").equals(200)) {
                return jsonObject.getBool("data", false);
            }
        }
        log.error("保存衍生文件失败: {}", response.body());
        return false;
    }
}
