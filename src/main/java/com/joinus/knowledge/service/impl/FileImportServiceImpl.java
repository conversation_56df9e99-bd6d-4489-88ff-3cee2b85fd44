package com.joinus.knowledge.service.impl;

import cn.hutool.core.util.StrUtil;
import com.joinus.knowledge.model.entity.FileDerivative;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.mapper.FileDerivativeMapper;
import com.joinus.knowledge.mapper.FilesMapper;
import com.joinus.knowledge.service.FileImportService;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.utils.ImageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件导入服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileImportServiceImpl implements FileImportService {

    private final FilesMapper filesMapper;
    private final FileDerivativeMapper fileDerivativeMapper;

    private static final List<String> SUPPORTED_IMAGE_EXTENSIONS = Arrays.asList("jpg", "jpeg", "png");
    private static final List<String> SUPPORTED_IMAGE_MIME_TYPES = Arrays.asList("image/jpeg", "image/png");
    private final FilesService filesService;

    /**
     * 判断文件是否为图片
     */
    private boolean isImageFile(java.io.File file) {
        String fileName = file.getName().toLowerCase();
        return SUPPORTED_IMAGE_EXTENSIONS.stream()
                .anyMatch(ext -> fileName.endsWith("." + ext));
    }
    
    /**
     * 获取文件MIME类型
     */
    private String getMimeType(java.io.File file) throws IOException {
        String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (fileName.endsWith(".png")) {
            return "image/png";
        }
        return Files.probeContentType(file.toPath());
    }
    
    /**
     * 创建并保存缩略图
     */
    private void createAndSaveThumbnail(File fileRecord, BufferedImage originalImage, String fileExtension, String parentDir) throws IOException {
        // 这里简化处理，实际应用中应该根据需求调整缩略图尺寸和质量
        int thumbWidth = 200;
        int thumbHeight = originalImage.getHeight() * thumbWidth / originalImage.getWidth();
        
        BufferedImage thumbnail = ImageUtils.resizeImage(originalImage, thumbWidth, thumbHeight);
        
        // 保存缩略图到本地
        String baseName = fileRecord.getName().substring(0, fileRecord.getName().lastIndexOf('.'));
        String thumbnailName = StrUtil.replace(baseName , "origin", "thumbnail") + "." + fileExtension;
        java.io.File thumbnailFile = new java.io.File(parentDir, thumbnailName);
        ImageIO.write(thumbnail, fileExtension, thumbnailFile);
        
        // 保存缩略图记录
        FileDerivative thumbnailDerivative = new FileDerivative();
        thumbnailDerivative.setId(UUID.randomUUID());
        thumbnailDerivative.setFileId(fileRecord.getId());
        thumbnailDerivative.setDerivativeType("thumbnail");
        thumbnailDerivative.setStoragePath(thumbnailFile.getAbsolutePath());
        thumbnailDerivative.setFormat(fileExtension);
        thumbnailDerivative.setWidth(thumbWidth);
        thumbnailDerivative.setHeight(thumbHeight);
        thumbnailDerivative.setFileSize(thumbnailFile.length());
        fileDerivativeMapper.insert(thumbnailDerivative);
        
        log.info("保存缩略图衍生记录: {}, 衍生ID: {}", thumbnailName, thumbnailDerivative.getId());
    }
    
    /**
     * 创建并保存预览图
     */
    private void createAndSavePreview(File fileRecord, BufferedImage originalImage, String fileExtension, String parentDir) throws IOException {
        // 预览图，可以稍大一些
        int previewWidth = 800;
        int previewHeight = originalImage.getHeight() * previewWidth / originalImage.getWidth();
        
        BufferedImage preview = ImageUtils.resizeImage(originalImage, previewWidth, previewHeight);
        
        // 保存预览图到本地
        String baseName = fileRecord.getName().substring(0, fileRecord.getName().lastIndexOf('.'));
        String previewName =  StrUtil.replace(baseName , "origin", "preview") + "." + fileExtension;
        java.io.File previewFile = new java.io.File(parentDir, previewName);
        ImageIO.write(preview, fileExtension, previewFile);
        
        // 保存预览图记录
        FileDerivative previewDerivative = new FileDerivative();
        previewDerivative.setId(UUID.randomUUID());
        previewDerivative.setFileId(fileRecord.getId());
        previewDerivative.setDerivativeType("preview");
        previewDerivative.setStoragePath(previewFile.getAbsolutePath());
        previewDerivative.setFormat(fileExtension);
        previewDerivative.setWidth(previewWidth);
        previewDerivative.setHeight(previewHeight);
        previewDerivative.setFileSize(previewFile.length());
        fileDerivativeMapper.insert(previewDerivative);
        
        log.info("保存预览图衍生记录: {}, 衍生ID: {}", previewName, previewDerivative.getId());
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1).toLowerCase();
        }
        return "";
    }
}
