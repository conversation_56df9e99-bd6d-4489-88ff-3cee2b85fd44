package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.enums.ContentPartType;
import com.joinus.knowledge.enums.ModelProviderType;
import com.joinus.knowledge.enums.ModelThinkingType;
import com.joinus.knowledge.model.entity.Conversation;
import com.joinus.knowledge.model.param.ChatCompletionWithExtraBodyRequest;
import com.joinus.knowledge.model.param.CustomChatCompletionRequest;
import com.joinus.knowledge.model.po.ContentPart;
import com.joinus.knowledge.model.response.StreamResponse;
import com.joinus.knowledge.service.AIChatService;
import com.joinus.knowledge.utils.Base64Utils;
import com.volcengine.ark.runtime.model.completion.chat.*;
import com.volcengine.ark.runtime.service.ArkService;
import io.reactivex.Flowable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.model.Media;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.util.MimeTypeUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class AIChatServiceImpl implements AIChatService {

    @Resource
    private ArkService arkService;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public String chat(String promptText, AIModelType aiModelType) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatBySpringAI(promptText, new ArrayList<>(), aiModelType, null, null);
            case ModelProviderType.VOLCEGINE_SDK -> chatByVolcegineSdk(promptText, new ArrayList<>(), aiModelType, null);
            case ModelProviderType.HTTP -> chatByHttp(promptText, new ArrayList<>() ,aiModelType, null);
        };
    }

    @Override
    public String chat(String promptText, String imageUrl, AIModelType aiModelType) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatBySpringAI(promptText, List.of(imageUrl), aiModelType, null, null);
            case ModelProviderType.VOLCEGINE_SDK -> chatByVolcegineSdk(promptText, List.of(imageUrl), aiModelType, null);
            case ModelProviderType.HTTP -> chatByHttp(promptText, List.of(imageUrl), aiModelType, null);
        };
    }

    @Override
    public String chat(String promptText, List<String> imageUrls, AIModelType aiModelType) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatBySpringAI(promptText, imageUrls, aiModelType, null,null);
            case ModelProviderType.VOLCEGINE_SDK -> chatByVolcegineSdk(promptText, imageUrls, aiModelType, null);
            case ModelProviderType.HTTP -> chatByHttp(promptText, imageUrls, aiModelType, null);
        };
    }

    @Override
    public String chat(String promptText, AIModelType aiModelType, ChatOptions chatOptions) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatBySpringAI(promptText, new ArrayList<>(), aiModelType, chatOptions, null);
            case ModelProviderType.VOLCEGINE_SDK -> chatByVolcegineSdk(promptText, new ArrayList<>(), aiModelType, chatOptions);
            case ModelProviderType.HTTP -> chatByHttp(promptText, new ArrayList<>(), aiModelType, chatOptions);
        };
    }

    @Override
    public String chat(String promptText, String imageUrl, AIModelType aiModelType, ChatOptions chatOptions) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatBySpringAI(promptText, List.of(imageUrl), aiModelType, chatOptions, null);
            case ModelProviderType.VOLCEGINE_SDK -> chatByVolcegineSdk(promptText, List.of(imageUrl), aiModelType, chatOptions);
            case ModelProviderType.HTTP -> chatByHttp(promptText, List.of(imageUrl),aiModelType, chatOptions);
        };
    }

    @Override
    public String chat(String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatBySpringAI(promptText, imageUrls, aiModelType, chatOptions, null);
            case ModelProviderType.VOLCEGINE_SDK -> chatByVolcegineSdk(promptText, imageUrls, aiModelType, chatOptions);
            case ModelProviderType.HTTP -> chatByHttp(promptText, imageUrls, aiModelType, chatOptions);

        };
    }

    @Override
    public String chat(String systemPromptText, String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions, JsonNode jsonSchema) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> null;
            case ModelProviderType.VOLCEGINE_SDK -> chatByVolcegineSdk(systemPromptText, promptText, imageUrls, aiModelType, chatOptions, jsonSchema);
            case ModelProviderType.HTTP -> chatByHttp(systemPromptText, promptText, imageUrls, aiModelType, chatOptions);
        };
    }

    @Override
    public String chat(String promptText, AIModelType aiModelType, ChatOptions chatOptions, Conversation conversation) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatBySpringAI(promptText, new ArrayList<>(), aiModelType, chatOptions, conversation);
            case ModelProviderType.VOLCEGINE_SDK -> chatByVolcegineSdk(promptText, new ArrayList<>(), aiModelType, chatOptions);
            case ModelProviderType.HTTP -> null;
        };
    }

    @Override
    public String chatWithJsonSchema(String promptText,  AIModelType aiModelType, ChatOptions chatOptions, String jsonSchema) {
        return chatWithJsonSchema(null,promptText, aiModelType, chatOptions, jsonSchema);
    }

    @Override
    public String chatWithJsonSchema(String systemPromptText, String promptText, AIModelType aiModelType, ChatOptions chatOptions, String jsonSchema) {
        String chatResult = null;
        try {
            WebClient webClient = (WebClient) applicationContext.getBean(aiModelType.getWebClientName());
            CustomChatCompletionRequest request = new CustomChatCompletionRequest();
            List<ChatMessage> messages = new ArrayList<>();
            messages.add(ChatMessage.builder().role(ChatMessageRole.USER).content(promptText).build());
            if (StrUtil.isNotBlank(systemPromptText)) {
                messages.add(ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemPromptText).build());
            }
            request.setMessages(messages);
            request.setTemperature(chatOptions.getTemperature());
            request.setModel(aiModelType.getModelName());
            ObjectMapper mapper = new ObjectMapper();
            mapper.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
            request.setGuidedJson(mapper.readTree(jsonSchema));
            String jsonStr = mapper.writeValueAsString(request);

            ChatCompletionResult result = webClient.post()
                    .uri("/v1/chat/completions")
                    .bodyValue(jsonStr)
                    .retrieve()
                    .bodyToMono(ChatCompletionResult.class)
                    .block();

            if (aiModelType.getModelName().equals("jysd-deepseekr1")) {
                chatResult =  result.getChoices().getFirst().getMessage().getReasoningContent();
            } else {
                chatResult =  result.getChoices().getFirst().getMessage().getContent().toString();
            }
        } catch (Exception e) {
            log.error("chat error", e);
        }
        return chatResult;
    }

    @Override
    public Flux<String> chatStream(String promptText, List<String> imageUrls, AIModelType aiModelType) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatStreamBySpringAI(promptText, imageUrls, aiModelType, null);
            case ModelProviderType.VOLCEGINE_SDK -> chatStreamByVolcegineSdk(promptText, imageUrls, aiModelType, null);
            case ModelProviderType.HTTP -> chatByHttpStream(promptText, imageUrls, aiModelType, null);
        };
    }

    @Override
    public Flux<String> chatStream(String promptText, AIModelType aiModelType) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatStreamBySpringAI(promptText, List.of(), aiModelType, null);
            case ModelProviderType.VOLCEGINE_SDK -> chatStreamByVolcegineSdk(promptText, List.of(), aiModelType, null);
            case ModelProviderType.HTTP -> chatByHttpStream(promptText, List.of(), aiModelType, null);
        };
    }

    @Override
    public Flux<String> chatStream(String promptText, String imageUrl, AIModelType aiModelType) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatStreamBySpringAI(promptText, List.of(imageUrl), aiModelType, null);
            case ModelProviderType.VOLCEGINE_SDK -> chatStreamByVolcegineSdk(promptText, List.of(imageUrl), aiModelType, null);
            case ModelProviderType.HTTP -> chatByHttpStream(promptText, List.of(imageUrl), aiModelType, null);
        };
    }

    @Override
    public Flux<String> chatStream(String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatStreamBySpringAI(promptText, imageUrls, aiModelType, chatOptions);
            case ModelProviderType.VOLCEGINE_SDK -> chatStreamByVolcegineSdk(promptText, imageUrls, aiModelType, chatOptions);
            case ModelProviderType.HTTP -> chatByHttpStream(promptText, imageUrls, aiModelType, chatOptions);
        };
    }

    @Override
    public Flux<String> chatStream(String promptText, AIModelType aiModelType, ChatOptions chatOptions) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatStreamBySpringAI(promptText, List.of(), aiModelType, chatOptions);
            case ModelProviderType.VOLCEGINE_SDK -> chatStreamByVolcegineSdk(promptText, List.of(), aiModelType, chatOptions);
            case ModelProviderType.HTTP -> chatByHttpStream(promptText, List.of(), aiModelType, chatOptions);
        };
    }

    @Override
    public Flux<String> chatStream(String promptText, String imageUrl, AIModelType aiModelType, ChatOptions chatOptions) {
        return switch (aiModelType.getProviderType()) {
            case ModelProviderType.SPRING_AI -> chatStreamBySpringAI(promptText, List.of(imageUrl), aiModelType, chatOptions);
            case ModelProviderType.VOLCEGINE_SDK -> chatStreamByVolcegineSdk(promptText, List.of(imageUrl), aiModelType, chatOptions);
            case ModelProviderType.HTTP -> chatByHttpStream(promptText, List.of(imageUrl), aiModelType, chatOptions);
        };
    }

    private String chatBySpringAI(String promptText, List<String> imageUrls, AIModelType aiModelType,
                                  ChatOptions chatOptions,Conversation conversation) {
        ChatClient chatClient = (ChatClient) applicationContext.getBean(aiModelType.getClientModel());
        Prompt prompt = getChatPrompt(promptText, imageUrls, chatOptions,conversation);
        String content = chatClient.prompt(prompt).call().content();
        if (conversation != null) {
            conversation.addUserMessage(promptText);
            conversation.addSystemMessage(content);
        }
        return content;
    }

    private String chatByVolcegineSdk(String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions) {
        List<ChatMessage> messages = new ArrayList<>();
        List<ChatCompletionContentPart> multiContent = new ArrayList<>();
        ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(promptText).build();
        multiContent.add(textPart);
        imageUrls.forEach(imageUrl -> {
            ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(imageUrl)).build();
            multiContent.add(imageUrlPart);
        });
        ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
        messages.add(userMessage);
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(aiModelType.getClientModel())
                .messages(messages)
                .build();

        if (chatOptions != null) {
            if (chatOptions.getTemperature() != null) {
                chatCompletionRequest.setTemperature(chatOptions.getTemperature());
            }
            if (chatOptions.getTopP() != null) {
                chatCompletionRequest.setTopP(chatOptions.getTopP());
            }
        }

        String response = arkService.createChatCompletion(chatCompletionRequest).getChoices().getFirst().getMessage().getContent().toString();
        return response;
    }

    private String chatByVolcegineSdk(String systemPromptText, String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions, JsonNode jsonSchema) {
        List<ChatMessage> messages = new ArrayList<>();

        if (CollUtil.isEmpty(imageUrls)) {
            ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(promptText).build();
            messages.add(userMessage);
        } else {
            List<ChatCompletionContentPart> multiContent = new ArrayList<>();
            ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(promptText).build();
            multiContent.add(textPart);
            imageUrls.forEach(imageUrl -> {
                ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(imageUrl)).build();
                multiContent.add(imageUrlPart);
            });
            ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
            messages.add(userMessage);
        }

        if (StrUtil.isNotBlank(systemPromptText)) {
            ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemPromptText).build();
            messages.add(systemMessage);
        }

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(aiModelType.getClientModel())
                .messages(messages)
                .build();

        if (chatOptions != null) {
            if (chatOptions.getTemperature() != null) {
                chatCompletionRequest.setTemperature(chatOptions.getTemperature());
            }
            if (chatOptions.getTopP() != null) {
                chatCompletionRequest.setTopP(chatOptions.getTopP());
            }
        }

        if (jsonSchema != null) {
            ResponseFormatJSONSchemaJSONSchemaParam jsonSchemaParam = new ResponseFormatJSONSchemaJSONSchemaParam("my_schema", "my_schema", jsonSchema, true);
            ChatCompletionRequest.ChatCompletionRequestResponseFormat responseFormat = new ChatCompletionRequest.ChatCompletionRequestResponseFormat("json_schema", jsonSchemaParam);
            chatCompletionRequest.setResponseFormat(responseFormat);
        }

        String response = arkService.createChatCompletion(chatCompletionRequest).getChoices().getFirst().getMessage().getContent().toString();
        return response;
    }

    private String chatByVolcegineSdk(List<ContentPart> contentParts, AIModelType aiModelType, ChatOptions chatOptions, JsonNode jsonSchema, ModelThinkingType modelThinkingType) {
        List<ChatMessage> messages = new ArrayList<>();
        List<ChatCompletionContentPart> multiContent = new ArrayList<>();
        contentParts.forEach(contentPart -> {
            if (ContentPartType.TEXT == contentPart.getContentPartType()) {
                ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(contentPart.getText()).build();
                multiContent.add(textPart);
            }
            if (ContentPartType.IMAGE_URL == contentPart.getContentPartType()) {
                ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(contentPart.getUrl())).build();
                multiContent.add(imageUrlPart);
            }
        });
        ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
        messages.add(userMessage);
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(aiModelType.getClientModel())
                .messages(messages)
                .build();

        if (chatOptions != null) {
            if (chatOptions.getTemperature() != null) {
                chatCompletionRequest.setTemperature(chatOptions.getTemperature());
            }
            if (chatOptions.getTopP() != null) {
                chatCompletionRequest.setTopP(chatOptions.getTopP());
            }
        }

        if (jsonSchema != null) {
            ResponseFormatJSONSchemaJSONSchemaParam jsonSchemaParam = new ResponseFormatJSONSchemaJSONSchemaParam("my_schema", "my_schema", jsonSchema, true);
            ChatCompletionRequest.ChatCompletionRequestResponseFormat responseFormat = new ChatCompletionRequest.ChatCompletionRequestResponseFormat("json_schema", jsonSchemaParam);
            chatCompletionRequest.setResponseFormat(responseFormat);
        }

        if (modelThinkingType != null) {
            chatCompletionRequest.setThinking(new ChatCompletionRequest.ChatCompletionRequestThinking(modelThinkingType.getValue()));
        }

        String response = arkService.createChatCompletion(chatCompletionRequest).getChoices().getFirst().getMessage().getContent().toString();
        return response;
    }


    private Flux<String> chatStreamBySpringAI(String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions) {
        ChatClient chatClient = (ChatClient) applicationContext.getBean(aiModelType.getClientModel());
        Prompt prompt = getChatPrompt(promptText, imageUrls, chatOptions, null);
        return chatClient.prompt(prompt).stream().chatResponse().flatMap(chatResponse -> {
            StreamResponse response = StreamResponse.builder()
                    .content(chatResponse.getResult().getOutput().getText())
                    .finish(StrUtil.isNotBlank(chatResponse.getResult().getMetadata().getFinishReason()))
                    .build();
            return Flux.just(JSONUtil.toJsonStr(response));
        });
    }

    private Flux<String> chatStreamByVolcegineSdk(String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions) {
        List<ChatMessage> messages = new ArrayList<>();
        List<ChatCompletionContentPart> multiContent = new ArrayList<>();
        ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(promptText).build();
        multiContent.add(textPart);
        imageUrls.forEach(imageUrl -> {
            ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(imageUrl)).build();
            multiContent.add(imageUrlPart);
        });
        ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
        messages.add(userMessage);
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(aiModelType.getClientModel())
                .messages(messages)
                .build();

        if (chatOptions != null) {
            if (chatOptions.getTemperature() != null) {
                chatCompletionRequest.setTemperature(chatOptions.getTemperature());
            }
            if (chatOptions.getTopP() != null) {
                chatCompletionRequest.setTopP(chatOptions.getTopP());
            }
        }

        return Flux.create(sink -> {
            Disposable disposable = arkService.streamChatCompletion(chatCompletionRequest)
                    .doOnError(Throwable::printStackTrace)
                    .subscribeOn(Schedulers.io())
                    .observeOn(Schedulers.single())
                    .subscribe(
                            choice -> {
                                if (!choice.getChoices().isEmpty()) {
                                    StreamResponse response = StreamResponse.builder()
                                            .reasoningContent(choice.getChoices().getFirst().getMessage().getReasoningContent())
                                            .content(choice.getChoices().getFirst().getMessage().getContent().toString())
                                            .finish(StrUtil.isNotBlank(choice.getChoices().getFirst().getFinishReason()))
                                            .build();
                                    sink.next(JSONUtil.toJsonStr(response));
                                }
                            },
                            sink::error,
                            sink::complete
                    );
            sink.onCancel(disposable::dispose);
        });
    }

    private Flux<String> chatStreamByVolcegineSdk(List<ContentPart> contentParts, AIModelType aiModelType, ChatOptions chatOptions, JsonNode jsonSchema, ModelThinkingType modelThinkingType) {
        List<ChatMessage> messages = new ArrayList<>();
        List<ChatCompletionContentPart> multiContent = new ArrayList<>();
        contentParts.forEach(contentPart -> {
            if (ContentPartType.TEXT == contentPart.getContentPartType()) {
                ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(contentPart.getText()).build();
                multiContent.add(textPart);
            }
            if (ContentPartType.IMAGE_URL == contentPart.getContentPartType()) {
                ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(contentPart.getUrl())).build();
                multiContent.add(imageUrlPart);
            }
        });
        ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
        messages.add(userMessage);
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(aiModelType.getClientModel())
                .messages(messages)
                .build();

        if (chatOptions != null) {
            if (chatOptions.getTemperature() != null) {
                chatCompletionRequest.setTemperature(chatOptions.getTemperature());
            }
            if (chatOptions.getTopP() != null) {
                chatCompletionRequest.setTopP(chatOptions.getTopP());
            }
        }

        if (jsonSchema != null) {
            ResponseFormatJSONSchemaJSONSchemaParam jsonSchemaParam = new ResponseFormatJSONSchemaJSONSchemaParam("my_schema", "my_schema", jsonSchema, true);
            ChatCompletionRequest.ChatCompletionRequestResponseFormat responseFormat = new ChatCompletionRequest.ChatCompletionRequestResponseFormat("json_schema", jsonSchemaParam);
            chatCompletionRequest.setResponseFormat(responseFormat);
        }

        if (modelThinkingType != null) {
            chatCompletionRequest.setThinking(new ChatCompletionRequest.ChatCompletionRequestThinking(modelThinkingType.getValue()));
        }

        return Flux.create(sink -> {
            Disposable disposable = arkService.streamChatCompletion(chatCompletionRequest)
                    .doOnError(Throwable::printStackTrace)
                    .subscribeOn(Schedulers.io())
                    .observeOn(Schedulers.single())
                    .subscribe(
                            choice -> {
                                if (!choice.getChoices().isEmpty()) {
                                    StreamResponse response = StreamResponse.builder()
                                            .reasoningContent(choice.getChoices().getFirst().getMessage().getReasoningContent())
                                            .content(choice.getChoices().getFirst().getMessage().getContent().toString())
                                            .finish(StrUtil.isNotBlank(choice.getChoices().getFirst().getFinishReason()))
                                            .build();
                                    sink.next(JSONUtil.toJsonStr(response));
                                }
                            },
                            sink::error,
                            sink::complete
                    );
            sink.onCancel(disposable::dispose);
        });
    }

    @Override
    public Flowable<ChatCompletionChunk> chatStreamChunkByVolcegineSdk(String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions) {
        List<ChatMessage> messages = new ArrayList<>();
        List<ChatCompletionContentPart> multiContent = new ArrayList<>();
        ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(promptText).build();
        multiContent.add(textPart);
        imageUrls.forEach(imageUrl -> {
            ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(imageUrl)).build();
            multiContent.add(imageUrlPart);
        });
        ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
        messages.add(userMessage);
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(aiModelType.getClientModel())
                .messages(messages)
                .build();

        if (chatOptions != null) {
            if (chatOptions.getTemperature() != null) {
                chatCompletionRequest.setTemperature(chatOptions.getTemperature());
            }
            if (chatOptions.getTopP() != null) {
                chatCompletionRequest.setTopP(chatOptions.getTopP());
            }
        }
        return arkService.streamChatCompletion(chatCompletionRequest);

        /*return Flux.create(sink -> {
            Disposable disposable = arkService.streamChatCompletion(chatCompletionRequest)
                    .doOnError(Throwable::printStackTrace)
                    .subscribeOn(Schedulers.io())
                    .observeOn(Schedulers.single())
                    .subscribe(
                            choice -> {
                                if (!choice.getChoices().isEmpty()) {
                                    // 将模型返回的内容流式推送给下游
                                    sink.next(JSONUtil.toJsonStr(choice));
                                }
                            },
                            sink::error,
                            sink::complete  // 当流结束时通知完成
                    );
            sink.onCancel(disposable::dispose);
        });*/
    }

    private Prompt getChatPrompt(String promptText, List<String> imageUrls, ChatOptions chatOptions, Conversation conversation) {
        List<Message> messageList = new ArrayList<>();
        if (conversation != null) {
            List<Message> history = conversation.getHistory();
            if (!history.isEmpty()) {
                messageList.addAll(history);
            }
        }

        UserMessage userMessage;
        if (!imageUrls.isEmpty()) {
            List<Media> mediaList = new ArrayList<>();
            imageUrls.forEach(imageUrl -> {
                try {
                    mediaList.add(new Media(MimeTypeUtils.IMAGE_PNG, new UrlResource(imageUrl)));
                } catch (MalformedURLException e) {
                    throw new RuntimeException(e);
                }
            });
            userMessage = new UserMessage(promptText, mediaList);
        } else {
            userMessage= new UserMessage(promptText);
        }
        messageList.add(userMessage);
        return new Prompt(messageList, chatOptions);
    }

    @Override
    public String chat(String promptText, AIModelType aiModelType, ChatOptions chatOptions, String extraBody) {
        WebClient webClient = (WebClient) applicationContext.getBean(aiModelType.getWebClientName());
        ChatCompletionWithExtraBodyRequest request = new ChatCompletionWithExtraBodyRequest();
        List<ChatMessage> messages = List.of(ChatMessage.builder().role(ChatMessageRole.USER).content(promptText).build());
        request.setMessages(messages);
        request.setTemperature(chatOptions.getTemperature());
        request.setModel(aiModelType.getModelName());
        request.setGuidedJson(JSONObject.parseObject(extraBody));

        String jsonStr = JSONObject.toJSONString(request);

        ChatCompletionResult result = webClient.post()
                .uri("/chat/completions")
                .bodyValue(jsonStr)
                .retrieve()
                .bodyToMono(ChatCompletionResult.class)
                .block();

        return result.getChoices().getFirst().getMessage().getContent().toString();
    }

    @Override
    public String chatWithContentPart(List<ContentPart> contentParts, AIModelType aiModelType, ChatOptions chatOptions, JsonNode jsonSchema, ModelThinkingType modelThinkingType) {
        return switch (aiModelType.getProviderType()) {
            case SPRING_AI -> null;
            case ModelProviderType.VOLCEGINE_SDK -> chatByVolcegineSdk(contentParts, aiModelType, chatOptions, jsonSchema, modelThinkingType);
            case ModelProviderType.HTTP -> chatByHttp(contentParts, aiModelType, chatOptions);
        };
    }

    @Override
    public Flux<String> chatStreamWithContentPart(List<ContentPart> contentParts, AIModelType aiModelType, ChatOptions chatOptions, JsonNode jsonSchema, ModelThinkingType modelThinkingType) {
        return switch (aiModelType.getProviderType()) {
            case SPRING_AI -> null;
            case ModelProviderType.VOLCEGINE_SDK -> chatStreamByVolcegineSdk(contentParts, aiModelType, chatOptions, jsonSchema, modelThinkingType);
            case ModelProviderType.HTTP -> chatByHttpStream(contentParts, aiModelType, chatOptions);
        };
    }

    public String chatByHttp(String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions) {
        WebClient webClient = (WebClient) applicationContext.getBean(aiModelType.getWebClientName());

        List<ChatMessage> messages = new ArrayList<>();
        if (CollUtil.isEmpty(imageUrls)) {
            ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(promptText).build();
            messages.add(userMessage);
        } else {
            List<ChatCompletionContentPart> multiContent = new ArrayList<>();
            ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(promptText).build();
            multiContent.add(textPart);
            imageUrls.forEach(imageUrl -> {
                ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(imageUrl)).build();
                multiContent.add(imageUrlPart);
            });
            ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
            messages.add(userMessage);
        }

        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setMessages(messages);
        if (chatOptions != null) {
            if (chatOptions.getTemperature() != null) {
                request.setTemperature(chatOptions.getTemperature());
            }
        }
        request.setModel(aiModelType.getModelName());

        String jsonStr = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            // 配置全局忽略 null 值字段
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            jsonStr = mapper.writeValueAsString(request);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        if (jsonStr.contains("\"imageUrl\"")) {
            jsonStr = jsonStr.replaceAll("\"imageUrl\"", "\"image_url\"");
        }
        log.info("请求参数 {}", jsonStr);
        ChatCompletionResult result = webClient.post()
                .uri("/chat/completions")
                .bodyValue(jsonStr)
                .retrieve()
                .onStatus(HttpStatusCode::is4xxClientError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody ->
                                        Mono.error(new RuntimeException("Server error: " + errorBody))
                                )
                )
                .bodyToMono(ChatCompletionResult.class)
                .block();

        Object content = result.getChoices().getFirst().getMessage().getContent();
        return content == null ? null : content.toString();
    }

    public String chatByHttp(String systemPromptText, String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions) {
        WebClient webClient = (WebClient) applicationContext.getBean(aiModelType.getWebClientName());

        List<ChatMessage> messages = new ArrayList<>();
        if (CollUtil.isEmpty(imageUrls)) {
            ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(promptText).build();
            messages.add(userMessage);
        } else {
            List<ChatCompletionContentPart> multiContent = new ArrayList<>();
            ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(promptText).build();
            multiContent.add(textPart);
            imageUrls.forEach(imageUrl -> {
                ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(imageUrl)).build();
                multiContent.add(imageUrlPart);
            });
            ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
            messages.add(userMessage);
        }
        if (StrUtil.isNotBlank(systemPromptText)) {
            ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemPromptText).build();
            messages.add(systemMessage);
        }

        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setMessages(messages);
        if (chatOptions != null) {
            if (chatOptions.getTemperature() != null) {
                request.setTemperature(chatOptions.getTemperature());
            }
        }
        request.setModel(aiModelType.getModelName());

        String jsonStr = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            // 配置全局忽略 null 值字段
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            jsonStr = mapper.writeValueAsString(request);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        if (jsonStr.contains("\"imageUrl\"")) {
            jsonStr = jsonStr.replaceAll("\"imageUrl\"", "\"image_url\"");
        }
        log.info("请求参数 {}", jsonStr);
        ChatCompletionResult result = webClient.post()
                .uri("/chat/completions")
                .bodyValue(jsonStr)
                .retrieve()
                .onStatus(HttpStatusCode::is4xxClientError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody ->
                                        Mono.error(new RuntimeException("Server error: " + errorBody))
                                )
                )
                .bodyToMono(ChatCompletionResult.class)
                .block();

        Object content = result.getChoices().getFirst().getMessage().getContent();
        return content == null ? null : content.toString();
    }

    public Flux<String> chatByHttpStream(String promptText, List<String> imageUrls, AIModelType aiModelType, ChatOptions chatOptions) {
        WebClient webClient = (WebClient) applicationContext.getBean(aiModelType.getWebClientName());

        ChatCompletionRequest request = new ChatCompletionRequest();
        List<ChatCompletionContentPart> multiContent = new ArrayList<>();
        ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(promptText).build();
        multiContent.add(textPart);
        imageUrls.forEach(imageUrl -> {
            ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL("data:image/png;base64," + Base64Utils.convertImageUrlToBase64(imageUrl))).build();
            multiContent.add(imageUrlPart);
        });
        ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(userMessage);
        request.setMessages(messages);
        if (chatOptions != null) {
            if (chatOptions.getTemperature() != null) {
                request.setTemperature(chatOptions.getTemperature());
            }
        }
        request.setModel(aiModelType.getModelName());
        request.setStream(true);

        String jsonStr = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            // 配置全局忽略 null 值字段
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            jsonStr = mapper.writeValueAsString(request);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        if (jsonStr.contains("\"imageUrl\"")) {
            jsonStr = jsonStr.replaceAll("\"imageUrl\"", "\"image_url\"");
        }

        return webClient.post()
                .uri("/chat/completions")
                .bodyValue(jsonStr)
                .retrieve()
                .onStatus(HttpStatusCode::is4xxClientError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody ->
                                        Mono.error(new RuntimeException("Server error: " + errorBody))
                                )
                )
                .bodyToFlux(String.class)
                .flatMap(resultJson -> {
                    StreamResponse response;
                    log.info(resultJson);
                    if (resultJson.equals("[DONE]")) {
                        return Flux.empty();
                    }
                    if (resultJson.contains("\"delta\"")) {
                        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(resultJson);
                        cn.hutool.json.JSONObject choice = jsonObject.getJSONArray("choices").get(0, cn.hutool.json.JSONObject.class);
                        String content = choice.getJSONObject("delta").getStr("content");
                        String reasoningContent = choice.getJSONObject("delta").getStr("reasoning_content");
                        response = StreamResponse.builder()
                                .reasoningContent(reasoningContent)
                                .content(content)
                                .finish(ObjectUtil.equals(choice.getStr("finish_reason"), "stop"))
                                .build();
                    }else {
                        ChatCompletionChunk chatCompletionChunk = JSONUtil.toBean(resultJson, ChatCompletionChunk.class);
                        Object content = chatCompletionChunk.getChoices().getFirst().getMessage().getContent();
                        String reasoningContent = chatCompletionChunk.getChoices().getFirst().getMessage().getReasoningContent();
                        response = StreamResponse.builder()
                                .reasoningContent(reasoningContent)
                                .content(content == null ? null : content.toString())
                                .finish(StrUtil.isNotBlank(chatCompletionChunk.getChoices().getFirst().getFinishReason()))
                                .build();
                    }
                    return Flux.just(JSONUtil.toJsonStr(response));
                });
    }

    public String chatByHttp(List<ContentPart> contentParts, AIModelType aiModelType, ChatOptions chatOptions) {
        WebClient webClient = (WebClient) applicationContext.getBean(aiModelType.getWebClientName());

        ChatCompletionRequest request = new ChatCompletionRequest();
        List<ChatCompletionContentPart> multiContent = new ArrayList<>();
        contentParts.forEach(contentPart -> {
            if (ContentPartType.TEXT == contentPart.getContentPartType()) {
                ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(contentPart.getText()).build();
                multiContent.add(textPart);
            }
            if (ContentPartType.IMAGE_URL == contentPart.getContentPartType()) {
                ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL("data:image/png;base64," + Base64Utils.convertImageUrlToBase64(contentPart.getUrl()))).build();
                multiContent.add(imageUrlPart);
            }
        });
        ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(userMessage);
        request.setMessages(messages);
        if (chatOptions != null) {
            if (chatOptions.getTemperature() != null) {
                request.setTemperature(chatOptions.getTemperature());
            }
        }
        request.setModel(aiModelType.getModelName());

        String jsonStr = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            // 配置全局忽略 null 值字段
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            jsonStr = mapper.writeValueAsString(request);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        if (jsonStr.contains("\"imageUrl\"")) {
            jsonStr = jsonStr.replaceAll("\"imageUrl\"", "\"image_url\"");
        }
        log.info("请求参数 {}", jsonStr);
        ChatCompletionResult result = webClient.post()
                .uri("/chat/completions")
                .bodyValue(jsonStr)
                .retrieve()
                .onStatus(HttpStatusCode::is4xxClientError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody ->
                                        Mono.error(new RuntimeException("Server error: " + errorBody))
                                )
                )
                .bodyToMono(ChatCompletionResult.class)
                .block();

        Object content = result.getChoices().getFirst().getMessage().getContent();
        return content == null ? null : content.toString();
    }

    public Flux<String> chatByHttpStream(List<ContentPart> contentParts, AIModelType aiModelType, ChatOptions chatOptions) {
        WebClient webClient = (WebClient) applicationContext.getBean(aiModelType.getWebClientName());

        ChatCompletionRequest request = new ChatCompletionRequest();
        List<ChatCompletionContentPart> multiContent = new ArrayList<>();
        contentParts.forEach(contentPart -> {
            if (ContentPartType.TEXT == contentPart.getContentPartType()) {
                ChatCompletionContentPart textPart = ChatCompletionContentPart.builder().type("text").text(contentPart.getText()).build();
                multiContent.add(textPart);
            }
            if (ContentPartType.IMAGE_URL == contentPart.getContentPartType()) {
                ChatCompletionContentPart imageUrlPart = ChatCompletionContentPart.builder().type("image_url").imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL("data:image/png;base64," + Base64Utils.convertImageUrlToBase64(contentPart.getUrl()))).build();
                multiContent.add(imageUrlPart);
            }
        });
        ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiContent).build();
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(userMessage);
        request.setMessages(messages);
        if (chatOptions != null) {
            if (chatOptions.getTemperature() != null) {
                request.setTemperature(chatOptions.getTemperature());
            }
        }
        request.setModel(aiModelType.getModelName());
        request.setStream(true);

        String jsonStr = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            // 配置全局忽略 null 值字段
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            jsonStr = mapper.writeValueAsString(request);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        if (jsonStr.contains("\"imageUrl\"")) {
            jsonStr = jsonStr.replaceAll("\"imageUrl\"", "\"image_url\"");
        }

        return webClient.post()
                .uri("/chat/completions")
                .bodyValue(jsonStr)
                .retrieve()
                .onStatus(HttpStatusCode::is4xxClientError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody ->
                                        Mono.error(new RuntimeException("Server error: " + errorBody))
                                )
                )
                .bodyToFlux(String.class)
                .flatMap(resultJson -> {
                    StreamResponse response;
                    log.info(resultJson);
                    if (resultJson.equals("[DONE]")) {
                        return Flux.empty();
                    }
                    if (resultJson.contains("\"delta\"")) {
                        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(resultJson);
                        cn.hutool.json.JSONObject choice = jsonObject.getJSONArray("choices").get(0, cn.hutool.json.JSONObject.class);
                        String content = choice.getJSONObject("delta").getStr("content");
                        String reasoningContent = choice.getJSONObject("delta").getStr("reasoning_content");
                        response = StreamResponse.builder()
                                .reasoningContent(reasoningContent)
                                .content(content)
                                .finish(ObjectUtil.equals(choice.getStr("finish_reason"), "stop"))
                                .build();
                    }else {
                        ChatCompletionChunk chatCompletionChunk = JSONUtil.toBean(resultJson, ChatCompletionChunk.class);
                        Object content = chatCompletionChunk.getChoices().getFirst().getMessage().getContent();
                        String reasoningContent = chatCompletionChunk.getChoices().getFirst().getMessage().getReasoningContent();
                        response = StreamResponse.builder()
                                .reasoningContent(reasoningContent)
                                .content(content == null ? null : content.toString())
                                .finish(StrUtil.isNotBlank(chatCompletionChunk.getChoices().getFirst().getFinishReason()))
                                .build();
                    }
                    return Flux.just(JSONUtil.toJsonStr(response));
                });
    }

}
