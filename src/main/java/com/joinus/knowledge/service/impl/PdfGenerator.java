package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.dto.pdf.*;
import com.joinus.knowledge.model.entity.MathChapter;
import com.joinus.knowledge.model.entity.MathSection;
import com.joinus.knowledge.model.entity.Textbooks;
import com.joinus.knowledge.model.vo.MathAnswerVO;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.model.vo.MathQuestionTypeVO;
import com.joinus.knowledge.model.vo.MathQuestionVO;
import com.joinus.knowledge.service.*;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
public class PdfGenerator {

    @Resource
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Resource
    private MathQuestionTypesService mathQuestionTypesService;
    @Resource
    private TextbooksService textbooksService;
    @Resource
    private MathChapterService mathChapterService;
    @Resource
    private MathSectionService mathSectionService;
    @Resource
    private MathQuestionsService mathQuestionsService;
    @Resource
    private WebClient pdfGenerateServiceWebClient;

    // Helper record to hold grouped question data
    private record QuestionGroup(
            Map<UUID, List<MathQuestionVO>> byKnowledgePoint,
            Map<UUID, List<MathQuestionVO>> byQuestionType,
            Set<UUID> knowledgePointIds,
            Set<UUID> questionTypeIds
    ) {}

    // Helper record to hold all fetched data for easy access (the "Context")
    private record DataContext(
            Map<UUID, MathKnowledgePointVO> knowledgePoints,
            Map<UUID, MathQuestionTypeVO> questionTypes,
            List<Textbooks> sortedTextbooks,
            Map<UUID, MathChapter> chapters,
            Map<UUID, MathSection> sections
    ) {}

    /**
     * Main public method. Acts as a coordinator for the PDF generation process.
     */
    public String generatePdf(List<MathQuestionVO> questionVOList) {
        // 1. Group initial questions and collect their IDs
        QuestionGroup questionGroup = groupQuestions(questionVOList);

        // 2. Fetch all required data from services in one go
        DataContext dataContext = fetchAllRelatedData(questionGroup);

        // 3. Build the hierarchical PDF data structure
        Counter questionCounter = new Counter();
        List<PdfBook> pdfBooks = buildPdfBooks(dataContext, questionGroup, questionCounter);

        // 4. Call the external PDF generation service
        return callPdfGenerationService(pdfBooks, questionGroup, questionCounter);
    }

    public PdfParam generatePdfParam(List<MathQuestionVO> questionVOList) {
        // 1. Group initial questions and collect their IDs
        QuestionGroup questionGroup = groupQuestions(questionVOList);

        // 2. Fetch all required data from services in one go
        DataContext dataContext = fetchAllRelatedData(questionGroup);

        // 3. Build the hierarchical PDF data structure
        Counter questionCounter = new Counter();
        List<PdfBook> pdfBooks = buildPdfBooks(dataContext, questionGroup, questionCounter);

        PdfParam pdfParam = new PdfParam();
        pdfParam.setBookList(pdfBooks);
        pdfParam.setTotalQuestions(questionCounter.get());
        pdfParam.setTotalQuestionTypes(questionGroup.questionTypeIds().size());
        pdfParam.setTotalKnowledgePoints(questionGroup.knowledgePointIds().size());
        pdfParam.setContents(List.of("1", "3", "4"));
        return pdfParam;
    }

    /**
     * Step 1: Groups questions by KnowledgePoint and QuestionType.
     */
    private QuestionGroup groupQuestions(List<MathQuestionVO> questions) {
        Map<UUID, List<MathQuestionVO>> byKnowledgePoint = new HashMap<>();
        Map<UUID, List<MathQuestionVO>> byQuestionType = new HashMap<>();

        questions.forEach(q -> {
            if (CollUtil.isNotEmpty(q.getKnowledgePoints())) {
                q.getKnowledgePoints().forEach(kp ->
                        byKnowledgePoint.computeIfAbsent(kp.getId(), k -> new ArrayList<>()).add(q));
            }
            if (CollUtil.isNotEmpty(q.getQuestionTypes())) {
                q.getQuestionTypes().forEach(qt ->
                        byQuestionType.computeIfAbsent(qt.getId(), k -> new ArrayList<>()).add(q));
            }
        });

        return new QuestionGroup(byKnowledgePoint, byQuestionType, byKnowledgePoint.keySet(), byQuestionType.keySet());
    }

    /**
     * Step 2: Fetches all data needed for PDF generation from various services.
     */
    private DataContext fetchAllRelatedData(QuestionGroup questionGroup) {
        // Fetch VOs and convert to maps for efficient lookup
        Map<UUID, MathKnowledgePointVO> knowledgePointsMap = fetchAndMapById(
                ids -> mathKnowledgePointsService.listByIds(new ArrayList<>(ids)), // <-- 修改这里
                questionGroup.knowledgePointIds(),
                MathKnowledgePointVO::getId
        );

        Map<UUID, MathQuestionTypeVO> questionTypesMap = fetchAndMapById(
                ids -> mathQuestionTypesService.listByIds(new ArrayList<>(ids)), // <-- 修改这里
                questionGroup.questionTypeIds(),
                MathQuestionTypeVO::getId
        );
        // Collect all textbook, chapter, and section IDs
        Set<UUID> textbookIds = Stream.concat(
                knowledgePointsMap.values().stream().map(MathKnowledgePointVO::getTextbookId),
                questionTypesMap.values().stream().map(MathQuestionTypeVO::getTextbookId)
        ).collect(Collectors.toSet());

        Set<UUID> chapterIds = Stream.concat(
                knowledgePointsMap.values().stream().map(MathKnowledgePointVO::getChapterId),
                questionTypesMap.values().stream().map(MathQuestionTypeVO::getChapterId)
        ).collect(Collectors.toSet());

        Set<UUID> sectionIds = Stream.concat(
                knowledgePointsMap.values().stream().map(MathKnowledgePointVO::getSectionId),
                questionTypesMap.values().stream().map(MathQuestionTypeVO::getSectionId)
        ).collect(Collectors.toSet());

        // Fetch entities
        List<Textbooks> sortedTextbooks = textbooksService.listByIds(textbookIds).stream()
                .sorted(Comparator.comparing(Textbooks::getPublisher)
                        .thenComparing(Textbooks::getGrade)
                        .thenComparing(Textbooks::getSemester))
                .toList();

        Map<UUID, MathChapter> chaptersMap = fetchAndMapById(mathChapterService::listByIds, chapterIds, MathChapter::getId);
        Map<UUID, MathSection> sectionsMap = fetchAndMapById(mathSectionService::listByIds, sectionIds, MathSection::getId);

        return new DataContext(knowledgePointsMap, questionTypesMap, sortedTextbooks, chaptersMap, sectionsMap);
    }
    
    /**
     * Generic helper to fetch items by IDs and convert the list to a map.
     */
    private <T, K> Map<K, T> fetchAndMapById(Function<Collection<K>, List<T>> fetchFunction, Collection<K> ids, Function<T, K> idExtractor) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        return fetchFunction.apply(new ArrayList<>(ids)).stream()
                .collect(Collectors.toMap(idExtractor, Function.identity()));
    }


    /**
     * Step 3: Build the final list of PdfBook objects.
     * This is the main transformation logic, broken into hierarchical steps.
     */
    private List<PdfBook> buildPdfBooks(DataContext ctx, QuestionGroup qg, Counter counter) {
        return ctx.sortedTextbooks().stream()
                .map(textbook -> {
                    PdfBook pdfBook = new PdfBook();
                    BeanUtil.copyProperties(textbook, pdfBook);
                    pdfBook.setBookStr(StrUtil.format("{}年级{}学期 {}", textbook.getGrade(), textbook.getSemester() == 1 ? "上" : "下", textbook.getPublisher()));

                    List<PdfChapter> chapters = buildPdfChapters(textbook.getId(), ctx, qg, counter);
                    pdfBook.setChapterList(chapters);
                    return pdfBook;
                })
                .toList();
    }

    private List<PdfChapter> buildPdfChapters(UUID textbookId, DataContext ctx, QuestionGroup qg, Counter counter) {
        // Find all chapters related to this textbook
        Set<UUID> chapterIdsForBook = Stream.concat(
            ctx.knowledgePoints().values().stream(),
            ctx.questionTypes().values().stream()
        )
        .filter(vo -> vo instanceof MathKnowledgePointVO ? ((MathKnowledgePointVO) vo).getTextbookId().equals(textbookId) : ((MathQuestionTypeVO) vo).getTextbookId().equals(textbookId))
        .map(vo -> vo instanceof MathKnowledgePointVO ? ((MathKnowledgePointVO) vo).getChapterId() : ((MathQuestionTypeVO) vo).getChapterId())
        .collect(Collectors.toSet());

        return chapterIdsForBook.stream()
                .map(chapterId -> ctx.chapters().get(chapterId))
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(MathChapter::getSortNo))
                .map(chapter -> {
                    PdfChapter pdfChapter = new PdfChapter();
                    pdfChapter.setChapterId(chapter.getId().toString());
                    pdfChapter.setChapterName(chapter.getName());
                    pdfChapter.setChapterSortNo(chapter.getSortNo());

                    List<PdfSection> sections = buildPdfSections(chapter.getId(), ctx, qg, counter);
                    pdfChapter.setSectionList(sections);
                    return pdfChapter;
                })
                .toList();
    }

    private List<PdfSection> buildPdfSections(UUID chapterId, DataContext ctx, QuestionGroup qg, Counter counter) {
        // Find all sections related to this chapter
        Set<UUID> sectionIdsForChapter = Stream.concat(
            ctx.knowledgePoints().values().stream(),
            ctx.questionTypes().values().stream()
        )
        .filter(vo -> vo instanceof MathKnowledgePointVO ? ((MathKnowledgePointVO) vo).getChapterId().equals(chapterId) : ((MathQuestionTypeVO) vo).getChapterId().equals(chapterId))
        .map(vo -> vo instanceof MathKnowledgePointVO ? ((MathKnowledgePointVO) vo).getSectionId() : ((MathQuestionTypeVO) vo).getSectionId())
        .collect(Collectors.toSet());
        
        return sectionIdsForChapter.stream()
                .map(sectionId -> ctx.sections().get(sectionId))
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(MathSection::getSortNo))
                .map(section -> {
                    PdfSection pdfSection = new PdfSection();
                    pdfSection.setSectionId(section.getId().toString());
                    pdfSection.setSectionName(section.getSectionName());
                    pdfSection.setSectionSortNo(section.getSortNo());

                    // Build KnowledgePoints for this section
                    List<PdfKnowledgePoint> pdfKnowledgePoints = ctx.knowledgePoints().values().stream()
                            .filter(kp -> kp.getSectionId().equals(section.getId()))
                            .map(kp -> {
                                PdfKnowledgePoint pdfKp = new PdfKnowledgePoint();
                                pdfKp.setKnowledgePointId(kp.getId().toString());
                                pdfKp.setKnowledgePointName(kp.getName());
                                List<MathQuestionVO> questions = qg.byKnowledgePoint().getOrDefault(kp.getId(), Collections.emptyList());
                                pdfKp.setQuestionList(createPdfQuestions(questions, counter, q -> q.setKnowledgePointId(kp.getId().toString())));
                                return pdfKp;
                            })
                            .toList();

                    // Build QuestionTypes for this section
                    List<PdfQuestionType> pdfQuestionTypes = ctx.questionTypes().values().stream()
                            .filter(qt -> qt.getSectionId().equals(section.getId()))
                            .map(qt -> {
                                PdfQuestionType pdfQt = new PdfQuestionType();
                                pdfQt.setQuestionTypeId(qt.getId().toString());
                                pdfQt.setQuestionTypeName(qt.getName());
                                List<MathQuestionVO> questions = qg.byQuestionType().getOrDefault(qt.getId(), Collections.emptyList());
                                pdfQt.setQuestionList(createPdfQuestions(questions, counter, q -> q.setQuestionTypeId(qt.getId().toString())));
                                return pdfQt;
                            })
                            .toList();
                    
                    pdfSection.setKnowledgePoints(pdfKnowledgePoints);
                    pdfSection.setQuestionTypes(pdfQuestionTypes);
                    return pdfSection;
                })
                .toList();
    }

    /**
     * Reusable helper to create a list of PdfQuestion objects.
     * This eliminates the code duplication from the original method.
     */
    private List<PdfQuestion> createPdfQuestions(List<MathQuestionVO> questions, Counter counter, java.util.function.Consumer<PdfQuestion> customizer) {
        if (CollUtil.isEmpty(questions)) {
            return Collections.emptyList();
        }
        // NOTE: The original logic only took the first question. 
        // This implementation does the same. If all questions are needed, use .stream() instead of .stream().findFirst()...
        return questions.stream().findFirst()
                .map(question -> {
                    PdfQuestion pQuestion = new PdfQuestion();
                    pQuestion.setQuestionId(question.getId().toString());
                    pQuestion.setQuestionContent(question.getContent());
                    pQuestion.setSortNo(counter.incrementAndGet());

                    customizer.accept(pQuestion); // Apply specific IDs (KP or QT)

                    List<MathAnswerVO> answers = mathQuestionsService.listAnswersByQuestionId(question.getId());
                    if (CollUtil.isNotEmpty(answers)) {
                        MathAnswerVO firstAnswer = answers.getFirst();
                        pQuestion.setAnswer(firstAnswer.getAnswer());
                        pQuestion.setAnalyzeContent(firstAnswer.getContent());
                    }
                    return List.of(pQuestion);
                }).orElse(Collections.emptyList());
    }

    /**
     * Step 4: Prepares the final payload and calls the external service.
     */
    private String callPdfGenerationService(List<PdfBook> books, QuestionGroup qg, Counter counter) {
        PdfParam pdfParam = new PdfParam();
        pdfParam.setBookList(books);
        pdfParam.setTotalQuestions(counter.get());
        pdfParam.setTotalQuestionTypes(qg.questionTypeIds().size());
        pdfParam.setTotalKnowledgePoints(qg.knowledgePointIds().size());
        pdfParam.setContents(List.of("1", "3", "4")); // Example values

        Result<Map> map = pdfGenerateServiceWebClient.post()
                .uri("/pdf/generate/paper-review")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(JSONUtil.toJsonStr(pdfParam))
                .retrieve()
                .bodyToMono(Result.class)
                .timeout(Duration.ofMillis(180000))
                .block();

        return MapUtil.getStr(map.getData(), "presignedUrl");
    }

    public String callNewPdfGenerationService(List<MathQuestionVO> questionVOList) {
        Result<Map> map = pdfGenerateServiceWebClient.post()
                .uri("/math/pdf-download")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(JSONUtil.toJsonStr(questionVOList))
                .retrieve()
                .bodyToMono(Result.class)
                .timeout(Duration.ofMillis(180000))
                .block();

        if (map.getCode() != 200) {
            throw new RuntimeException("生成PDF失败: " + map.getMessage());
        }
        return MapUtil.getStr(map.getData(), "presignedUrl");
    }
    /**
     * A simple mutable counter to replace AtomicInteger when thread safety is not required.
     */
    private static class Counter {
        private int count = 0;
        public int incrementAndGet() {
            return ++count;
        }
        public int get() {
            return count;
        }
    }
}