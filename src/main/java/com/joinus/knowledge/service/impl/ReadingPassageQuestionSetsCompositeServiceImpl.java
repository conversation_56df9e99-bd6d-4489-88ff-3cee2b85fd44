package com.joinus.knowledge.service.impl;

import com.joinus.knowledge.model.entity.ReadingPassageQuestionSets;
import com.joinus.knowledge.model.param.ReadingQuestionSetEditParam;
import com.joinus.knowledge.service.ReadingPassageQuestionSetsCompositeService;
import com.joinus.knowledge.service.ReadingPassageQuestionSetsService;
import com.joinus.knowledge.service.ReadingPassageQuestionsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description 套题集成服务实现类
 * <AUTHOR>
 * @date 2025/5/14
 */

@Slf4j
@Service
public class ReadingPassageQuestionSetsCompositeServiceImpl implements ReadingPassageQuestionSetsCompositeService {

    @Autowired
    private ReadingPassageQuestionSetsService passageQuestionSetsService;
    @Autowired
    private ReadingPassageQuestionsService passageQuestionsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSets(ReadingQuestionSetEditParam param) {
        ReadingPassageQuestionSets updateSets = new ReadingPassageQuestionSets();
        updateSets.setId(param.getQuestionSetId());
        updateSets.setIsAudit(param.getIsAudit());
        updateSets.setIsEnabled(param.getIsEnabled());
        // 更新题目集信息
        passageQuestionsService.batchUpdate(param.getQuestionList());
        // 更新套题信息
        passageQuestionSetsService.updateById(updateSets);
    }
}