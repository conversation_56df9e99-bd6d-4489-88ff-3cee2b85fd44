package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.Header;
import com.joinus.knowledge.mapper.MathQuestionsMapper;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.QuestionAnswerRelation;
import com.joinus.knowledge.model.param.DifyGenerateQuestionParam;
import com.joinus.knowledge.model.param.DifyImageParam;
import com.joinus.knowledge.model.param.DifyParam;
import com.joinus.knowledge.model.po.AIGeneratedQuestionCount;
import com.joinus.knowledge.model.vo.FileVO;
import com.joinus.knowledge.model.vo.QuestionAnswerDetailVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import com.joinus.knowledge.service.MathAnswersService;
import com.joinus.knowledge.service.MathQuestionsService;
import com.joinus.knowledge.service.QuestionAnswerRelationsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class GenerateQuestionServiceImpl {
    @Resource
    private MathQuestionsMapper mathQuestionsMapper;

    @Resource
    private MathQuestionsService mathQuestionsService;

    @Resource
    private MathAnswersService mathAnswersService;

    @Resource
    private QuestionAnswerRelationsService questionAnswerRelationsService;

    @Resource
    private WebClient difyWebClient;

    @Value("${dify.workflow.generate-math-question:app-D9hr5KamYhIK3Nc4omEuEUBq}")
    private String apiKey;

    int corePoolSize = 10;
    int maximumPoolSize = 10;
    long keepAliveTime = 0L;
    int queueCapacity = 5000;

    BlockingQueue<Runnable> workQueue = new ArrayBlockingQueue<>(queueCapacity);

    ThreadPoolExecutor executor = new ThreadPoolExecutor(
            corePoolSize,
            maximumPoolSize,
            keepAliveTime,
            TimeUnit.MILLISECONDS,
            workQueue,
            new ThreadPoolExecutor.AbortPolicy()
    );

    public void generateQuestion(int count) {
        List<AIGeneratedQuestionCount> list = mathQuestionsMapper.listNotEnoughAIGeneratedQuestionCount(count);
        list.forEach(questionCount -> executor.execute(() -> generate(questionCount, count)));
    }

    private void generate(AIGeneratedQuestionCount aiGeneratedQuestionCount, int limitCount) {
        int generateCount = Math.min(limitCount - aiGeneratedQuestionCount.getTotalCount(), 5);

        MathQuestion mathQuestion = mathQuestionsMapper.selectById(aiGeneratedQuestionCount.getQuestionId());
        QuestionAnswerRelation questionAnswerRelation = questionAnswerRelationsService.lambdaQuery()
                .eq(QuestionAnswerRelation::getQuestionId, aiGeneratedQuestionCount.getQuestionId())
                .list()
                .getFirst();
        QuestionAnswerDetailVO answerVO = mathAnswersService.getById(questionAnswerRelation.getAnswerId());
        DifyGenerateQuestionParam generateQuestionParam = DifyGenerateQuestionParam.builder()
                .questionId(aiGeneratedQuestionCount.getQuestionId().toString())
                .content(mathQuestion.getContent())
                .type(mathQuestion.getQuestionType().getType())
                .answerId(answerVO.getId().toString())
                .answer(answerVO.getAnswer())
                .analysis(answerVO.getContent())
                .numberOfQuestions(String.valueOf(generateCount))
                .build();
        QuestionDetailVO questionDetailVO = mathQuestionsService.getDetailById(aiGeneratedQuestionCount.getQuestionId());
        List<DifyImageParam> imageList = new ArrayList<>();
        List<FileVO> fileVOList = questionDetailVO.getFiles();
        if (CollUtil.isNotEmpty(fileVOList)) {
            for (FileVO fileVO : fileVOList) {
                DifyImageParam difyImageParam = DifyImageParam.builder()
                        .transferMethod("remote_url")
                        .type("image")
                        .url(fileVO.getOssUrl())
                        .build();
                imageList.add(difyImageParam);
            }
            generateQuestionParam.setContentImage(imageList);
        }
        DifyParam difyParam = DifyParam.builder()
                .inputs(generateQuestionParam)
                .responseMode("streaming")
                .user("edu-knowledge-hub")
                .build();
        try {
            String result = difyWebClient.post()
                    .uri("/workflows/run")
                    .header(Header.AUTHORIZATION.getValue(),"Bearer " + apiKey)
                    .bodyValue(difyParam)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(1200))
                    .onErrorResume(e -> {
                        log.error("调用dify生题失败: questionId={}, 错误={}", mathQuestion.getId(), e.getMessage(), e);
                        return Mono.just("生题失败: " + e.getMessage());
                    })
                    .block();
        } catch (Exception e) {
            log.error("调用dify生题失败: questionId={}, 错误={}", mathQuestion.getId(), e.getMessage(), e);
        }
    }
}
