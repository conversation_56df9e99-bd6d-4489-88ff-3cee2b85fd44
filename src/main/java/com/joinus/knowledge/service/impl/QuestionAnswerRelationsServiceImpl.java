package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.QuestionAnswerRelation;
import com.joinus.knowledge.service.QuestionAnswerRelationsService;
import com.joinus.knowledge.mapper.QuestionAnswerRelationsMapper;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_answers】的数据库操作Service实现
* @createDate 2025-03-01 10:22:43
*/
@Service
public class QuestionAnswerRelationsServiceImpl extends ServiceImpl<QuestionAnswerRelationsMapper, QuestionAnswerRelation>
    implements QuestionAnswerRelationsService{

    @Override
    public void createAssociation(UUID questionId, UUID answerId) {
        QuestionAnswerRelation questionAnswerRelation = lambdaQuery().eq(QuestionAnswerRelation::getQuestionId, questionId)
                .eq(QuestionAnswerRelation::getAnswerId, answerId)
                .one();
        if (null != questionAnswerRelation) {
            return;
        }
        QuestionAnswerRelation relation = new QuestionAnswerRelation();
        relation.setQuestionId(questionId);
        relation.setAnswerId(answerId);
        save(relation);
    }

    @Override
    public void deleteAssociation(UUID questionId, UUID answerId) {
        remove(lambdaQuery().eq(QuestionAnswerRelation::getQuestionId, questionId)
                .eq(QuestionAnswerRelation::getAnswerId, answerId)
                .getWrapper());
    }
}




