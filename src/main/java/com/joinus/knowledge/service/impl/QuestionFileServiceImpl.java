package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.QuestionFileType;
import com.joinus.knowledge.mapper.FilesMapper;
import com.joinus.knowledge.mapper.QuestionFileMapper;
import com.joinus.knowledge.model.dto.QuestionFileDTO;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathAnswerFiles;
import com.joinus.knowledge.model.entity.QuestionFile;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.service.QuestionFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_files】的数据库操作Service实现
* @createDate 2025-03-11 18:31:25
*/
@Service
public class QuestionFileServiceImpl extends ServiceImpl<QuestionFileMapper, QuestionFile>
    implements QuestionFileService{

    @Autowired
    private FilesService filesService;
    @Autowired
    private FilesMapper filesMapper;

    @Override
    public void saveOrUpdateBatch(UUID id, List<String> files) {
        if (CollUtil.isEmpty(files)) {
            return;
        }
        List<QuestionFileDTO> questionFiles = baseMapper.listByQuestionId(id);
        //删除掉多余的关联关系
        questionFiles.forEach(questionFile -> {
            if (!files.contains(questionFile.getOssUrl())) {
                LambdaQueryWrapper<QuestionFile> wrapper = Wrappers.lambdaQuery(QuestionFile.class)
                        .eq(QuestionFile::getFileId, questionFile.getFileId())
                        .eq(QuestionFile::getQuestionId, id);
                baseMapper.delete(wrapper);
            }
        });
        files.forEach(ossUrl -> {
            if (questionFiles.stream().noneMatch(questionFile -> questionFile.getOssUrl().equals(ossUrl))) {
                String pathWithoutParams = StrUtil.subBefore(ossUrl, "?", true);

                LambdaQueryWrapper<File> wrapper = Wrappers.lambdaQuery(File.class)
                        .eq(File::getOssUrl, ossUrl);
                File file = filesMapper.selectOne(wrapper);
                if (null == file) {
                    file = filesService.save(FileUtil.getName(pathWithoutParams), FileUtil.getSuffix(pathWithoutParams), "", ossUrl, OssEnum.MINIO_EDU_KNOWLEDGE_HUB);
                }
                baseMapper.insert(QuestionFile.builder()
                        .questionId(id)
                        .fileId(file.getId())
                        .build());
            }
        });
    }

    @Override
    public List<QuestionFile> selectByQuestionId(UUID questionId) {
        return lambdaQuery()
                .eq(QuestionFile::getQuestionId, questionId)
                .list();
    }

    @Override
    public void removeByQuestionId(UUID questionId) {
        List<QuestionFile> questionFiles = selectByQuestionId(questionId);
        if (CollUtil.isNotEmpty(questionFiles)) {
            List<UUID> fileIds = questionFiles.stream().map(QuestionFile::getFileId).toList();
            // 使用批量删除方法
            if (CollUtil.isNotEmpty(fileIds)) {
                fileIds.stream().forEach(id -> filesService.removeById(id));
            }
            baseMapper.delete(lambdaQuery().eq(QuestionFile::getQuestionId, questionId).getWrapper());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UUID> saveQuestionFileAndRelation(List<String> objectNames, UUID questionId, OssEnum ossEnum, QuestionFileType questionFileType) {
        List<UUID> fileIds = new ArrayList<>();
        for (int i = 0; i < objectNames.size(); i++) {
            String objectName = objectNames.get(i);
            String originalImageType = objectName.endsWith(".jpg") ? "jpg" : "png";
            String originalMimeType = originalImageType.equals("jpg") ? "image/jpeg" : "image/png";
            com.joinus.knowledge.model.entity.File file = filesService.save(objectName.substring(objectName.lastIndexOf("/") + 1), originalImageType, originalMimeType, objectName, ossEnum);
            QuestionFile questionFile = QuestionFile.builder()
                    .questionId(questionId)
                    .fileId(file.getId())
                    .sortNo(i)
                    .type(questionFileType.getValue())
                    .build();
            this.save(questionFile);
            fileIds.add(file.getId());
        }
        return fileIds;
    }
}




