package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.model.dto.SchoolExamDTO;
import com.joinus.knowledge.service.SmartStudyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class SmartStudyServiceImpl implements SmartStudyService {

    @Value("${server.domain.smart-study:https://smart-study-analytics.uat.ijiaxiao.net}")
    private String smartStudyServerDomain;

    @Override
    public void addOrUpdateSchoolExamRelation(UUID id, List<Long> schoolIds) {
        String url = StrUtil.format(smartStudyServerDomain + "/api/smart-study/math/anon/exam-school-relation/{}", id);
        try {
            log.info("新增或更新学校试卷关系 request {} {}", url, JSONUtil.toJsonStr(schoolIds));
            HttpResponse response = HttpUtil.createPost(url)
                    .body(JSONUtil.toJsonStr(schoolIds))
                    .method(Method.POST)
                    .timeout(15 * 1000)
                    .execute();
            if (null != response && response.isOk()) {
                JSONObject jsonObject = JSONUtil.parseObj(response.body());
                log.info("新增或更新学校试卷关系 responseBody {}", response.body());
                if (null != jsonObject.get("code") && jsonObject.get("code").toString().equals("200")) {
                    log.info("新增或更新学校试卷关系成功");
                }else {
                    throw new BusinessException("新增或更新学校试卷关系失败：" +  jsonObject.get("msg"));
                }
            } else {
                log.warn("新增或更新学校试卷关系失败 {}", JSONUtil.toJsonStr(response));
                throw new BusinessException("新增或更新学校试卷关系失败：" + JSONUtil.toJsonStr(response));
            }
        } catch (Exception e) {
            throw new BusinessException("新增或更新学校试卷关系错误：" + e.getMessage());
        }
    }

    @Override
    public void deleteSchoolExamRelation(UUID id) {
        String url = StrUtil.format(smartStudyServerDomain + "/api/smart-study/math/anon/exam-school-relation/{examId}", id);
        try {
            log.info("删除学校试卷关系 request {}", url);
            HttpResponse response = HttpUtil.createPost(url)
                    .method(Method.DELETE)
                    .timeout(15 * 1000)
                    .execute();
            if (null != response && response.isOk()) {
                JSONObject jsonObject = JSONUtil.parseObj(response.body());
                log.info("删除学校试卷关系 responseBody {}", response.body());
                if (null != jsonObject.get("code") && jsonObject.get("code").toString().equals("200")) {
                    log.info("删除学校试卷关系成功");
                }else {
                    throw new BusinessException("删除学校试卷关系失败：" +  jsonObject.get("msg"));
                }
            } else {
                log.warn("删除学校试卷关系失败 {}", JSONUtil.toJsonStr(response));
                throw new BusinessException("删除学校试卷关系失败：" + JSONUtil.toJsonStr(response));
            }
        } catch (Exception e) {
            throw new BusinessException("删除学校试卷关系错误：" + e.getMessage());
        }
    }

    @Override
    public List<JSONObject> listSchoolExamRelationByExamId(UUID id) {
        String url = StrUtil.format(smartStudyServerDomain + "/api/smart-study/math/anon/exam-school-relation/{}", id);
        try {
            log.info("查询学校试卷关系 request {}", url);
            HttpResponse response = HttpUtil.createGet(url)
                    .timeout(15 * 1000)
                    .execute();
            if (null != response && response.isOk()) {
                JSONObject jsonObject = JSONUtil.parseObj(response.body());
                log.info("查询学校试卷关系 responseBody {}", response.body());
                if (null != jsonObject.get("code") && jsonObject.get("code").toString().equals("200")) {
                    log.info("查询学校试卷关系成功");
                    JSONArray dataArray = JSONUtil.parseArray(jsonObject.get("data"));
                    return dataArray.toList(JSONObject.class);
                }else {
                    throw new BusinessException("查询学校试卷关系失败：" +  jsonObject.get("msg"));
                }
            } else {
                log.warn("查询学校试卷关系失败 {}", JSONUtil.toJsonStr(response));
                throw new BusinessException("查询学校试卷关系失败：" + JSONUtil.toJsonStr(response));
            }
        } catch (Exception e) {
            throw new BusinessException("查询学校试卷关系错误：" + e.getMessage());
        }
    }

    @Override
    public List<UUID> listSchoolExamRelationBySchoolId(Long schoolId) {
        String url = StrUtil.format(smartStudyServerDomain + "/api/smart-study/math/anon/exam-school-relation?schoolId={}", schoolId);
        try {
            log.info("查询学校试卷关系 request {}", url);
            HttpResponse response = HttpUtil.createGet(url)
                    .timeout(15 * 1000)
                    .execute();
            if (null != response && response.isOk()) {
                JSONObject jsonObject = JSONUtil.parseObj(response.body());
                log.info("查询学校试卷关系 responseBody {}", response.body());
                if (null != jsonObject.get("code") && jsonObject.get("code").toString().equals("200")) {
                    log.info("查询学校试卷关系成功");
                    JSONArray dataArray = JSONUtil.parseArray(jsonObject.get("data"));
                    List<SchoolExamDTO> list = dataArray.toList(SchoolExamDTO.class);
                    if (CollUtil.isNotEmpty(list)) {
                        return list.stream().map(SchoolExamDTO::getExamId).toList();
                    }
                    return List.of();
                }else {
                    throw new BusinessException("查询学校试卷关系失败：" +  jsonObject.get("msg"));
                }
            } else {
                log.warn("查询学校试卷关系失败 {}", JSONUtil.toJsonStr(response));
                throw new BusinessException("查询学校试卷关系失败：" + JSONUtil.toJsonStr(response));
            }
        } catch (Exception e) {
            throw new BusinessException("查询学校试卷关系错误：" + e.getMessage());
        }
    }

    @Override
    public List<SchoolExamDTO> listSchoolExamRelation(List<UUID> examIdList) {
        String url = smartStudyServerDomain + "/api/smart-study/math/anon/exam-school-relation";
        try {
            log.info("查询学校试卷关系 request {}", url);
            HttpResponse response = HttpUtil.createGet(url)
                    .timeout(15 * 1000)
                    .form("examIds", examIdList)
                    .execute();
            if (null != response && response.isOk()) {
                JSONObject jsonObject = JSONUtil.parseObj(response.body());
                log.info("查询学校试卷关系 responseBody {}", response.body());
                if (null != jsonObject.get("code") && jsonObject.get("code").toString().equals("200")) {
                    log.info("查询学校试卷关系成功");
                    JSONArray dataArray = JSONUtil.parseArray(jsonObject.get("data"));
                    return dataArray.toList(SchoolExamDTO.class);
                }else {
                    throw new BusinessException("查询学校试卷关系失败：" +  jsonObject.get("msg"));
                }
            } else {
                log.warn("查询学校试卷关系失败 {}", JSONUtil.toJsonStr(response));
                throw new BusinessException("查询学校试卷关系失败：" + JSONUtil.toJsonStr(response));
            }
        } catch (Exception e) {
            throw new BusinessException("查询学校试卷关系错误：" + e.getMessage());
        }
    }
}
