package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.MathExamPoints;
import com.joinus.knowledge.service.MathExamPointsService;
import com.joinus.knowledge.mapper.MathExamPointsMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【math_exam_points】的数据库操作Service实现
* @createDate 2025-02-28 14:12:06
*/
@Service
public class MathExamPointsServiceImpl extends ServiceImpl<MathExamPointsMapper, MathExamPoints>
    implements MathExamPointsService{

}




