package com.joinus.knowledge.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.model.vo.SchoolVO;
import com.joinus.knowledge.service.BasicApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class BasicApiServiceImpl implements BasicApiService {


    @Value("${server.domain.basic-api:https://basic-api.uat.ijiaxiao.net}")
    private String smartStudyServerDomain;

    @Override
    public List<SchoolVO> listSchools(String schoolName) {
        String url = StrUtil.format(smartStudyServerDomain + "/external/school?schoolName={}", schoolName);
        try {
            log.info("查询基础业务学校信息 request {}", url);
            HttpResponse response = HttpUtil.createGet(url)
                    .timeout(15 * 1000)
                    .execute();
            if (null != response && response.isOk()) {
                JSONObject jsonObject = JSONUtil.parseObj(response.body());
                log.info("查询基础业务学校信息 responseBody {}", response.body());
                if (null != jsonObject.get("code") && jsonObject.get("code").toString().equals("200")) {
                    return JSONUtil.toList(JSONUtil.parseArray(jsonObject.get("data")), SchoolVO.class);
                }else {
                    throw new BusinessException("查询基础业务学校信息失败：" +  jsonObject.get("msg"));
                }
            } else {
                log.warn("查询基础业务学校信息失败 {}", JSONUtil.toJsonStr(response));
                throw new BusinessException("查询基础业务学校信息失败：" + JSONUtil.toJsonStr(response));
            }
        } catch (Exception e) {
            throw new BusinessException("查询基础业务学校信息错误：" + e.getMessage());
        }

    }
}
