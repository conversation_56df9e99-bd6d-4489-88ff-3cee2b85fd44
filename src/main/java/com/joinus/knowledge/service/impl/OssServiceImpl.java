package com.joinus.knowledge.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.vo.OssFileVO;
import com.joinus.knowledge.service.OssService;
import com.joinus.knowledge.utils.AliOssUtils;
import com.joinus.knowledge.utils.MinioUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.Date;

@Service
public class OssServiceImpl implements OssService {

    @Autowired
    private AliOssUtils aliOssUtils;
    @Autowired
    private MinioUtils minioUtils;


    @Override
    public OssFileVO uploadInputStreamToOss(OssEnum ossEnum, String businessType, String fileName, InputStream inputStream, long size) throws IOException {
        String key = "";
        String presignedDownloadUrl = "";
        if (OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB == ossEnum) {
            key = aliOssUtils.upload(inputStream.readAllBytes(), businessType, fileName);
            presignedDownloadUrl = aliOssUtils.generatePresignedUrl(key);
        } else {
            key = StrUtil.format("{}/{}/{}", businessType, DateUtil.format(new Date(), "yyyyMMdd"), fileName);
            minioUtils.uploadInputStream(ossEnum.getBucket(), key, inputStream, size);
            presignedDownloadUrl = minioUtils.getPresignedDownloadUrl(ossEnum.getBucket(), key, fileName);
        }
        OssFileVO ossFile = OssFileVO.builder()
                .ossEnum(ossEnum)
                .key(key)
                .presignedUrl(presignedDownloadUrl)
                .build();
        return ossFile;
    }

    @Override
    public String getPresignedDownloadUrl(OssEnum ossEnum, String ossKey) {
        return switch (ossEnum) {
            case OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB -> aliOssUtils.generatePresignedUrl(ossEnum.getBucket(), ossKey);
            case OssEnum.MINIO_EDU_KNOWLEDGE_HUB -> minioUtils.getPresignedDownloadUrl(ossEnum.getBucket(), ossKey, ossKey.substring(ossKey.lastIndexOf("/") + 1));
        };
    }

    @Override
    public OssFileVO getPresignedInfo(OssEnum ossEnum, String ossKey) {
        String presignedDownloadUrl = getPresignedDownloadUrl(ossEnum, ossKey);
        return OssFileVO.builder()
                .ossEnum(ossEnum)
                .presignedUrl(presignedDownloadUrl)
                .key(ossKey)
                .build();
    }
}




