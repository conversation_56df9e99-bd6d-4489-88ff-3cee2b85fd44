package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.MathGraphicsScriptStatusEnum;
import com.joinus.knowledge.enums.MathLabelTypeEnum;
import com.joinus.knowledge.model.dto.CandidateQuestion;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.param.GraphicsSubmitParam;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.mapper.QuestionGraphicsScriptMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_question_graphics_scripts(问题图案生成脚本)】的数据库操作Service实现
* @createDate 2025-04-17 16:04:14
*/
@Slf4j
@Service
public class QuestionGraphicsScriptServiceImpl extends ServiceImpl<QuestionGraphicsScriptMapper, QuestionGraphicsScript>
    implements QuestionGraphicsScriptService{

    @Resource
    private MathLabelService mathLabelService;

    @Resource
    private QuestionLabelService questionLabelService;

    @Resource
    private MathQuestionsService mathQuestionsService;

    @Resource
    private QuestionKnowledgePointsService questionKnowledgePointsService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer claimScripts(Integer count, String username) {
        /*List<QuestionGraphicsScript> scriptList = this.lambdaQuery()
                .isNull(QuestionGraphicsScript::getUsername)
                .last("LIMIT " + count + " FOR UPDATE SKIP LOCKED")
                .list();*/

        List<UUID> questionIds = this.extractPreferredScripts(count);
        if (CollUtil.isEmpty(questionIds)) {
            return 0;
        }
        List<QuestionGraphicsScript> scriptList = this.lambdaQuery()
                .in(QuestionGraphicsScript::getQuestionId, questionIds)
                .last("LIMIT " + count + " FOR UPDATE SKIP LOCKED")
                .list();
        if (CollUtil.isEmpty(scriptList)) {
            return 0;
        }
        scriptList.forEach(script -> script.setUsername(username));
        this.lambdaUpdate().set(QuestionGraphicsScript::getUsername, username)
                .set(QuestionGraphicsScript::getUpdatedAt, new Date())
                .in(QuestionGraphicsScript::getQuestionId, scriptList.stream().map(QuestionGraphicsScript::getQuestionId).toList())
                .isNull(QuestionGraphicsScript::getUsername)
                .update();
        Long successCount = this.lambdaQuery()
                .in(QuestionGraphicsScript::getQuestionId, scriptList.stream().map(QuestionGraphicsScript::getQuestionId).toList())
                .eq(QuestionGraphicsScript::getUsername, username)
                .count();
        return successCount.intValue();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submit(GraphicsSubmitParam graphicsSubmitParam) {
        if (CollUtil.isNotEmpty(graphicsSubmitParam.labelNames()) || CollUtil.isNotEmpty(graphicsSubmitParam.removeLabelNames())) {
            this.updateWrongQuestionLabel(graphicsSubmitParam.questionId(), graphicsSubmitParam.labelNames(), graphicsSubmitParam.removeLabelNames(), graphicsSubmitParam.remark(), graphicsSubmitParam.username());
        } else {
            this.updateRemark(graphicsSubmitParam.questionId(), graphicsSubmitParam.remark());
        }
    }

    @Override
    public void ignore(UUID questionId, String remark) {
        mathLabelService.lambdaQuery()
                .eq(MathLabel::getType, MathLabelTypeEnum.ILLUSTRATION_NEED.toString())
                .list().forEach(mathLabel -> {
                    questionLabelService.deleteRelations(questionId, List.of(mathLabel.getId()));
                });

        mathLabelService.lambdaQuery()
                .eq(MathLabel::getName, "无需人工配图")
                .eq(MathLabel::getType, MathLabelTypeEnum.ILLUSTRATION_NEED.toString())
                .list().forEach(mathLabel -> {
                    questionLabelService.save(QuestionLabel.builder()
                            .questionId(questionId)
                            .labelId(mathLabel.getId())
                            .labelType(mathLabel.getType())
                            .build());
                });

        this.lambdaUpdate()
                .set(StrUtil.isNotBlank(remark), QuestionGraphicsScript::getRemark, remark)
                .set(QuestionGraphicsScript::getStatus, MathGraphicsScriptStatusEnum.IGNORED)
                .set(QuestionGraphicsScript::getUpdatedAt, new Date())
                .set(QuestionGraphicsScript::getCompletedAt, new Date())
                .eq(QuestionGraphicsScript::getQuestionId, questionId)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void verify(UUID questionId, boolean verified, String remark) {
        lambdaUpdate()
                .set(QuestionGraphicsScript::getStatus, verified ? MathGraphicsScriptStatusEnum.VERIFIED : MathGraphicsScriptStatusEnum.REJECTED)
                .set(StrUtil.isNotBlank(remark), QuestionGraphicsScript::getRemark, remark)
                .set(QuestionGraphicsScript::getUpdatedAt, new Date())
                .eq(QuestionGraphicsScript::getQuestionId, questionId)
                .update();
        if (!verified) {
            mathQuestionsService.lambdaUpdate()
                    .set(MathQuestion::getEnabled, false)
                    .eq(MathQuestion::getId, questionId)
                    .update();
            mathLabelService.lambdaQuery()
                    .eq(MathLabel::getName, "无需人工配图")
                    .eq(MathLabel::getType, MathLabelTypeEnum.ILLUSTRATION_NEED.toString())
                    .list().forEach(mathLabel -> {
                        questionLabelService.deleteRelations(questionId, List.of(mathLabel.getId()));
                    });
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRemark(UUID questionId, String remark) {
        this.lambdaUpdate()
                .set(StrUtil.isNotBlank(remark), QuestionGraphicsScript::getRemark, remark)
                .set(QuestionGraphicsScript::getStatus, MathGraphicsScriptStatusEnum.SUBMITTED)
                .set(QuestionGraphicsScript::getUpdatedAt, new Date())
                .set(QuestionGraphicsScript::getCompletedAt, new Date())
                .eq(QuestionGraphicsScript::getQuestionId, questionId)
                .update();
        mathQuestionsService.lambdaUpdate()
                .set(MathQuestion::getEnabled, true)
                .eq(MathQuestion::getId, questionId)
                .update();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateWrongQuestionLabel(UUID questionId, List<String> labelNames, List<String> removeLabelNames, String remark, String username) {
        MathGraphicsScriptStatusEnum status = MathGraphicsScriptStatusEnum.SUBMITTED;
        if (CollUtil.isNotEmpty(labelNames)) {
            mathLabelService.lambdaQuery()
                    .in(MathLabel::getName, labelNames)
                    .eq(MathLabel::getType, MathLabelTypeEnum.CORRECTNESS.toString())
                    .list().forEach(mathLabel -> {
                        questionLabelService.save(QuestionLabel.builder()
                                .questionId(questionId)
                                .labelId(mathLabel.getId())
                                .labelType(mathLabel.getType())
                                .build());
                    });
            status = MathGraphicsScriptStatusEnum.FAILED;
        }

        if (CollUtil.isNotEmpty(removeLabelNames)) {
            mathLabelService.lambdaQuery()
                    .in(MathLabel::getName, removeLabelNames)
                    .eq(MathLabel::getType, MathLabelTypeEnum.CORRECTNESS.toString())
                    .list().forEach(mathLabel -> {
                        questionLabelService.deleteRelations(questionId, List.of(mathLabel.getId()));
                    });
            status = MathGraphicsScriptStatusEnum.SUBMITTED;
        }

        MathQuestion question = mathQuestionsService.getById(questionId);
        question.setEnabled(questionLabelService.lambdaQuery()
                        .eq(QuestionLabel::getLabelType, MathLabelTypeEnum.CORRECTNESS.toString())
                        .eq(QuestionLabel::getQuestionId, questionId)
                        .count() == 0);
        mathQuestionsService.updateById(question);

        this.lambdaUpdate()
                .set(StrUtil.isNotBlank(remark), QuestionGraphicsScript::getRemark, remark)
                .set(QuestionGraphicsScript::getStatus, status)
                .set(QuestionGraphicsScript::getUsername, username)
                .set(QuestionGraphicsScript::getUpdatedAt, new Date())
                .set(CollUtil.isNotEmpty(labelNames), QuestionGraphicsScript::getCompletedAt, new Date())
                .eq(QuestionGraphicsScript::getQuestionId, questionId)
                .update();
    }

    public List<UUID> extractPreferredScripts(int numberOfItemsToExtract) {
        Set<UUID> selectedQuestionIds = new HashSet<>();
        List<Map<String, Object>> keyPointsCounts = baseMapper.getInitialKeyPointCounts();
        Map<UUID, Integer> keyPointsMap = keyPointsCounts.stream()
                .collect(Collectors.toMap(
                        keyPointCount -> (UUID) keyPointCount.get("keyPointId"),
                        keyPointCount -> ((Number) keyPointCount.get("count")).intValue(),
                        (existingValue, newValue) -> newValue
                ));

        log.debug("已分配的知识点关联题的数量: {}", keyPointsMap);

        for (int i = 0; i < numberOfItemsToExtract; i++) {
            List<CandidateQuestion> availableCandidates = baseMapper.getAvailableCandidates(selectedQuestionIds);

            if (availableCandidates.isEmpty()) {
                log.info("没有可用的候选题");
                break;
            }

            CandidateQuestion bestCandidate = null;
            long minKnowledgePointScoreForBestCandidate = Long.MAX_VALUE;

            for (CandidateQuestion candidate : availableCandidates) {
                if (candidate.getKeyPointIds().isEmpty()) {
                    continue;
                }

                long currentMinKnowledgePointScoreForThisCandidate = Long.MAX_VALUE;
                for (UUID kpId : candidate.getKeyPointIds()) {
                    currentMinKnowledgePointScoreForThisCandidate = Math.min(
                            currentMinKnowledgePointScoreForThisCandidate,
                            keyPointsMap.getOrDefault(kpId, 0)
                    );
                }

                if (currentMinKnowledgePointScoreForThisCandidate < minKnowledgePointScoreForBestCandidate) {
                    minKnowledgePointScoreForBestCandidate = currentMinKnowledgePointScoreForThisCandidate;
                    bestCandidate = candidate;
                } else if (currentMinKnowledgePointScoreForThisCandidate == minKnowledgePointScoreForBestCandidate) {
                    bestCandidate = candidate;
                }
            }

            if (bestCandidate == null) {
                log.info("没有合适的候选题");
                break;
            }

            selectedQuestionIds.add(bestCandidate.getQuestionId()); // Add question_id
            log.debug("第{}次: Selected Question ID: {}, 知识点: {}, 得分: {})", i + 1, bestCandidate.getQuestionId(), bestCandidate.getKeyPointIds(), minKnowledgePointScoreForBestCandidate);

            List<UUID> knowledgePointsOfChosenItem = bestCandidate.getKeyPointIds();
            if (knowledgePointsOfChosenItem.isEmpty() && bestCandidate.getQuestionId() != null) {
                log.info("候选题没有知识点或题型:{}", bestCandidate.getQuestionId());
                knowledgePointsOfChosenItem = questionKnowledgePointsService.lambdaQuery()
                        .eq(QuestionKnowledgePoint::getQuestionId, bestCandidate.getQuestionId())
                        .list()
                        .stream()
                        .map(QuestionKnowledgePoint::getKnowledgePointId)
                        .toList();
            }

            for (UUID keyPointId : knowledgePointsOfChosenItem) {
                keyPointsMap.put(keyPointId, keyPointsMap.getOrDefault(keyPointId, 0) + 1);
            }
            log.debug("更新抽取的知识点下关联的题目数量: {}", keyPointsMap);
        }
        return new ArrayList<>(selectedQuestionIds);
    }
}




