package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.SectionKnowledgePoint;
import com.joinus.knowledge.model.entity.SectionQuestionType;
import com.joinus.knowledge.model.po.SectionQuestionTypePO;
import com.joinus.knowledge.service.SectionQuestionTypesService;
import com.joinus.knowledge.mapper.SectionQuestionTypesMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_section_question_types】的数据库操作Service实现
* @createDate 2025-03-06 16:06:22
*/
@Service
public class SectionQuestionTypesServiceImpl extends ServiceImpl<SectionQuestionTypesMapper, SectionQuestionType>
    implements SectionQuestionTypesService{

    @Override
    public void createRelation(UUID sectionId, UUID questionTypeId, Integer pageIndex) {
        LambdaQueryWrapper<SectionQuestionType> wrapper = Wrappers.lambdaQuery(SectionQuestionType.class)
                .eq(SectionQuestionType::getSectionId, sectionId)
                .eq(SectionQuestionType::getQuestionTypeId, questionTypeId)
                .eq(SectionQuestionType::getPageIndex, pageIndex);
        Long count = baseMapper.selectCount(wrapper);
        if (count == 0) {
            SectionQuestionType sectionQuestionType = SectionQuestionType.builder()
                    .questionTypeId(questionTypeId)
                    .sectionId(sectionId)
                    .pageIndex(pageIndex)
                    .build();
            baseMapper.insert(sectionQuestionType);
        }
    }

    @Override
    public void deleteRelation(UUID sectionId, UUID questionTypeId, Integer pageIndex) {
        LambdaQueryWrapper<SectionQuestionType> wrapper = Wrappers.lambdaQuery(SectionQuestionType.class)
                .eq(SectionQuestionType::getSectionId, sectionId)
                .eq(SectionQuestionType::getQuestionTypeId, questionTypeId)
                .eq(SectionQuestionType::getPageIndex, pageIndex);
        Long count = baseMapper.selectCount(wrapper);
        if (count > 0) {
            baseMapper.delete(wrapper);
        }
    }

    @Override
    public boolean createAssociation(UUID sectionId, UUID questionTypeId) {
// 先检查是否已存在
        LambdaQueryWrapper<SectionQuestionType> eq = Wrappers.lambdaQuery(SectionQuestionType.class)
                .eq(SectionQuestionType::getSectionId, sectionId)
                .eq(SectionQuestionType::getQuestionTypeId, questionTypeId);
        List<SectionQuestionType> sectionQuestionTypes = baseMapper.selectList(eq);
        if (CollUtil.isNotEmpty(sectionQuestionTypes)) {
            return true;
        }

        // 创建新实体并保存
        SectionQuestionType newRelation = new SectionQuestionType();
        newRelation.setSectionId(sectionId);
        newRelation.setQuestionTypeId(questionTypeId);
        return save(newRelation);
    }

    @Override
    public void removeEntity(SectionQuestionTypePO sectionQt) {
        LambdaQueryWrapper<SectionQuestionType> wrapper = Wrappers.lambdaQuery(SectionQuestionType.class)
                .eq(SectionQuestionType::getSectionId, sectionQt.getSectionId())
                .eq(SectionQuestionType::getQuestionTypeId, sectionQt.getId())
                .eq(null != sectionQt.getPageIndex(), SectionQuestionType::getPageIndex, sectionQt.getPageIndex())
                .isNull(null == sectionQt.getPageIndex(), SectionQuestionType::getPageIndex);
        Long count = baseMapper.selectCount(wrapper);
        if (count > 0) {
            lambdaUpdate().set(SectionQuestionType::getRemain, false)
                    .eq(SectionQuestionType::getSectionId, sectionQt.getSectionId())
                    .eq(SectionQuestionType::getQuestionTypeId, sectionQt.getId())
                    .eq(null != sectionQt.getPageIndex(), SectionQuestionType::getPageIndex, sectionQt.getPageIndex())
                    .isNull(null == sectionQt.getPageIndex(), SectionQuestionType::getPageIndex)
                    .update();
        }
    }
}




