package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.UserLog;
import com.joinus.knowledge.service.UserLogService;
import com.joinus.knowledge.mapper.UserLogMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【math_user_logs】的数据库操作Service实现
* @createDate 2025-03-14 09:02:51
*/
@Service
public class UserLogServiceImpl extends ServiceImpl<UserLogMapper, UserLog>
    implements UserLogService{

}




