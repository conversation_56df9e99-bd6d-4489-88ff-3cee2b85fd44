package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.mapper.*;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.po.SectionKeypointCountPO;
import com.joinus.knowledge.model.po.SectionKnowledgePointPO;
import com.joinus.knowledge.model.po.SectionQuestionTypePO;
import com.joinus.knowledge.model.po.SectionVideoPO;
import com.joinus.knowledge.model.vo.MathSectionVO;
import com.joinus.knowledge.model.vo.QuerySectionVideoVO;
import com.joinus.knowledge.model.vo.SectionKeypointVO;
import com.joinus.knowledge.model.vo.SectionVO;
import com.joinus.knowledge.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_sections】的数据库操作Service实现
* @createDate 2025-03-06 16:06:22
*/
@Service
public class MathSectionServiceImpl extends ServiceImpl<MathSectionMapper, MathSection>
    implements MathSectionService {

    @Autowired
    private SectionKnowledgePointsMapper sectionKnowledgePointsMapper;
    @Autowired
    private SectionQuestionTypesMapper sectionQuestionTypesMapper;
    @Autowired
    private MathKnowledgePointsMapper mathKnowledgePointsMapper;
    @Autowired
    private MathQuestionTypesMapper mathQuestionTypesMapper;
    @Autowired
    private MathSectionMapper mathSectionMapper;
    @Autowired
    private QuestionKnowledgePointsService questionKnowledgePointsService;
    @Autowired
    private QuestionTypesMappingService questionTypesMappingService;;
    @Autowired
    private KnowledgePointQuestionTypeRelationshipMapper kpQtRelationMapper;
    @Autowired
    private QuestionKnowledgePointsMapper questionKnowledgePointsMapper;
    @Autowired
    private QuestionTypesMappingMapper questionTypesMappingMapper;
    @Autowired
    private SectionKnowledgePointsService sectionKnowledgePointsService;
    @Autowired
    private SectionQuestionTypesService sectionQuestionTypesService;
    @Autowired
    private MathExamsMapper mathExamsMapper;


    @Override
    public List<MathSection> getByPageNo(Integer pageNo, UUID textbookId, UUID sectionId) {
        return baseMapper.getByPageNo(pageNo, textbookId, sectionId);
    }

    @Override
    @Transactional
    public void updateSectionKeypointMapping(UpdateSectionKeypointParam param) {

        MathSection mathSection = mathSectionMapper.selectById(param.getSectionId());
        if (null == mathSection) {
            throw new BusinessException("要关联小节不存在");
        }

        if (param.getType().equals("knowledgePoint") || param.getType().equals("examPoint")) {

            MathKnowledgePoint mathKnowledgePoint = mathKnowledgePointsMapper.selectById(param.getKeyPointId());
            if (null == mathKnowledgePoint) {
                throw new BusinessException("关联知识点不存在");
            }

            LambdaQueryWrapper<SectionKnowledgePoint> oldEq = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getSectionId, param.getOldSectionId())
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId());
            List<SectionKnowledgePoint> existOldMappings = sectionKnowledgePointsMapper.selectList(oldEq);
            if (CollUtil.isEmpty(existOldMappings)) {
                throw new BusinessException("原关联关系不存在");
            }

            LambdaQueryWrapper<SectionKnowledgePoint> newEq = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getSectionId, param.getSectionId())
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId());
            List<SectionKnowledgePoint> existNewMappings = sectionKnowledgePointsMapper.selectList(newEq);
            if (CollUtil.isNotEmpty(existNewMappings)) {
                throw new BusinessException("新关系已存在");
            }

            sectionKnowledgePointsMapper.delete(oldEq);
            SectionKnowledgePoint newMapping = SectionKnowledgePoint.builder()
                    .sectionId(param.getSectionId())
                    .knowledgePointId(param.getKeyPointId())
                    .pageIndex(existOldMappings.get(0).getPageIndex())
                    .build();
            sectionKnowledgePointsMapper.insert(newMapping);
        } else {

            MathQuestionType mathQuestionType = mathQuestionTypesMapper.selectById(param.getKeyPointId());
            if (null == mathQuestionType) {
                throw new BusinessException("关联题型不存在");
            }

            LambdaQueryWrapper<SectionQuestionType> oldEq = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getSectionId, param.getOldSectionId())
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId());
            List<SectionQuestionType> existOldMappings = sectionQuestionTypesMapper.selectList(oldEq);
            if (CollUtil.isEmpty(existOldMappings)) {
                throw new BusinessException("原关联关系不存在");
            }

            LambdaQueryWrapper<SectionQuestionType> newEq = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getSectionId, param.getSectionId())
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId());
            List<SectionQuestionType> existNewMappings = sectionQuestionTypesMapper.selectList(newEq);
            if (CollUtil.isNotEmpty(existNewMappings)) {
                throw new BusinessException("新关系已存在");
            }

            sectionQuestionTypesMapper.delete(oldEq);
            SectionQuestionType newMapping = SectionQuestionType.builder()
                    .sectionId(param.getSectionId())
                    .questionTypeId(param.getKeyPointId())
                    .pageIndex(existOldMappings.get(0).getPageIndex())
                    .build();
            sectionQuestionTypesMapper.insert(newMapping);
        }
    }

    @Override
    @Transactional
    public void deleteSectionKeypointMapping(DeleteSectionKeypointParam param) {
        MathSection mathSection = mathSectionMapper.selectById(param.getSectionId());
        if (null == mathSection) {
            throw new BusinessException("要删除关系的小节不存在");
        }

        if (param.getType().equals("knowledgePoint") || param.getType().equals("examPoint")) {

            MathKnowledgePoint mathKnowledgePoint = mathKnowledgePointsMapper.selectById(param.getKeyPointId());
            if (null == mathKnowledgePoint) {
                throw new BusinessException("要删除关系关联知识点不存在");
            }

            LambdaQueryWrapper<SectionKnowledgePoint> oldEq = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getSectionId, param.getSectionId())
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId());
            List<SectionKnowledgePoint> existOldMappings = sectionKnowledgePointsMapper.selectList(oldEq);
            if (CollUtil.isEmpty(existOldMappings)) {
                throw new BusinessException("要删除关系不存在");
            }
            sectionKnowledgePointsMapper.delete(oldEq);
        } else {

            MathQuestionType mathQuestionType = mathQuestionTypesMapper.selectById(param.getKeyPointId());
            if (null == mathQuestionType) {
                throw new BusinessException("要删除关系关联题型不存在");
            }

            LambdaQueryWrapper<SectionQuestionType> oldEq = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getSectionId, param.getSectionId())
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId());
            List<SectionQuestionType> existOldMappings = sectionQuestionTypesMapper.selectList(oldEq);
            if (CollUtil.isEmpty(existOldMappings)) {
                throw new BusinessException("要删除关系不存在");
            }
            sectionQuestionTypesMapper.delete(oldEq);
        }
    }

    @Override
    public List<SectionVO> listAllSectionsByBookId(UUID id) {
        return baseMapper.listAllSectionsByBookId(id);
    }

    @Override
    public List<SectionKeypointVO> listKeypointsById(UUID sectionId) {
        return baseMapper.listKeypointsById(sectionId);
    }

    @Override
    @Transactional
    public void switchKeypointType(SwitchKeypointTypeParam param) {
        if ((param.getType().equals("knowledgePoint") || param.getType().equals("examPoint"))
                && param.getTargetType().equals("questionType")) {
            MathKnowledgePoint mathKnowledgePoint = mathKnowledgePointsMapper.selectById(param.getKeyPointId());
            if (null == mathKnowledgePoint) {
                throw new BusinessException("要切换知识点不存在");
            }
            MathQuestionType existMathQuestionType = mathQuestionTypesMapper.selectById(param.getKeyPointId());
            if (null != existMathQuestionType) {
                throw new BusinessException("要切换题型已存在");
            }

            MathQuestionType mathQuestionType = MathQuestionType.builder()
                    .id(mathKnowledgePoint.getId())
                    .name(mathKnowledgePoint.getName())
                    .sortNo(mathKnowledgePoint.getSortNo())
                    .build();
            mathQuestionTypesMapper.insert(mathQuestionType);

            //处理知识点与题目的关系
            List<UUID> questionIds = questionKnowledgePointsService.listQuestionIdsByKnowledgePointId(param.getKeyPointId());
            if (CollUtil.isNotEmpty(questionIds)) {
                List<QuestionTypesMapping> questionTypesMappings = questionIds.stream()
                        .filter(questionId -> {
                            List<QuestionTypesMapping> existQtms = questionTypesMappingMapper.selectList(
                                    Wrappers.lambdaQuery(QuestionTypesMapping.class)
                                            .eq(QuestionTypesMapping::getQuestionId, questionId)
                                            .eq(QuestionTypesMapping::getQuestionTypeId, mathKnowledgePoint.getId())
                            );
                            return CollUtil.isEmpty(existQtms);
                        }).map(questionId -> {
                            return QuestionTypesMapping.builder()
                                    .questionId(questionId)
                                    .questionTypeId(mathKnowledgePoint.getId())
                                    .build();
                        }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(questionTypesMappings)) {
                    questionTypesMappingService.saveBatch(questionTypesMappings);
                }
            }

            //处理知识点与小节的关系
            LambdaQueryWrapper<SectionKnowledgePoint> eq = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId());
            List<SectionKnowledgePoint> sectionKnowledgePoints = sectionKnowledgePointsMapper.selectList(eq);
            sectionKnowledgePoints.forEach(sectionKnowledgePoint -> {
                List<SectionQuestionType> sectionQuestionTypes = sectionQuestionTypesMapper.selectList(Wrappers.lambdaQuery(SectionQuestionType.class)
                        .eq(SectionQuestionType::getSectionId, sectionKnowledgePoint.getSectionId())
                        .eq(SectionQuestionType::getQuestionTypeId, sectionKnowledgePoint.getKnowledgePointId()));
                if (CollUtil.isEmpty(sectionQuestionTypes)) {
                    SectionQuestionType sectionQuestionType = SectionQuestionType.builder()
                            .sectionId(sectionKnowledgePoint.getSectionId())
                            .questionTypeId(sectionKnowledgePoint.getKnowledgePointId())
                            .pageIndex(sectionKnowledgePoint.getPageIndex())
                            .build();
                    sectionQuestionTypesMapper.insert(sectionQuestionType);
                }
            });

            mathKnowledgePointsMapper.realDeleteById(mathKnowledgePoint.getId());
            questionKnowledgePointsService.deleteAssociationsByKnowledgePointId(param.getKeyPointId());
            sectionKnowledgePointsMapper.delete(eq);
            kpQtRelationMapper.delete(Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                    .eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, param.getKeyPointId()));

        } else if (param.getType().equals("questionType")
                && (param.getTargetType().equals("knowledgePoint") || param.getTargetType().equals("examPoint"))){
            MathQuestionType mathQuestionType = mathQuestionTypesMapper.selectById(param.getKeyPointId());

            if (null == mathQuestionType) {
                throw new BusinessException("要切换题型不存在");
            }
            MathKnowledgePoint existMathKnowledgePoint = mathKnowledgePointsMapper.selectById(param.getKeyPointId());
            if (null != existMathKnowledgePoint) {
                throw new BusinessException("要切知识点已存在");
            }

            MathKnowledgePoint mathKnowledgePoint = MathKnowledgePoint.builder()
                    .id(mathQuestionType.getId())
                    .name(mathQuestionType.getName())
                    .examPoint(param.getTargetType().equals("examPoint") ? true : false)
                    .sortNo(mathQuestionType.getSortNo())
                    .build();
            mathKnowledgePointsMapper.insert(mathKnowledgePoint);

            List<UUID> questionIds = questionTypesMappingService.listQuestionIdsByQuestionTypeId(param.getKeyPointId());
            if (CollUtil.isNotEmpty(questionIds)) {
                List<QuestionKnowledgePoint> questionKnowledgePoints = questionIds.stream()
                        .filter(questionId -> {
                            List<QuestionKnowledgePoint> existQks = questionKnowledgePointsMapper.selectList(
                                    Wrappers.lambdaQuery(QuestionKnowledgePoint.class)
                                            .eq(QuestionKnowledgePoint::getQuestionId, questionId)
                                            .eq(QuestionKnowledgePoint::getKnowledgePointId, mathQuestionType.getId())
                            );
                            return CollUtil.isEmpty(existQks);
                        }).map(questionId -> {
                    return QuestionKnowledgePoint.builder()
                            .questionId(questionId)
                            .knowledgePointId(mathQuestionType.getId())
                            .build();
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(questionKnowledgePoints)) {
                    questionKnowledgePointsService.saveBatch(questionKnowledgePoints);
                }
            }

            LambdaQueryWrapper<SectionQuestionType> eq = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId());
            List<SectionQuestionType> sectionQuestionTypes = sectionQuestionTypesMapper.selectList(eq);
            sectionQuestionTypes.forEach(sectionQuestionType -> {
                List<SectionKnowledgePoint> sectionKnowledgePoints = sectionKnowledgePointsMapper.selectList(Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                        .eq(SectionKnowledgePoint::getSectionId, sectionQuestionType.getSectionId())
                        .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId()));
                if (CollUtil.isEmpty(sectionKnowledgePoints)) {
                    SectionKnowledgePoint sectionKnowledgePoint = SectionKnowledgePoint.builder()
                            .sectionId(sectionQuestionType.getSectionId())
                            .knowledgePointId(sectionQuestionType.getQuestionTypeId())
                            .pageIndex(sectionQuestionType.getPageIndex())
                            .build();
                    sectionKnowledgePointsMapper.insert(sectionKnowledgePoint);
                }
            });

            mathQuestionTypesMapper.realDeleteById(mathQuestionType.getId());
            questionTypesMappingService.deleteAssociationsByQuestionTypeId(param.getKeyPointId());
            sectionQuestionTypesMapper.delete(eq);
            kpQtRelationMapper.delete(Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                    .eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, param.getKeyPointId()));
        } else {
            boolean examPoint = param.getTargetType().equals("examPoint") ? true : false;
            mathKnowledgePointsMapper.updateById(MathKnowledgePoint.builder().id(param.getKeyPointId()).examPoint(examPoint).build());
        }
    }

    @Override
    @Transactional
    public UUID combineKeypoints(CombineKeypointParam param) {
        if (param.getType().equals("knowledgePoint")) {
            LambdaQueryWrapper<MathKnowledgePoint> eq1 = Wrappers.lambdaQuery(MathKnowledgePoint.class)
                    .eq(MathKnowledgePoint::getId, param.getKeyPointId1());
            MathKnowledgePoint mathKnowledgePoint1 = mathKnowledgePointsMapper.selectOne(eq1);
            if (null == mathKnowledgePoint1) {
                throw new BusinessException("要合并知识点1不存在");
            }
            LambdaQueryWrapper<MathKnowledgePoint> eq2 = Wrappers.lambdaQuery(MathKnowledgePoint.class)
                    .eq(MathKnowledgePoint::getId, param.getKeyPointId2());
            MathKnowledgePoint mathKnowledgePoint2 = mathKnowledgePointsMapper.selectOne(eq2);
            if (null == mathKnowledgePoint2) {
                throw new BusinessException("要合并知识点2不存在");
            }
            if (!mathKnowledgePoint1.getName().equals(mathKnowledgePoint2.getName())) {
                throw new BusinessException("要合并知识点1与知识点2名称不一致");
            }
            UUID remainId = getRemainKnowledgePointId(mathKnowledgePoint1, mathKnowledgePoint2);
            UUID removeId = param.getKeyPointId1().equals(remainId) ? param.getKeyPointId2() : param.getKeyPointId1();


            //修改知识点与题目的关系
            LambdaQueryWrapper<QuestionKnowledgePoint> questionKpWrapper = Wrappers.lambdaQuery(QuestionKnowledgePoint.class)
                    .eq(QuestionKnowledgePoint::getKnowledgePointId, removeId);
            List<QuestionKnowledgePoint> questionKnowledgePoints = questionKnowledgePointsMapper.selectList(questionKpWrapper);

            if (CollUtil.isNotEmpty(questionKnowledgePoints)) {
                questionKnowledgePoints.stream().forEach(questionKp -> {
                    List<QuestionKnowledgePoint> existRemainQuestionKnowledgePoints = questionKnowledgePointsMapper.selectList(Wrappers.lambdaQuery(QuestionKnowledgePoint.class)
                            .eq(QuestionKnowledgePoint::getQuestionId, questionKp.getQuestionId())
                            .eq(QuestionKnowledgePoint::getKnowledgePointId, remainId));
                    if (CollUtil.isEmpty(existRemainQuestionKnowledgePoints)) {
                        questionKnowledgePointsMapper.update(QuestionKnowledgePoint.builder()
                                .knowledgePointId(remainId)
                                .build(), questionKpWrapper.eq(QuestionKnowledgePoint::getQuestionId, questionKp.getQuestionId()));
                    } else {
                        questionKnowledgePointsMapper.delete(questionKpWrapper.eq(QuestionKnowledgePoint::getQuestionId, questionKp.getQuestionId()));
                    }
                });
            }

            //修改知识点与小节的关系
            LambdaQueryWrapper<SectionKnowledgePoint> sectionKpWrapper = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getKnowledgePointId, removeId);
            List<SectionKnowledgePoint> sectionKnowledgePoints = sectionKnowledgePointsMapper.selectList(sectionKpWrapper);

            sectionKnowledgePoints.forEach(sectionKp -> {
                List<SectionKnowledgePoint> existRemainSectionKnowledgePoints = sectionKnowledgePointsMapper.selectList(Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                                .eq(SectionKnowledgePoint::getSectionId, sectionKp.getSectionId())
                        .eq(SectionKnowledgePoint::getKnowledgePointId, remainId));
                if (CollUtil.isEmpty(existRemainSectionKnowledgePoints)) {
                    sectionKnowledgePointsMapper.update(SectionKnowledgePoint.builder()
                            .knowledgePointId(remainId)
                            .build(), sectionKpWrapper.eq(SectionKnowledgePoint::getSectionId, sectionKp.getSectionId()));
                } else {
                    sectionKnowledgePointsMapper.delete(sectionKpWrapper.eq(SectionKnowledgePoint::getSectionId, sectionKp.getSectionId())
                            );
                }
            });

            //修改知识点与题型的关系
            LambdaQueryWrapper<KnowledgePointQuestionTypeRelationship> kpQtWrapper = Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                    .eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, removeId);
            List<KnowledgePointQuestionTypeRelationship> kpQtRelationships = kpQtRelationMapper.selectList(kpQtWrapper);
            kpQtRelationships.forEach(kpQtRelationship -> {
                List<KnowledgePointQuestionTypeRelationship> existRemainKpQtRelationship = kpQtRelationMapper.selectList(Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                        .eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, kpQtRelationship.getQuestionTypeId())
                        .eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, remainId));
                if (CollUtil.isEmpty(existRemainKpQtRelationship)) {
                    kpQtRelationMapper.update(KnowledgePointQuestionTypeRelationship.builder()
                            .knowledgePointId(remainId)
                            .build(), kpQtWrapper.eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, kpQtRelationship.getQuestionTypeId()));
                } else {
                    kpQtRelationMapper.delete(kpQtWrapper.eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, kpQtRelationship.getQuestionTypeId()));
                }
            });

            //删除知识点
            mathKnowledgePointsMapper.realDeleteById(removeId);
            return remainId;
        } else {
            LambdaQueryWrapper<MathQuestionType> eq1 = Wrappers.lambdaQuery(MathQuestionType.class)
                    .eq(MathQuestionType::getId, param.getKeyPointId1());
            MathQuestionType mathQuestionType1 = mathQuestionTypesMapper.selectOne(eq1);
            if (null == mathQuestionType1) {
                throw new BusinessException("要合并题型1不存在");
            }
            LambdaQueryWrapper<MathQuestionType> eq2 = Wrappers.lambdaQuery(MathQuestionType.class)
                    .eq(MathQuestionType::getId, param.getKeyPointId2());
            MathQuestionType mathQuestionType2 = mathQuestionTypesMapper.selectOne(eq2);
            if (null == mathQuestionType2) {
                throw new BusinessException("要合并题型2不存在");
            }
            if (!mathQuestionType1.getName().equals(mathQuestionType2.getName())) {
                throw new BusinessException("要合并题型1与题型2名称不一致");
            }
            UUID remainId = mathQuestionType1.getId();
            UUID removeId = mathQuestionType2.getId();

            //修改题型与题目的关系
            LambdaQueryWrapper<QuestionTypesMapping> qtmWrapper = Wrappers.lambdaQuery(QuestionTypesMapping.class)
                    .eq(QuestionTypesMapping::getQuestionTypeId, removeId);
            List<QuestionTypesMapping> questionTypesMappings = questionTypesMappingMapper.selectList(qtmWrapper);
            if (CollUtil.isNotEmpty(questionTypesMappings)) {
                questionTypesMappings.stream().forEach(questionTypesMapping -> {
                    List<QuestionTypesMapping> existRemainQuestionTypesMappings = questionTypesMappingMapper.selectList(Wrappers.lambdaQuery(QuestionTypesMapping.class)
                            .eq(QuestionTypesMapping::getQuestionId, questionTypesMapping.getQuestionId())
                            .eq(QuestionTypesMapping::getQuestionTypeId, remainId));
                    if (CollUtil.isEmpty(existRemainQuestionTypesMappings)) {
                        questionTypesMappingMapper.update(QuestionTypesMapping.builder().questionTypeId(remainId).build(),
                                qtmWrapper.eq(QuestionTypesMapping::getQuestionId, questionTypesMapping.getQuestionId()));
                    } else {
                        questionTypesMappingMapper.delete(qtmWrapper.eq(QuestionTypesMapping::getQuestionId, questionTypesMapping.getQuestionId()));
                    }
                });
            }

            //修改题型与小节的关系
            LambdaQueryWrapper<SectionQuestionType> sectionQtWrapper = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getQuestionTypeId, removeId);
            List<SectionQuestionType> sectionKnowledgePoints = sectionQuestionTypesMapper.selectList(sectionQtWrapper);
            sectionKnowledgePoints.forEach(sectionKnowledgePoint -> {
                List<SectionQuestionType> existRemainSectionQts = sectionQuestionTypesMapper.selectList(Wrappers.lambdaQuery(SectionQuestionType.class)
                        .eq(SectionQuestionType::getSectionId, sectionKnowledgePoint.getSectionId())
                        .eq(SectionQuestionType::getQuestionTypeId, remainId));
                if (CollUtil.isEmpty(existRemainSectionQts)) {
                    sectionQuestionTypesMapper.update(SectionQuestionType.builder()
                            .questionTypeId(remainId)
                            .build(), sectionQtWrapper.eq(SectionQuestionType::getSectionId, sectionKnowledgePoint.getSectionId()));
                } else {
                    sectionQuestionTypesMapper.delete(sectionQtWrapper.eq(SectionQuestionType::getSectionId, sectionKnowledgePoint.getSectionId()));
                }
            });

            //修改知识点与题型的关系
            LambdaQueryWrapper<KnowledgePointQuestionTypeRelationship> kpQtWrapper = Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                    .eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, removeId);
            List<KnowledgePointQuestionTypeRelationship> kpQtRelationships = kpQtRelationMapper.selectList(kpQtWrapper);
            kpQtRelationships.forEach(kpQtRelationship -> {
                List<KnowledgePointQuestionTypeRelationship> existRemainKpQtRelationship = kpQtRelationMapper.selectList(Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                        .eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, kpQtRelationship.getKnowledgePointId())
                        .eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, remainId));
                if (CollUtil.isEmpty(existRemainKpQtRelationship)) {
                    kpQtRelationMapper.update(KnowledgePointQuestionTypeRelationship.builder()
                            .knowledgePointId(remainId)
                            .build(), kpQtWrapper.eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, kpQtRelationship.getKnowledgePointId()));
                } else {
                    kpQtRelationMapper.delete(kpQtWrapper.eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, kpQtRelationship.getKnowledgePointId()));
                }
            });

            //删除题型
            mathQuestionTypesMapper.realDeleteById(removeId);
            return remainId;
        }
    }

    @Override
    public void addKeypoints(AddSectionKeypointParam param) {
        if (param.getType().equals("knowledgePoint") || param.getType().equals("examPoint")) {
            sectionKnowledgePointsService.createRelation(param.getSectionId(), param.getKeyPointId(), param.getPageIndex());
        } else if (param.getType().equals("questionType")) {
            sectionQuestionTypesService.createRelation(param.getSectionId(), param.getKeyPointId(), param.getPageIndex());
        } else {
            throw new BusinessException("type参数错误");
        }
    }

    @Override
    public void seperateKnowledgePoints() {
        //查询出重复的知识点（一个知识点对应多个小节）
        List<SectionKnowledgePointPO> sectionKps = baseMapper.listDuplicateKnowledgePoints();
        //保留一个小节和知识点的关系，其他的新建同名知识点，删除其他小节与原知识点的关系，建立其他小节与新知识点的关系
        Map<UUID, List<SectionKnowledgePointPO>> collect =
                sectionKps.stream().collect(Collectors.groupingBy(SectionKnowledgePointPO::getId));

        //查询出知识点对应的题目关系，建立原知识点下面的题目与新知识点的关系
        List<UUID> kpIds = sectionKps.stream().map(SectionKnowledgePointPO::getId).distinct().toList();
        List<QuestionKnowledgePoint> questionKps = baseMapper.listBaseQuestionByKnowledgePointIds(kpIds);
        Map<UUID, List<QuestionKnowledgePoint>> questionKpMap = questionKps.stream().collect(Collectors.groupingBy(QuestionKnowledgePoint::getKnowledgePointId));

        //遍历collect
        for (Map.Entry<UUID, List<SectionKnowledgePointPO>> entry : collect.entrySet()) {
            List<SectionKnowledgePointPO> values = entry.getValue();
            if (CollUtil.isNotEmpty(values) && values.size() > 1) {
                SectionKnowledgePointPO remianPO = values.stream()
                        .filter(sectionKp -> sectionKp.getPublisher() == PublisherType.BEI_SHI_DA)
                        .findFirst()
                        .orElseGet(() -> values.get(0));
                values.stream().forEach(sectionKp -> {
                    if (sectionKp.getSectionId().equals(remianPO.getSectionId())) {
                        return;
                    }
                    dealDuplicateKnowledgePoint(remianPO, sectionKp, questionKpMap);

                });
            }
        }
    }

    @Override
    public void seperateQuestionTypes() {
        //查询出重复的题型（一个题型对应多个小节）
        List<SectionQuestionTypePO> sectionQts = baseMapper.listDuplicateQuestionTypes();
        //保留一个小节和题型的关系，其他的新建同名题型，删除其他小节与原题型的关系，建立其他小节与新题型的关系
        Map<UUID, List<SectionQuestionTypePO>> collect =
                sectionQts.stream().collect(Collectors.groupingBy(SectionQuestionTypePO::getId));

        //查询出题型对应的题目关系，建立原题型下面的题目与新题型的关系
        List<UUID> qtIds = sectionQts.stream().map(SectionQuestionTypePO::getId).distinct().toList();
        List<QuestionTypesMapping> questionQts = baseMapper.listBaseQuestionByQuestionTypeIds(qtIds);
        Map<UUID, List<QuestionTypesMapping>> questionQtMap = questionQts.stream().collect(Collectors.groupingBy(QuestionTypesMapping::getQuestionTypeId));

        //遍历collect
        for (Map.Entry<UUID, List<SectionQuestionTypePO>> entry : collect.entrySet()) {
            List<SectionQuestionTypePO> values = entry.getValue();
            if (CollUtil.isNotEmpty(values) && values.size() > 1) {
                SectionQuestionTypePO remianPO = values.stream()
                        .filter(sectionQt -> sectionQt.getPublisher() == PublisherType.BEI_SHI_DA)
                        .findFirst()
                        .orElseGet(() -> values.get(0));
                values.stream().forEach(sectionQt -> {
                    if (sectionQt.getSectionId().equals(remianPO.getSectionId())) {
                        return;
                    }
                    dealDuplicateQuestionType(remianPO, sectionQt, questionQtMap);
                });
            }
        }
    }

    @Override
    public void updatePageIndex(UpdateSectionKeypointParam param) {
        if (param.getType().equals("questionType")) {
            LambdaQueryWrapper<SectionQuestionType> queryWrapper = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getSectionId, param.getSectionId())
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId())
                    .eq(null != param.getOldPageIndex(), SectionQuestionType::getPageIndex, param.getOldPageIndex())
                    .isNull(null == param.getOldPageIndex(), SectionQuestionType::getPageIndex);
            List<SectionQuestionType> sectionQuestionTypes = sectionQuestionTypesMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(sectionQuestionTypes)) {
                throw new BusinessException("未找到该小节题型关系");
            }
            LambdaUpdateWrapper<SectionQuestionType> wrapper = Wrappers.lambdaUpdate(SectionQuestionType.class)
                    .eq(SectionQuestionType::getSectionId, param.getSectionId())
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId())
                    .eq(null != param.getOldPageIndex(), SectionQuestionType::getPageIndex, param.getOldPageIndex())
                    .isNull(null == param.getOldPageIndex(), SectionQuestionType::getPageIndex)
                    .set(SectionQuestionType::getPageIndex, param.getPageIndex());
            sectionQuestionTypesMapper.update(wrapper);
        } else if (param.getType().equals("knowledgePoint") || param.getType().equals("examPoint")) {
            LambdaQueryWrapper<SectionKnowledgePoint> queryWrapper = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getSectionId, param.getSectionId())
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId())
                    .eq(null != param.getOldPageIndex(), SectionKnowledgePoint::getKnowledgePointId, param.getOldPageIndex())
                    .isNull(null == param.getOldPageIndex(), SectionKnowledgePoint::getPageIndex);
            List<SectionKnowledgePoint> sectionKnowledgePoints = sectionKnowledgePointsMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(sectionKnowledgePoints)) {
                throw new BusinessException("未找到该小节知识点关系");
            }

            LambdaUpdateWrapper<SectionKnowledgePoint> wrapper = Wrappers.lambdaUpdate(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getSectionId, param.getSectionId())
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId())
                    .eq(null != param.getOldPageIndex(), SectionKnowledgePoint::getKnowledgePointId, param.getOldPageIndex())
                    .isNull(null == param.getOldPageIndex(), SectionKnowledgePoint::getPageIndex)
                    .set(SectionKnowledgePoint::getPageIndex, param.getPageIndex());
            sectionKnowledgePointsMapper.update(wrapper);
        }
    }

    @Override
    public List<SectionVO> listByTextbookId(UUID textbookId) {
        return baseMapper.listAllSectionsByBookId(textbookId);
    }

    @Override
    public List<MathSectionVO> list(String name, Integer grade, Integer semester, PublisherType publisher, String chapterName, UUID chapterId) {
        return baseMapper.list(name, grade, semester, publisher, chapterName, chapterId);
    }

    @Override
    public List<SectionVO> listAllsectionsByExamId(UUID examId, PublisherType publisher) {
        MathExam mathExam = mathExamsMapper.selectById(examId);
        if (null == mathExam) {
            throw new BusinessException("试卷不存在");
        }
        publisher = null != publisher ? publisher : mathExam.getPublisher();
        if (null == publisher) {
            throw new BusinessException("试卷未设置教材版本");
        }
        return baseMapper.listAllsectionsByPublisher(publisher);
    }

    @Override
    public List<SectionVO> listSections(Integer grade, PublisherType publisher, Integer semester) {
        List<SectionVO> sectionVOS = baseMapper.listSections(grade, semester, publisher);
        if (CollUtil.isEmpty(sectionVOS)) {
            return new ArrayList<>();
        }
        List<SectionKeypointCountPO> poList = baseMapper.listKnowledgePointCountAndQuestionTypeCount(sectionVOS.stream().map(SectionVO::getSectionId).toList());
        
        // Create a map for quick lookup of counts by sectionId
        Map<UUID, SectionKeypointCountPO> countMap = poList.stream()
                .collect(Collectors.toMap(
                        SectionKeypointCountPO::getSectionId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
                
        // Assign knowledgePointCount and questionTypeCount to each SectionVO
        sectionVOS.forEach(sectionVO -> {
            SectionKeypointCountPO countPO = countMap.get(sectionVO.getSectionId());
            if (countPO != null) {
                sectionVO.setKnowledgePointCount(countPO.getKnowledgePointCount());
                sectionVO.setQuestionTypeCount(countPO.getQuestionTypeCount());
            } else {
                sectionVO.setKnowledgePointCount(0);
                sectionVO.setQuestionTypeCount(0);
            }
        });
        return sectionVOS.stream()
                .filter(sectionVO -> sectionVO.getKnowledgePointCount() > 0 || sectionVO.getQuestionTypeCount() > 0)
                .sorted(Comparator.comparing(SectionVO::getTextbookId)
                        .thenComparing(SectionVO::getChapterSortNo)
                        .thenComparing(SectionVO::getSectionSortNo))
                .collect(Collectors.toList());
    }

    @Override
    public List<QuerySectionVideoVO> querySectionVideos(List<UUID> sectionIds) {

        List<SectionVideoPO> sectionVideoPOs = baseMapper.querySectionVideos(sectionIds);
        if (CollUtil.isNotEmpty(sectionVideoPOs)) {
            return sectionVideoPOs.stream()
                    .map(sectionVideoPO -> QuerySectionVideoVO.builder()
                            .ossKey(sectionVideoPO.getOssKey())
                            .ossEnum(OssEnum.ofTypeAndBucket(sectionVideoPO.getOssType(), sectionVideoPO.getOssBucket()))
                            .name(sectionVideoPO.getFileName())
                            .sectionId(sectionVideoPO.getSectionId())
                            .sortNo(sectionVideoPO.getSortNo())
                            .build())
                    .collect(Collectors.toList());
        }
        return List.of();
    }

    @Transactional(rollbackFor = Exception.class)
    protected void dealDuplicateKnowledgePoint(SectionKnowledgePointPO remianPO, SectionKnowledgePointPO sectionKp, Map<UUID, List<QuestionKnowledgePoint>> questionKpMap) {
        //新建新知识点
        MathKnowledgePoint newKp = BeanUtil.copyProperties(remianPO, MathKnowledgePoint.class);
        newKp.setId(UUID.randomUUID());
        newKp.setCreatedAt(new Date());
        newKp.setUpdatedAt(new Date());
        newKp.setIsBase(false);
        mathKnowledgePointsMapper.insert(newKp);

        //创建新知识点与小节的关系
        sectionKnowledgePointsService.createRelation(sectionKp.getSectionId(), newKp.getId(), sectionKp.getPageIndex());

        //删除旧知识点与小节的关系
        sectionKnowledgePointsService.removeEntity(sectionKp);

        //建立新知识点与题目的关系
        List<QuestionKnowledgePoint> questionKnowledgePoints = questionKpMap.get(sectionKp.getId());
        if (CollUtil.isNotEmpty(questionKnowledgePoints)) {
            List<QuestionKnowledgePoint> newQuestionKps = new ArrayList<>();
            questionKnowledgePoints.stream()
                    .distinct()
                    .forEach(questionKp -> {
                        newQuestionKps.add(QuestionKnowledgePoint.builder()
                                .questionId(questionKp.getQuestionId())
                                .knowledgePointId(newKp.getId())
                                .build()
                        );
                    });
            questionKnowledgePointsService.saveBatch(newQuestionKps);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    protected void dealDuplicateQuestionType(SectionQuestionTypePO remianPO, SectionQuestionTypePO sectionQt, Map<UUID, List<QuestionTypesMapping>> questionQtMap) {
        //新建新题型
        MathQuestionType newQt = BeanUtil.copyProperties(remianPO, MathQuestionType.class);
        newQt.setId(UUID.randomUUID());
        newQt.setCreatedAt(new Date());
        newQt.setUpdatedAt(new Date());
        newQt.setIsBase(false);
        mathQuestionTypesMapper.insert(newQt);

        //创建新题型与小节的关系
        sectionQuestionTypesService.createRelation(sectionQt.getSectionId(), newQt.getId(), sectionQt.getPageIndex());

        //删除旧题型与小节的关系
        sectionQuestionTypesService.removeEntity(sectionQt);

        //建立新题型与题目的关系
        List<QuestionTypesMapping> questionTypesMappings = questionQtMap.get(sectionQt.getId());
        if (CollUtil.isNotEmpty(questionTypesMappings)) {
            List<QuestionTypesMapping> newQuestionQts = new ArrayList<>();
            questionTypesMappings.stream()
                    .distinct()
                    .forEach(questionQt -> {
                        newQuestionQts.add(QuestionTypesMapping.builder()
                                .questionId(questionQt.getQuestionId())
                                .questionTypeId(newQt.getId())
                                .build()
                        );
                    });
            questionTypesMappingService.saveBatch(newQuestionQts);
        }
    }

    private UUID getRemainKnowledgePointId(MathKnowledgePoint mathKnowledgePoint1, MathKnowledgePoint mathKnowledgePoint2) {
        if (null != mathKnowledgePoint1.getExamPoint() && mathKnowledgePoint1.getExamPoint()) {
            return mathKnowledgePoint1.getId();
        }
        if (null != mathKnowledgePoint2.getExamPoint() && mathKnowledgePoint2.getExamPoint()) {
            return mathKnowledgePoint2.getId();
        }
        if (null != mathKnowledgePoint1.getExamPoint()) {
            return mathKnowledgePoint1.getId();
        }
        if (null != mathKnowledgePoint2.getExamPoint()) {
            return mathKnowledgePoint2.getId();
        }
        return mathKnowledgePoint1.getId();
    }
}
