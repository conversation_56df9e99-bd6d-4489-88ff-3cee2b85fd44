package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.config.base.BaseEntity;

import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.enums.Genre;
import com.joinus.knowledge.enums.ReadingQuestionType;
import com.joinus.knowledge.mapper.ReadingKnowledgePointsMapper;
import com.joinus.knowledge.mapper.ReadingPassagesMapper;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.param.ReadingPassageParam;
import com.joinus.knowledge.model.param.ReadingPassagesParam;
import com.joinus.knowledge.model.po.ClonePassageAndQuestionsPO;
import com.joinus.knowledge.model.vo.ReadingPassageQuestionsVO;
import com.joinus.knowledge.model.vo.ReadingPassageVO;
import com.joinus.knowledge.service.*;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.io.File;

@Slf4j
@Service
public class ReadingPassagesServiceImpl extends ServiceImpl<ReadingPassagesMapper, ReadingPassages> implements ReadingPassagesService {
    @Resource
    private AIChatService aiChatService;
    @Resource
    private ReadingKnowledgePointsService readingKnowledgePointsService;
    @Resource
    private ReadingPassageQuestionsService readingPassageQuestionsService;
    @Resource
    private ReadingQuestionAnswersService readingQuestionAnswersService;
    @Resource
    private ReadingQuestionKnowledgePointsService readingQuestionKnowledgePointsService;
    @Resource
    private ReadingUnitsService readingUnitsService;
    @Resource
    private ReadingUnitGenresService readingUnitGenresService;
    @Resource
    private ReadingKnowledgePointsMapper readingKnowledgePointsMapper;
    @Resource
    private ReadingPassageQuestionSetsService readingPassageQuestionSetsService;
    @Resource
    private ReadingPassageQuestionSetEntriesService readingPassageQuestionSetEntriesService;

    private static final ConcurrentHashMap<String, UUID> knowledgePointMap = new ConcurrentHashMap<>();

    private static final AIModelType r1 = AIModelType.JYSD_DEEPSEEK_R1;
    @Value("${dify.api.key.passage:app-mMpttVJwlQ4t02VnZAVO40jX}")
    private String createPassageAndQuestionDifyApiKey;
    @Value("${dify.api.key.passage.question:app-5a6UbWEIsAgdIFbbyZCHfjCZ}")
    private String createQuestionDifyApiKey;
    @Value("${dify.url:https://dify.ijx.icu/v1/workflows/run}")
    private String difyUrl;

    @PostConstruct
    public void init() {
        List<ReadingKnowledgePoints> knowledgePointsList = readingKnowledgePointsService.list();
        knowledgePointsList.forEach(knowledgePoints -> knowledgePointMap.put(knowledgePoints.getName(), knowledgePoints.getId()));
    }


    @Override
    public void batchCreateReadingPassages() {
        int createNum = 99;
        int grade = 4;
        int semester = 2;
        String unitName = "第一单元";
        String jsonSchema = """
                {
                  "type": "object",
                  "properties": {
                    "title": {
                      "type": "string"
                    },
                    "content": {
                      "type": "string"
                    }
                  },
                  "required": [
                    "title",
                    "content"
                  ]
                }
                """;
        List<ReadingUnits> readingUnitsList = readingUnitsService.list(Wrappers.<ReadingUnits>lambdaQuery()
                .eq(ReadingUnits::getGrade, grade)
                .eq(ReadingUnits::getSemester, semester)
                .eq(ReadingUnits::getName, unitName));
        if (CollectionUtil.isEmpty(readingUnitsList)) {
            return;
        }
        UUID unitId = readingUnitsList.get(0).getId();
        List<ReadingUnitGenres> readingUnitGenresList = readingUnitGenresService.list(Wrappers.<ReadingUnitGenres>lambdaQuery()
                .eq(ReadingUnitGenres::getUnitId, unitId));
        if (CollectionUtil.isEmpty(readingUnitGenresList)) {
            return;
        }

        ExecutorService executorService = Executors.newFixedThreadPool(100);

        String createPassagePromptTemplate = """
                你是一位经验丰富、专业严谨的语文教研老师，熟知四年级语文考试要求和阅读理解题型特点，能够精准地为四年级语文阅读理解出题。
                要求技能如下：
                1: 审核问题-在开始出题之前，仔细审核之前提出的问题，确保逻辑清晰、要求明确。
                2：知识点熟练-了解所有四年级下学期的考试要求，阅读理解的知识点，阅读答题公式。
                生成阅读题要求：
                1.请生成1篇适合%s%s孩子学习的阅读理解，阅读类型限%s，要求阅读正文400-700字左右。不用显示字数。
                2.要求添加标签：阅读类型、预计做题时间。例如：记叙文-10分钟
                3.标签放在正文开头。例如：记叙文-10分钟  正文内容
                4.生成的内容分为标题（title）和正文（content）两部分，不用生成题目和答案。
                5.请严格按照{"title": "","content": ""}格式返回结果;
                """;

        for (ReadingUnitGenres readingUnitGenres : readingUnitGenresList) {
            if (readingUnitGenres.getGenre().equals(Genre.CLASSIC_POETRY.name())) { //不用生成古诗文
                continue;
            }
            Genre genre = Genre.getByName(readingUnitGenres.getGenre());
            if (genre == null) {
                continue;
            }
            String gradeStr = grade + "年级";
            String semesterStr = semester == 1 ? "上学期" : "下学期";
            String createPassagePrompt = String.format(createPassagePromptTemplate, gradeStr, semesterStr, genre.getGenreName());
            for (int i = 0; i < createNum; i++) {
                int finalI = i;
                executorService.execute(() -> {
                    try {
                        OpenAiChatOptions options = new OpenAiChatOptions();
                        options.setTemperature(0.8);
                        String result = aiChatService.chatWithJsonSchema(createPassagePrompt, r1, options, jsonSchema);
                        JSONObject jsonObject = JSONUtil.parseObj(result);
                        log.info("第{}个生成阅读文章生成成功", finalI);
                        ReadingPassages passage = ReadingPassages.builder()
                                .source(r1.getModelName())
                                .content(jsonObject.getStr("content"))
                                .title(jsonObject.getStr("title"))
                                .unitId(unitId)
                                .genre(genre.name())
                                .build();
                        this.save(passage);
                        log.info("第{}个保存阅读段落成功", finalI);
                    } catch (Exception e) {
                        log.info("第{}个生成阅读段落失败", finalI, e);
                    }
                });

            }

            // 关闭线程池
            executorService.shutdown();
        }


    }

    @Override
    public void testListMsg() {
        String content = """
                 （阅读类型：散文｜预计用时：15分钟｜总分：10分）
                
                 《老屋的四季絮语》
                
                 外婆家的老屋檐角总是挂着铜铃铛，春风吹过时，"叮铃——"一声便惊醒了檐下筑巢的紫燕。斑驳的木门框上歪歪扭扭刻着表姐十五年前的身高线，青苔沿着门槛的裂缝爬上台阶，像给老屋系了条翡翠腰带。
                
                 谷雨时节，老屋成了蜻蜓的驿站。成群的红蜻蜓歇在晾衣绳上，把灰扑扑的麻绳点缀成流动的璎珞。我和表弟蹑手蹑脚蹲在竹帘后，看它们透明的翅膀在斜阳里忽闪着碎金。蝙蝠总在暮色将临时突然从阁楼窗缝钻出，翅膀掠过紫藤花架时，惊落几串沾着露水的花铃铛。
                
                 夏夜的老屋是天然的八音盒。纺织娘在丝瓜架下拨动月光弦，蛙鸣从荷塘捎来湿润的鼓点。外婆摇着蒲扇讲古，竹椅"吱呀吱呀"应和着故事里的虎啸龙吟。萤火虫提着灯笼在纱窗外游弋，把我们的影子投在糊着宣纸的格窗上，忽大忽小像皮影戏。
                
                 秋风扫过天井时，老屋便成了色彩博物馆。金桂香渗进木梁的每道年轮里，晒秋的玉米棒在廊下串成琥珀珠帘。我们踩着咯吱作响的竹梯摘柿子，熟透的果实"啪嗒"坠入稻草堆，溅起带着甜味的阳光碎屑。猫头鹰在古银杏上值守，它琉璃般的眼睛映着灶膛跃动的火苗。
                
                 落雪的老屋最像童话里的姜饼屋。冰棱给瓦当镶上水晶流苏，麻雀在覆雪的柴垛上跳出一串小竹叶。围炉烤橘子的香气裹着柴火噼啪声，把玻璃窗呵出毛茸茸的雾。外公戴着老花镜修补藤椅，篾条在他指间穿梭，编织着春燕归巢的旧梦。
                
                 墙皮剥落处露出层层叠叠的旧报纸，像老屋珍藏的日记本。墙角陶罐里，野菊与狗尾草年复一年更替着花束。每当炊烟袅袅升起，这座会呼吸的老屋便又翻开新的篇章，等待下一季的燕语呢喃。
                
                 （注：文中自然嵌入"屋檐""装饰""和谐""慰藉(jiè)""绮(qǐ)丽""锐利""闪烁""催眠曲""帐子""蝙蝠""猫头鹰"等单元重点词汇）
                """;
        HashMap<String, Object> result = analysisPassage(content);
        log.info("分析结果：{}", JSONUtil.toJsonStr(result));


    }

    public HashMap<String, Object> analysisPassage(String content) {
        HashMap<String, Object> result = new HashMap<>();
        String promptTemplate0 = """
                %s
                根据上面文章内容,请返回文章的标题，不带《》
                """;
        String prompt0 = String.format(promptTemplate0, content);
        String prompt1 = """
                根据上面文章内容，请回答以下问题：
                1.文章情节是是属于哪一类生活的（学校、家庭、友谊、自然等分类）
                2.文章结构和逻辑是否连贯跳跃
                3.给出对于三年级学生（9岁）有难度的词汇
                4.给出对于三年级学生（9岁）语法上有难度的句子
                5.给出文章里需要跨学科知识的地方
                6.文中是否有使用修辞手法，若有则给出
                """;
        String prompt2 = """
                请按照以下要求生成：
                1.从词汇角度来分析这篇文章的难度，假如 一年级的小朋友的难度为1，三年级小朋友适合阅读的难度为3，六年级的小朋友适合阅读的难度为6 ，给出这篇文章的难度值
                2.从句子角度来分析这篇文章的难度并给出难度值
                3.从情节与结构来分析这篇文章的难度并给出难度值
                4.从文章的思想深度（隐喻、暗示、讽刺等）来分析这篇文章难度并给出难度值
                5.从文章的篇幅长度来分析这篇文章难度并给出难度值
                6.从跨学科角度来分析这篇文章的难度并给出难度值
                7.从修辞角度来分析这篇文章的难度并给出难度值
                """;
        String prompt3 = "只返回综合难度评估的结果,用整数表示";

        Conversation conversation = new Conversation();
        OpenAiChatOptions options = new OpenAiChatOptions();
        options.setTemperature(0.8);
        String title = aiChatService.chat(prompt0, AIModelType.JYSD_DEEPSEEK_R1, options, conversation);
        options.setTemperature(0.8);
        String result1 = aiChatService.chat(prompt1, AIModelType.JYSD_DEEPSEEK_R1, options, conversation);
        options.setTemperature(0.6);
        String result2 = aiChatService.chat(prompt2, AIModelType.JYSD_DEEPSEEK_R1, options, conversation);
        options.setTemperature(0.2);
        String result3 = aiChatService.chat(prompt3, AIModelType.JYSD_DEEPSEEK_R1, options, conversation);
        result.put("title", title);
        result.put("result1", result1);
        result.put("result2", result2);
        result.put("result3", result3);
        result.put("prompt0", prompt0);
        result.put("prompt1", prompt1);
        result.put("prompt2", prompt2);
        result.put("prompt3", prompt3);

        return result;
    }

    public void createPassageQuestion(UUID passageId) {
        int base = 1;

        ReadingPassages readingPassages = this.getById(passageId);
        if (readingPassages == null) {
            return;
        }
        String content = readingPassages.getContent();
        String genre = "散文";
        String grade = "四年级";
        String semester = "下学期";
        String unit = "第一单元";
        String knowledgePoint = "内容概括、修辞手法、近义词/反义词、表现手法、中心思想、细节理解、词语理解、人称视角、情感表达、标点符号、文章意境、感悟启示";
        String keyKnowledgePoint = "情感表达";
        String questionType = "选择、填空、问答、解释词句、判断、仿写/改写句子、找中心句/关键句";
        String keyQuestionType = "找中心句/关键句";
        String[] questionTypeArray = questionType.split("、");
        List<String> questionTypeList = new ArrayList<>();
        for (String s : questionTypeArray) {
            int loop = base;
            if (keyQuestionType.contains(s)) {
                loop = 3 * base;
            }
            for (int i = 0; i < loop; i++) {
                questionTypeList.add(s);
            }
        }

        String creatFirstQuestionTemplate = """
                %s
                上述文章是一篇适合%s学生阅读的一篇%s，
                请根据上述文章并结合知识点，按照以下要求生成一道%s题；
                1.知识点包括：%s；
                2.生成题目(选择题的话需要包含选项)、答案、解析以及所包含的知识点;
                3.请严格按照{"question": "","answer": "","analysis": "",knowledgePoint:""}格式返回结果;
                """;
        List<JSONObject> list = new ArrayList<>();
        OpenAiChatOptions options = new OpenAiChatOptions();
        options.setTemperature(0.8);
        //设置返回格式个JSON
        String jsonSchema = """
                {
                  "type": "object",
                  "properties": {
                    "question": {
                      "type": "string"
                    },
                    "answer": {
                      "type": "string"
                    },
                    "analysis": {
                      "type": "string"
                    },
                    "knowledgePoint": {
                      "type": "array",
                      "items": {
                        "type": "string"
                      }
                    }
                  },
                  "required": [
                    "question",
                    "answer",
                    "analysis",
                    "knowledgePoint"
                  ]
                }
                """;
        ExecutorService executorService = Executors.newFixedThreadPool(20);
        List<CompletableFuture<Void>> futures = questionTypeList.stream()
                .map(qt -> CompletableFuture.runAsync(() -> {
                    try {
                        String prompt = String.format(creatFirstQuestionTemplate,
                                content, grade, genre,
                                qt, knowledgePoint);
                        String result = aiChatService.chatWithJsonSchema(prompt, r1, options, jsonSchema);
                        JSONObject json = JSONUtil.parseObj(result);
                        List<UUID> uuidsList = new ArrayList<>();
                        JSONArray knowledgePointJSONArray = json.getJSONArray("knowledgePoint");
                        for (int i = 0; i < knowledgePointJSONArray.size(); i++) {
                            String item = knowledgePointJSONArray.getStr(i);
                            UUID uuid = null;
                            if (knowledgePointMap.containsKey(item)) {
                                uuid = knowledgePointMap.get(item);
                            } else {
                                List<ReadingKnowledgePoints> knowledgePointsList = readingKnowledgePointsService.list(Wrappers.<ReadingKnowledgePoints>lambdaQuery()
                                        .eq(ReadingKnowledgePoints::getName, item));
                                if (CollectionUtil.isNotEmpty(knowledgePointsList)) {
                                    uuid = knowledgePointsList.get(0).getId();
                                    knowledgePointMap.put(item, uuid);
                                }
                            }
                            if (uuid != null) {
                                uuidsList.add(uuid);
                            }
                        }
                        json.set("knowledgeUuids", uuidsList);
                        json.set("questionType", ReadingQuestionType.getByType(qt).name());
                        list.add(json);
                    } catch (Exception e) {
                        log.error("生成题目异常 [题型:{}]：{}", qt, e.getMessage());
                    }

                }, executorService)).toList();
        //等待所有线程执行完毕
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .exceptionally(ex -> {
                    log.error("批量任务执行异常：{}", ex.getMessage());
                    return null;
                })
                .join();  // 统一等待所有任务完成
        executorService.shutdown();
        log.info("生成题目结果:{}", JSONUtil.toJsonStr(list));
        List<ReadingPassageQuestions> readingPassageQuestionsList = new ArrayList<>();
        List<ReadingQuestionAnswers> readingQuestionAnswersList = new ArrayList<>();
        List<ReadingQuestionKnowledgePoints> readingQuestionKnowledgePointsList = new ArrayList<>();
        list.forEach((JSONObject json) -> {
            String question = json.getStr("question");
            String answer = json.getStr("answer");
            String analysis = json.getStr("analysis");
            String singleQuestionType = json.getStr("questionType");
            List<UUID> knowledgeUuidsList = json.getBeanList("knowledgeUuids", UUID.class);

            if (StrUtil.isNotBlank(question) && StrUtil.isNotBlank(answer) && StrUtil.isNotBlank(analysis)) {
                UUID questionUuid = UUID.randomUUID();
                ReadingPassageQuestions readingPassageQuestion = ReadingPassageQuestions.builder()
                        .id(questionUuid)
                        .passageId(passageId)
                        .content(null)
                        .questionType(singleQuestionType)
                        .source(r1.getModelName())
                        .build();
                readingPassageQuestionsList.add(readingPassageQuestion);

                ReadingQuestionAnswers readingQuestionAnswers = ReadingQuestionAnswers.builder()
                        .questionId(questionUuid)
                        .answer(null)
                        .content(analysis)
                        .build();
                readingQuestionAnswersList.add(readingQuestionAnswers);

                for (UUID item : knowledgeUuidsList) {
                    ReadingQuestionKnowledgePoints readingQuestionKnowledgePoints = ReadingQuestionKnowledgePoints.builder()
                            .knowledgePointId(item)
                            .questionId(questionUuid)
                            .build();
                    readingQuestionKnowledgePointsList.add(readingQuestionKnowledgePoints);
                }

            }
        });

        if (CollectionUtil.isNotEmpty(readingPassageQuestionsList)) {
            readingPassageQuestionsService.saveBatch(readingPassageQuestionsList);
            if (CollectionUtil.isNotEmpty(readingQuestionAnswersList)) {
                readingQuestionAnswersService.saveBatch(readingQuestionAnswersList);
            }
            if (CollectionUtil.isNotEmpty(readingQuestionKnowledgePointsList)) {
                readingQuestionKnowledgePointsService.saveBatch(readingQuestionKnowledgePointsList);
            }
        }
        log.info("操作完成");

    }


    @Override
    public ReadingPassageVO getPassageAndQuestions(ReadingPassageParam param) {
        ReadingPassageVO passageVO = new ReadingPassageVO();
        ReadingPassages passages = null;
        if (ObjectUtil.isNull(param.getPassageId())) {
            //根据单元id随机查询一个阅读文章
            Assert.notEmpty(param.getUnitId().toString(), "单元id不能为空");
            passages = baseMapper.getByUnitId(param);
        } else {
            passages = this.query(param.getPassageId());
        }
        if (passages != null) {
            BeanUtil.copyProperties(passages, passageVO);
            Genre genreName = Genre.getByName(passages.getGenre());
            passageVO.setGenreName(null != genreName ? genreName.getGenreName() : "");
            //根据文章id 薄弱知识点 和已经做的题目id 随机5道题目
            param.setPassageId(passages.getId());
            param.setCount(5);
            List<ReadingPassageQuestionsVO> questions = readingPassageQuestionsService.getQuestions(param);
            // 如果生成的题目不足5道，则取消薄弱知识点限制，补充至5道
            if (questions.size() < 5) {
                param.setKnowledgePoints(null);
                List<UUID> questionIds = param.getQuestionIds();
                questionIds.addAll(questions.stream().map(ReadingPassageQuestionsVO::getId).toList());
                param.setQuestionIds(questionIds);
                param.setCount(5 - questions.size());
                questions.addAll(readingPassageQuestionsService.getQuestions(param));
            }
            if (CollectionUtil.isEmpty(questions)) {
                return null;
            }
            questions.forEach(item -> {
                ReadingQuestionType questionType = ReadingQuestionType.getByName(item.getQuestionType());
                item.setQuestionType(null != questionType ? questionType.getType() : "");
                if (StrUtil.isNotBlank(item.getOptionStr())) {
                    List<String> list = JSONUtil.toList(JSONUtil.parseArray(item.getOptionStr()), String.class).stream().filter(StrUtil::isNotBlank).toList();
                    item.setOptions(list);
                }
            });
            passageVO.setQuestions(questions);
            return passageVO;
        }
        return null;
    }

    /**
     * 批量创建阅读文章和对应的问题
     *
     * @param grade 年级（1-一年级，以此类推）
     * @param semester 学期（1-上学期，2-下学期）
     * @param unit 单元名称
     * @param num 每种文体需要生成的文章数量，默认为5
     */
    public void batchCreatePassageAndQuestion(Integer grade, Integer semester, String unit, Integer num) {
        // 参数验证
        validateBatchCreateParams(grade, semester, unit);

        // 格式化年级和学期字符串
        String gradeStr = grade + "年级";
        String semesterStr = semester == 1 ? "上学期" : "下学期";

        // 获取单元信息
        ReadingUnits readingUnit = getReadingUnit(grade, semester, unit);

        // 获取单元对应的文体列表
        List<ReadingUnitGenres> readingUnitGenresList = getReadingUnitGenres(readingUnit.getId());

        // 设置默认生成数量
        int generateNum = num == null ? 5 : num;

        // 创建线程池并执行生成任务
        executeGenerationTasks(gradeStr, semesterStr, unit, readingUnit, readingUnitGenresList, generateNum);
    }

    /**
     * 验证批量创建参数
     */
    public void validateBatchCreateParams(Integer grade, Integer semester, String unit) {
        Assert.isTrue(grade != null && grade > 0, "年级参数错误，必须大于0");
        Assert.isTrue(semester != null && (semester == 1 || semester == 2), "学期参数错误，必须为1(上学期)或2(下学期)");
        Assert.isTrue(StrUtil.isNotBlank(unit), "单元名称不能为空");
    }

    /**
     * 获取指定年级、学期和单元名称的单元信息
     */
    public ReadingUnits getReadingUnit(Integer grade, Integer semester, String unit) {
        List<ReadingUnits> readingUnitsList = readingUnitsService.list(Wrappers.<ReadingUnits>lambdaQuery()
                .eq(ReadingUnits::getGrade, grade)
                .eq(ReadingUnits::getSemester, semester)
                .eq(ReadingUnits::getName, unit));
        Assert.isTrue(CollectionUtil.isNotEmpty(readingUnitsList), "未找到对应的单元信息");
        return readingUnitsList.getFirst();
    }

    /**
     * 获取单元对应的文体列表
     */
    public List<ReadingUnitGenres> getReadingUnitGenres(UUID unitId) {
        List<ReadingUnitGenres> readingUnitGenresList = readingUnitGenresService.list(Wrappers.<ReadingUnitGenres>lambdaQuery()
                .eq(ReadingUnitGenres::getUnitId, unitId));
        Assert.isTrue(CollectionUtil.isNotEmpty(readingUnitGenresList), "未找到单元对应的文体信息");
        return readingUnitGenresList;
    }

    /**
     * 执行生成任务
     */
    public void executeGenerationTasks(String gradeStr, String semesterStr, String unit,
                                      ReadingUnits readingUnit, List<ReadingUnitGenres> readingUnitGenresList, int generateNum) {
        ExecutorService executorService = Executors.newFixedThreadPool(50);
        try {
            for (ReadingUnitGenres readingUnitGenre : readingUnitGenresList) {
                // 跳过古诗古文阅读和名著阅读
                if (readingUnitGenre.getGenre().equals(Genre.CLASSIC_POETRY.name())
                        || readingUnitGenre.getGenre().equals(Genre.CLASSIC.name())) {
                    log.info("跳过{}的生成", readingUnitGenre.getGenre());
                    continue;
                }

                Genre genre = Genre.getByName(readingUnitGenre.getGenre());
                if (genre == null) {
                    log.warn("未知的文体类型: {}", readingUnitGenre.getGenre());
                    continue;
                }

                log.info("开始生成文体[{}]的文章，数量: {}", genre.getGenreName(), generateNum);
                for (int i = 0; i < generateNum; i++) {
                    final int index = i;
                    executorService.execute(() -> {
                        try {
                            log.info("开始生成第{}个[{}]文章", index + 1, genre.getGenreName());
                            createPassageAndQuestion(gradeStr, semesterStr, unit, readingUnit, genre, readingUnitGenre);
                        } catch (Exception e) {
                            log.error("生成第{}个[{}]文章失败: {}", index + 1, genre.getGenreName(), e.getMessage(), e);
                        }
                    });
                }
            }
        } finally {
            // 关闭线程池
            executorService.shutdown();
            log.info("所有生成任务已提交，线程池已关闭");
        }
    }

    /**
     * 创建单篇阅读文章和对应的问题
     *
     * @param gradeStr 年级字符串，如"四年级"
     * @param semesterStr 学期字符串，如"上学期"或"下学期"
     * @param unit 单元名称
     * @param readingUnit 单元实体对象
     * @param genre 文体类型
     * @param readingUnitGenre 单元文体实体对象
     */
    public void createPassageAndQuestion(String gradeStr, String semesterStr, String unit,
                                         ReadingUnits readingUnit, Genre genre, ReadingUnitGenres readingUnitGenre) {
        try {
            // 随机选择一个单词作为生成提示
            String randomWord = selectRandomWord(readingUnit);

            // 调用API生成文章和问题
            JSONObject inputs = new JSONObject();
            inputs.set("grade", gradeStr);
            inputs.set("semester", semesterStr);
            inputs.set("outline", readingUnit.getOutline());
            inputs.set("random_word", randomWord);
            inputs.set("unit", unit);
            inputs.set("genre", genre.getGenreName());
            inputs.set("min_word_num", readingUnitGenre.getMinWordNum());
            inputs.set("max_word_num", readingUnitGenre.getMaxWordNum());
            inputs.set("knowledge_point_question_type", readingUnitGenre.getKnowledgePointQuestionType());

            JSONObject responseJson = callDifyApiToGenerateContent(inputs, createPassageAndQuestionDifyApiKey);

            // 解析API返回结果
            String title = responseJson.getStr("title");
            String content = responseJson.getStr("content");

            if (responseJson.containsKey("questions")) {
                String source = responseJson.getStr("source");
                JSONArray questions = responseJson.getJSONArray("questions");
                // 创建文章实体
                UUID passageId = UUID.randomUUID();
                ReadingPassages readingPassage = createReadingPassage(passageId, readingUnit.getId(), title, content, genre, source);

                // 处理问题和答案
                List<ReadingPassageQuestions> questionsList = new ArrayList<>();
                List<ReadingQuestionAnswers> answersList = new ArrayList<>();
                List<ReadingQuestionKnowledgePoints> knowledgePointsList = new ArrayList<>();

                // 解析问题数据
                processQuestions(questions, passageId, questionsList, answersList, knowledgePointsList);

                // 保存数据到数据库
                savePassageAndQuestions(readingPassage, questionsList, answersList, knowledgePointsList);

                log.info("成功创建文章[{}]及{}个问题", title, questionsList.size());
            }

        } catch (Exception e) {
            log.error("创建文章和问题失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从单元词汇中随机选择一个单词
     */
    public String selectRandomWord(ReadingUnits readingUnit) {
        if (StrUtil.isNotBlank(readingUnit.getWords())) {
            String[] wordsArray = readingUnit.getWords().split(" ");
            int randomIndex = new java.util.Random().nextInt(wordsArray.length);
            return wordsArray[randomIndex];
        } else {
            //随机返回一个汉字
            return "";
        }

    }


    /**
     * 创建阅读文章实体
     */
    public ReadingPassages createReadingPassage(UUID passageId, UUID unitId, String title, String content, Genre genre, String source) {
        return ReadingPassages.builder()
                .id(passageId)
                .unitId(unitId)
                .title(title)
                .genre(genre.name())
                .content(content)
                .source(StrUtil.isBlank(source) ? AIModelType.JYSD_DEEPSEEK_R1.getModelName() : source)
                .build();
    }

    /**
     * 处理问题数据
     */
    public void processQuestions(JSONArray questions, UUID passageId,
                                List<ReadingPassageQuestions> questionsList,
                                List<ReadingQuestionAnswers> answersList,
                                List<ReadingQuestionKnowledgePoints> knowledgePointsList) {
        for (Object questionObj : questions) {
            JSONObject questionJson = JSONUtil.parseObj(questionObj);
            Set<String> keys = questionJson.keySet();
            String questionKey = keys.stream().findFirst().orElse(null);

            if (questionKey == null) {
                log.warn("跳过无效问题数据");
                continue;
            }

            // 根据问题类型处理数据
            processQuestionByType(questionKey, questionJson, passageId, questionsList, answersList, knowledgePointsList);
        }
    }

    /**
     * 根据问题类型处理问题数据
     */
    public void processQuestionByType(String questionKey, JSONObject questionJson, UUID passageId,
                                     List<ReadingPassageQuestions> questionsList,
                                     List<ReadingQuestionAnswers> answersList,
                                     List<ReadingQuestionKnowledgePoints> knowledgePointsList) {
        String questionType;
        JSONObject questionDetail;
        JSONObject questionContent = new JSONObject();
        JSONObject questionAnswer = new JSONObject();

        // 根据问题类型处理
        switch (questionKey) {
            case "选择":
                questionDetail = JSONUtil.parseObj(questionJson.getStr("选择"));
                questionType = ReadingQuestionType.CHOICE.name();
                questionContent.set("选项", questionDetail.getJSONArray("选项"));
                questionAnswer.set("答案", questionDetail.get("答案"));
                break;
            case "填空":
                questionDetail = JSONUtil.parseObj(questionJson.getStr("填空"));
                questionType = ReadingQuestionType.FILL_IN_THE_BLANK.name();
                questionAnswer.set("答案", questionDetail.get("clozeList"));
                break;
            case "其它":
                questionDetail = JSONUtil.parseObj(questionJson.getStr("其它"));
                questionType = ReadingQuestionType.getByType(questionDetail.getStr("题型")).name();
                questionAnswer.set("答案", questionDetail.get("答案"));
                break;
            default:
                log.warn("未知的问题类型: {}", questionKey);
                return;
        }

        // 设置问题内容
        questionContent.set("问题", questionDetail.get("问题"));

        // 创建问题实体
        UUID questionId = UUID.randomUUID();
        ReadingPassageQuestions question = ReadingPassageQuestions.builder()
                .id(questionId)
                .passageId(passageId)
                .questionType(questionType)
                .content(JSONUtil.toJsonStr(questionContent))
                .build();
        questionsList.add(question);

        // 创建答案实体
        ReadingQuestionAnswers answer = ReadingQuestionAnswers.builder()
                .questionId(questionId)
                .answer(JSONUtil.toJsonStr(questionAnswer))
                .content(questionDetail.getStr("解析"))
                .build();
        answersList.add(answer);

        // 处理知识点
        processKnowledgePoints(questionDetail.getJSONArray("知识点"), questionId, knowledgePointsList);
    }

    /**
     * 处理问题的知识点
     */
    public void processKnowledgePoints(JSONArray knowledgeArray, UUID questionId,
                                      List<ReadingQuestionKnowledgePoints> knowledgePointsList) {
        for (int i = 0; i < knowledgeArray.size(); i++) {
            String knowledgePointName = knowledgeArray.getStr(i);
            UUID knowledgePointId = getKnowledgePointId(knowledgePointName);

            if (knowledgePointId != null) {
                ReadingQuestionKnowledgePoints knowledgePoint = ReadingQuestionKnowledgePoints.builder()
                        .questionId(questionId)
                        .knowledgePointId(knowledgePointId)
                        .build();
                knowledgePointsList.add(knowledgePoint);
            }
        }
    }

    /**
     * 获取知识点ID，如果缓存中没有则从数据库查询
     */
    public UUID getKnowledgePointId(String knowledgePointName) {
        if (knowledgePointMap.containsKey(knowledgePointName)) {
            return knowledgePointMap.get(knowledgePointName);
        } else {
            List<ReadingKnowledgePoints> knowledgePointsList = readingKnowledgePointsService.list(
                    Wrappers.<ReadingKnowledgePoints>lambdaQuery()
                            .eq(ReadingKnowledgePoints::getName, knowledgePointName));
            if (CollectionUtil.isEmpty(knowledgePointsList)) {
                log.info("未找到精确匹配的知识点，尝试模糊匹配: {}", knowledgePointName);
                knowledgePointsList = readingKnowledgePointsService.list(
                        Wrappers.<ReadingKnowledgePoints>lambdaQuery()
                                .like(ReadingKnowledgePoints::getName, knowledgePointName));
            }

            if (CollectionUtil.isNotEmpty(knowledgePointsList)) {
                UUID id = knowledgePointsList.getFirst().getId();
                knowledgePointMap.put(knowledgePointName, id);
                return id;
            } else {
                log.info("未找到对应的知识点: {}", knowledgePointName);
//                ReadingKnowledgePoints readingKnowledgePoints = ReadingKnowledgePoints.builder()
//                        .name(knowledgePointName)
//                        .content("AI")
//                        .build();
//                readingKnowledgePointsService.save(readingKnowledgePoints);
//                knowledgePointMap.put(knowledgePointName, readingKnowledgePoints.getId());
//                return readingKnowledgePoints.getId();
                return null;
            }
        }
    }

    /**
     * 保存文章和问题数据到数据库
     */
    public void savePassageAndQuestions(ReadingPassages passage,
                                        List<ReadingPassageQuestions> questionsList,
                                        List<ReadingQuestionAnswers> answersList,
                                        List<ReadingQuestionKnowledgePoints> knowledgePointsList) {
        savePassageAndQuestions(passage,questionsList,answersList,knowledgePointsList,true);

    }

    public void savePassageAndQuestions(ReadingPassages passage,
                                        List<ReadingPassageQuestions> questionsList,
                                        List<ReadingQuestionAnswers> answersList,
                                        List<ReadingQuestionKnowledgePoints> knowledgePointsList,
                                        Boolean savePassageFlag) {
        if (CollectionUtil.isEmpty(questionsList)) {
            log.warn("没有有效的问题数据，跳过保存");
            return;
        }

        // 保存文章
        if (savePassageFlag) {
            this.save(passage);
            log.info("保存文章成功: {}", passage.getTitle());
        }

        // 保存问题
        readingPassageQuestionsService.saveBatch(questionsList);
        log.info("保存{}个问题成功", questionsList.size());

        // 保存答案
        if (CollectionUtil.isNotEmpty(answersList)) {
            readingQuestionAnswersService.saveBatch(answersList);
            log.info("保存{}个答案成功", answersList.size());
        }

        // 保存知识点关联
        if (CollectionUtil.isNotEmpty(knowledgePointsList)) {
            readingQuestionKnowledgePointsService.saveBatch(knowledgePointsList);
            log.info("保存{}个知识点关联成功", knowledgePointsList.size());
        }

    }



    @Override
    public void batchCreateQuestion(Integer grade,Integer semester,String unit,String genreName, UUID passageId, Integer num) {
        // 参数验证
        validateBatchCreateParams(grade, semester, unit);

        ReadingUnits readingUnit = getReadingUnit(grade, semester, unit);

        int finalNum = num == null ? 1 : num;
        LambdaQueryWrapper<ReadingPassages> queryWrapper = Wrappers.<ReadingPassages>lambdaQuery()
                .eq(ReadingPassages::getUnitId, readingUnit.getId());
        if (StrUtil.isNotBlank(genreName)) {
            queryWrapper.eq(ReadingPassages::getGenre, genreName);
        }
        if (passageId != null) {
            queryWrapper.eq(ReadingPassages::getId, passageId);
        }
        List<ReadingPassages> readingPassagesList = this.list(queryWrapper);
        List<ReadingUnitGenres> readingUnitGenresList = getReadingUnitGenres(readingUnit.getId());
        Map<String, List<ReadingUnitGenres>> collect = readingUnitGenresList.stream().collect(Collectors.groupingBy(ReadingUnitGenres::getGenre));

        // 格式化年级和学期字符串
        String gradeStr = grade + "年级";

        ExecutorService executorService = Executors.newFixedThreadPool(100);
        try {
            for (ReadingPassages readingPassage : readingPassagesList) {
                String passageGenre = readingPassage.getGenre();
                List<ReadingUnitGenres> readingUnitGenresListByGenre = collect.get(passageGenre);
                if (CollectionUtil.isEmpty(readingUnitGenresListByGenre)) {
                    continue;
                }
                ReadingUnitGenres readingUnitGenre = readingUnitGenresListByGenre.getFirst();
                log.info("开始生成文章[{}]的题目，重复调用dify api次数: {}", readingPassage.getTitle(), finalNum);

                JSONObject inputs = new JSONObject();
                inputs.set("grade", gradeStr);
                inputs.set("title", readingPassage.getTitle());
                inputs.set("content", readingPassage.getContent());
                inputs.set("genre", Genre.getByName(readingUnitGenre.getGenre()).getGenreName());
                inputs.set("num", finalNum);
                inputs.set("knowledge_point_question_type", readingUnitGenre.getKnowledgePointQuestionType());

                // 处理问题和答案
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                ConcurrentLinkedQueue<ReadingPassageQuestions> questionsList = new ConcurrentLinkedQueue<>();
                ConcurrentLinkedQueue<ReadingQuestionAnswers> answersList = new ConcurrentLinkedQueue<>();
                ConcurrentLinkedQueue<ReadingQuestionKnowledgePoints> knowledgePointsList = new ConcurrentLinkedQueue<>();

                for (int i = 0; i < finalNum; i++) {
                    // 使用CompletableFuture而不是直接提交任务到线程池
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            // 调用dify api
                            JSONObject textJson = callDifyApiToGenerateContent(inputs, createQuestionDifyApiKey);
                            JSONArray questions = textJson.getJSONArray("questions");

                            // 解析问题数据
                            // 使用线程安全的集合存储结果
                            List<ReadingPassageQuestions> threadQuestions = new ArrayList<>();
                            List<ReadingQuestionAnswers> threadAnswers = new ArrayList<>();
                            List<ReadingQuestionKnowledgePoints> threadKnowledgePoints = new ArrayList<>();

                            processQuestions(questions, readingPassage.getId(), threadQuestions, threadAnswers, threadKnowledgePoints);

                            // 将线程处理的结果添加到共享集合
                            questionsList.addAll(threadQuestions);
                            answersList.addAll(threadAnswers);
                            knowledgePointsList.addAll(threadKnowledgePoints);

                            log.info("成功生成【{}】一组题目，数量: {}", readingPassage.getTitle(), threadQuestions.size());
                        } catch (Exception e) {
                            log.error("调用dify api生成【{}】一组题目失败: {}", readingPassage.getTitle(), e.getMessage(), e);
                        }
                    }, executorService);

                    futures.add(future);
                }

                // 等待所有任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .exceptionally(ex -> {
                            log.error("批量生成题目任务异常: {}", ex.getMessage(), ex);
                            return null;
                        })
                        .join();

                // 所有任务完成后再保存数据
                List<ReadingPassageQuestions> finalQuestionsList = new ArrayList<>(questionsList);
                List<ReadingQuestionAnswers> finalAnswersList = new ArrayList<>(answersList);
                List<ReadingQuestionKnowledgePoints> finalKnowledgePointsList = new ArrayList<>(knowledgePointsList);

                // 保存数据到数据库(不保存文章)
                savePassageAndQuestions(readingPassage, finalQuestionsList, finalAnswersList, finalKnowledgePointsList, false);
                log.info("成功创建文章[{}]的{}个题目", readingPassage.getTitle(), finalQuestionsList.size());

            }
        } finally {
            // 关闭线程池
            executorService.shutdown();
        }

    }

    /**
     * 调用Dify API生成问题
     */
    public JSONObject callDifyApiToGenerateContent(JSONObject inputs, String apiKey) {
        // 构建请求参数
        JSONObject body = new JSONObject();
        body.set("inputs", inputs);
        body.set("response_mode", "blocking");
        body.set("user", "yuiop");

        // 发送HTTP请求
        String result = HttpUtil.createPost(difyUrl)
                .header("Authorization", String.format("Bearer %s", apiKey))
                .header("Content-Type", "application/json")
                .body(JSONUtil.toJsonStr(body))
                .execute()
                .body();

        log.debug("API返回结果: {}", result);

        // 解析返回结果
        JSONObject resultJson = JSONUtil.parseObj(result);
        JSONObject dataJson = resultJson.getJSONObject("data");
        JSONObject outputsJson = dataJson.getJSONObject("outputs");
        JSONObject textJson = outputsJson.getJSONObject("text");

        log.debug("解析后的文本内容: {}", textJson);
        return textJson;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassages> updateWrapper = Wrappers.lambdaUpdate(ReadingPassages.class)
                .set(BaseEntity::getDeletedAt, LocalDateTime.now())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ReadingPassagesParam param) {
        ReadingPassages readingPassages = BeanUtil.copyProperties(param, ReadingPassages.class);
        readingPassages.setUpdatedAt(new Date());
        this.updateById(readingPassages);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassages> updateWrapper = Wrappers.lambdaUpdate(ReadingPassages.class)
                .set(ReadingPassages::getIsEnabled, 1)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disable(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassages> updateWrapper = Wrappers.lambdaUpdate(ReadingPassages.class)
                .set(ReadingPassages::getIsEnabled, 0)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    public ReadingPassages query(UUID id) {
        LambdaQueryWrapper<ReadingPassages> wrapper = Wrappers.lambdaQuery(ReadingPassages.class)
                .eq(ReadingPassages::getId, id)
                .eq(ReadingPassages::getIsEnabled, 1)
                .eq(ReadingPassages::getIsAudit, 1)
                .isNull(ReadingPassages::getDeletedAt);
        return this.getOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditPass(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassages> updateWrapper = Wrappers.lambdaUpdate(ReadingPassages.class)
                .set(ReadingPassages::getIsAudit, 1)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditNoPass(List<UUID> idList) {
        LambdaUpdateWrapper<ReadingPassages> updateWrapper = Wrappers.lambdaUpdate(ReadingPassages.class)
                .set(ReadingPassages::getIsAudit, 2)
                .set(BaseEntity::getUpdatedAt, new Date())
                .in(BaseEntity::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    public void dealClonePassageAndQuestions() {
        List<ClonePassageAndQuestionsPO> clonePassageAndQuestionsList = baseMapper.getClonePassageAndQuestions();

        clonePassageAndQuestionsList.stream().collect(Collectors.groupingBy(ClonePassageAndQuestionsPO::getId)).forEach((k,v)-> {
            ClonePassageAndQuestionsPO firstItem = v.getFirst();
            List<ReadingPassageQuestionSets> readingPassageQuestionSetsList = new ArrayList<>();
            List<ReadingPassageQuestions> readingPassageQuestionsList = new ArrayList<>();
            List<ReadingQuestionAnswers> readingQuestionAnswersList = new ArrayList<>();
            List<ReadingQuestionKnowledgePoints> readingQuestionKnowledgePointsList = new ArrayList<>();
            List<ReadingPassageQuestionSetEntries> readingPassageQuestionSetEntriesList = new ArrayList<>();

            UUID passageId = firstItem.getId();
            Genre genre = Genre.getByGenreName(firstItem.getWrittingStyle());
            if (genre == null) {
                log.info("passageId: {} 未知的文体类型: {}", passageId,firstItem.getWrittingStyle());
                return;
            }
            // 创建文章实体
            ReadingPassages readingPassages = ReadingPassages.builder()
                    .id(passageId)
                    .unitId(firstItem.getUnitId())
                    .title(firstItem.getTitle())
                    .content(firstItem.getGenArticle())
                    .genre(Genre.getByGenreName(firstItem.getWrittingStyle()).name())
                    .basePassageId(firstItem.getBasePassageId())
                    .source("srcClone")
                    .build();


            //处理克隆题
            dealQuestionsData(firstItem.getJsonGenQuestions(),passageId,
                    readingPassageQuestionSetsList,readingPassageQuestionsList,readingQuestionAnswersList,
                    readingQuestionKnowledgePointsList,readingPassageQuestionSetEntriesList);

            log.info("开始保存文章和问题数据-文章id: {}", passageId);
            log.info("题目集{}个，题目{}个，答案{}个，知识点{}个",readingPassageQuestionSetsList.size(),readingPassageQuestionsList.size(),readingQuestionAnswersList.size(),readingQuestionKnowledgePointsList.size());
            if (CollectionUtil.isNotEmpty(readingPassageQuestionSetsList)) {
                this.save(readingPassages);
                readingPassageQuestionSetsService.saveBatch(readingPassageQuestionSetsList);
                if (CollectionUtil.isNotEmpty(readingPassageQuestionsList)) {
                    readingPassageQuestionsService.saveBatch(readingPassageQuestionsList);
                    readingPassageQuestionSetEntriesService.saveBatch(readingPassageQuestionSetEntriesList);
                    if (CollectionUtil.isNotEmpty(readingQuestionAnswersList)) {
                        readingQuestionAnswersService.saveBatch(readingQuestionAnswersList);
                        if (CollectionUtil.isNotEmpty(readingQuestionKnowledgePointsList)) {
                            readingQuestionKnowledgePointsService.saveBatch(readingQuestionKnowledgePointsList);
                        }
                    }
                }
            }
            baseMapper.updateProcessStatusByPassageId(passageId);
            log.info("保存文章和问题数据完成-文章id: {}", passageId);
        });

    }



    @Override
    public void dealUploadData(JSONObject json) {
        List<ReadingPassageQuestionSets> readingPassageQuestionSetsList = new ArrayList<>();
        List<ReadingPassageQuestions> readingPassageQuestionsList = new ArrayList<>();
        List<ReadingQuestionAnswers> readingQuestionAnswersList = new ArrayList<>();
        List<ReadingQuestionKnowledgePoints> readingQuestionKnowledgePointsList = new ArrayList<>();
        List<ReadingPassageQuestionSetEntries> readingPassageQuestionSetEntriesList = new ArrayList<>();

        UUID passageId = UUID.randomUUID();

        ReadingUnits readingUnits = readingUnitsService.getOne(Wrappers.<ReadingUnits>lambdaQuery()
                .eq(ReadingUnits::getGrade, json.getInt("grade"))
                .eq(ReadingUnits::getSemester, 2)
                .eq(ReadingUnits::getName, "期末"));

        // 创建文章实体
        ReadingPassages readingPassages = ReadingPassages.builder()
                .id(passageId)
                .title(json.getStr("title"))
                .content(json.getStr("article"))
                .genre(Genre.getByGenreName(json.getStr("src_genre")).name())
                .unitId(readingUnits.getId())
                .source("fileClone")
                .build();

        String questions = json.getStr("questions");
        dealQuestionsData(questions,passageId,
                readingPassageQuestionSetsList,readingPassageQuestionsList,readingQuestionAnswersList,
                readingQuestionKnowledgePointsList,readingPassageQuestionSetEntriesList);
        log.info("开始保存文章和问题数据-文章id: {}", passageId);
        log.info("题目集{}个，题目{}个，答案{}个，知识点{}个",readingPassageQuestionSetsList.size(),readingPassageQuestionsList.size(),readingQuestionAnswersList.size(),readingQuestionKnowledgePointsList.size());
        if (CollectionUtil.isNotEmpty(readingPassageQuestionSetsList)) {
                this.save(readingPassages);
                readingPassageQuestionSetsService.saveBatch(readingPassageQuestionSetsList);
                if (CollectionUtil.isNotEmpty(readingPassageQuestionsList)) {
                    readingPassageQuestionsService.saveBatch(readingPassageQuestionsList);
                    readingPassageQuestionSetEntriesService.saveBatch(readingPassageQuestionSetEntriesList);
                    if (CollectionUtil.isNotEmpty(readingQuestionAnswersList)) {
                        readingQuestionAnswersService.saveBatch(readingQuestionAnswersList);
                        if (CollectionUtil.isNotEmpty(readingQuestionKnowledgePointsList)) {
                            readingQuestionKnowledgePointsService.saveBatch(readingQuestionKnowledgePointsList);
                        }
                    }
                }
        }
        log.info("保存文章和问题数据完成-文章id: {}", passageId);



    }

    public void dealQuestionsData(String questions,UUID passageId,
                                  List<ReadingPassageQuestionSets> readingPassageQuestionSetsList,
                                  List<ReadingPassageQuestions> readingPassageQuestionsList,
                                  List<ReadingQuestionAnswers> readingQuestionAnswersList,
                                  List<ReadingQuestionKnowledgePoints> readingQuestionKnowledgePointsList,
                                  List<ReadingPassageQuestionSetEntries> readingPassageQuestionSetEntriesList) {
//[
// {
//  "type": "填空",
//  "difficult": "★★",
//  "knowledge": "细节理解",
//  "context": "根据文章第二自然段的内容，我的风筝是一只美丽的<cloze:1>，翅膀上画着<cloze:2>的图案。",
//  "answer": "蝴蝶|五颜六色",
//  "review": "本题考察对文章细节信息的提取能力。文章第二自然段明确写道：“我的风筝是一只美丽的蝴蝶，翅膀上画着五颜六色的图案。”"
//  },
//  {
//  "type": "问答", "difficult": "★★★", "knowledge": "修辞手法",
//  "context": "在文章第三自然段中，作者写道：“它像一只真正的蝴蝶，在空中自由自在地舞蹈。”这里使用了什么修辞手法？把风筝比作蝴蝶在空中舞蹈，表达了作者怎样的感受？",
//  "answer": "比喻。表达了作者看到风筝高飞时的喜悦、轻松和自由的感觉，仿佛自己的心也跟着风筝一起飞翔。",
//  "review": "本题考察对修辞手法的辨析和理解能力。句子中使用了“像”字，将风筝比作蝴蝶，是比喻的修辞手法。风筝像蝴蝶一样自由自在地舞蹈，体现了作者看到风筝高飞时内心感受到的那种无拘无束的快乐和轻盈。"
//  },
//  {"type": "选择", "difficult": "★★★★", "knowledge": "情感表达",
//  "context": "根据文章第四和第五自然段的内容，你认为风筝线牵着的不仅仅是风筝，它还牵着作者（以及大人们）心中的什么？",
//  "options": ["A. A) 对风筝的热爱和飞翔的渴望", "B. B) 对远方亲人的思念和想要实现的梦想", "C. C) 对大自然的热爱和对春天的赞美", "D. D) 对小伙伴的友情和对游戏的快乐"],
//  "answer": "B",
//  "review": "本题考察对文章深层含义和情感的理解。第四自然段提到把小秘密和梦想“寄”给远方的奶奶和蓝天，第五自然段说大人们也有“想寄出去的心愿”，风筝线牵着“心中那些说不出口的思念和梦想”。选项B最准确地概括了这一点。"
//  },
//  {
//  "type": "问答", "difficult": "★★★★", "knowledge": "主旨大意",
//  "context": "这篇散文通过描写春天放风筝的经历和感受，主要表达了作者怎样的思想感情？",
//  "answer": "这篇散文主要表达了作者在放风筝过程中感受到的快乐、自由以及通过风筝寄托对远方亲人的思念和对美好梦想的向往之情。",
//  "review": "本题考察对文章中心思想的概括能力。文章从描写放风筝的快乐开始，重点通过风筝寄托了作者对奶奶的思念和对梦想的渴望，并推及到大人们，点明风筝线牵着的不仅仅是物品，更是情感和希望。因此，中心思想是借放风筝抒发思念、寄托梦想和希望。"
//  },
//  {"type": "问答", "difficult": "★★★★★", "knowledge": "联想与想象",
//  "context": "文章中，作者想象风筝可以带着自己的小秘密和梦想飞向远方。如果你有一只特别的风筝，你最想让它带着什么飞向哪里？为什么？（请结合自己的生活或想法回答）",
//  "answer": "（本题为开放性题目，答案合理即可得分，需包含“想让风筝带走/带去什么”、“飞向哪里”以及“原因”。例如：）我最想让我的风筝带着我对考试不再紧张的愿望，飞向广阔的天空。因为我每次考试都很紧张，希望这个愿望能随着风筝飞走，让我以后能轻松面对考试。",
//  "review": "本题考察学生的联想与想象能力以及结合文本进行发散性思考的能力。鼓励学生联系自身经历或想法，将文章中“寄托心愿”的主题进行迁移运用，表达自己的愿望或想法，并说明理由。答案需包含“带什么”、“飞哪里”和“为什么”三个要素。"
//  }
// ]
        JSONArray questionsJsonArray = JSONUtil.parseArray(questions);
        if (questionsJsonArray.isEmpty()) {
            return;
        }
        // 1问题集->N问题
        // 1问题(含题目、选项)->1答案（含答案、解析）->1知识点
        // 创建问题集实体
        UUID questionSetId = UUID.randomUUID();
        ReadingPassageQuestionSets readingPassageQuestionSets = ReadingPassageQuestionSets.builder()
                .id(questionSetId)
                .name("第一套")
                .passageId(passageId)
                .build();
        readingPassageQuestionSetsList.add(readingPassageQuestionSets);

        for (int i = 0; i < questionsJsonArray.size(); i++) {
            JSONObject question = JSONUtil.parseObj(questionsJsonArray.get(i));
            String questionType = question.getStr("type");
            String context = question.getStr("context");
            String answer = question.getStr("answer");
            String review = question.getStr("review");
            String knowledge = question.getStr("knowledge");
            String options = question.getStr("options");
            Integer serialNumber = question.getInt("serial_number");
            serialNumber = serialNumber == null ? i+1 : serialNumber;

            JSONObject contentJson = new JSONObject();
            contentJson.set("问题", context);
            if (StrUtil.isNotBlank(options)) {
                JSONArray optionArray = JSONUtil.parseArray(options);
                if (optionArray.isEmpty() || !optionArray.getFirst().toString().matches("[A-Z][:.\\s].*")) {
                    contentJson.set("选项", optionArray);
                } else {
                    List<Map<String, String>> optionList = new ArrayList<>();
                    for (Object option : optionArray) {
                        String optionStr = (String) option;
                        String key = optionStr.substring(0, 1);
                        String value = optionStr.substring(2);
                        Map<String, String> optionMap = new HashMap<>();
                        optionMap.put("key", key);
                        optionMap.put("value", value);
                        optionList.add(optionMap);
                    }
                    contentJson.set("选项", optionList);
                }
            }

            JSONObject answerJson = new JSONObject();
            answerJson.set("答案", answer);


            // 创建题目实体
            UUID questionId = UUID.randomUUID();
            ReadingPassageQuestions readingPassageQuestion = ReadingPassageQuestions.builder()
                    .id(questionId)
                    .passageId(passageId)
                    .questionType(ReadingQuestionType.getByType(questionType).name())
                    .content(contentJson.toString())
                    .build();
            readingPassageQuestionsList.add(readingPassageQuestion);

            //创建答案实体
            UUID answerId = UUID.randomUUID();
            ReadingQuestionAnswers readingQuestionAnswers = ReadingQuestionAnswers.builder()
                    .id(answerId)
                    .questionId(questionId)
                    .answer(answerJson.toString())
                    .content(review)
                    .build();
            readingQuestionAnswersList.add(readingQuestionAnswers);

            //创建知识点和题目关系实体
            UUID knowledgePointId = getKnowledgePointId(knowledge);
            if (knowledgePointId != null) {
                ReadingQuestionKnowledgePoints readingQuestionKnowledgePoints = ReadingQuestionKnowledgePoints.builder()
                        .questionId(questionId)
                        .knowledgePointId(knowledgePointId)
                        .build();
                readingQuestionKnowledgePointsList.add(readingQuestionKnowledgePoints);
            }

            //创建题目和题目集关系实体
            ReadingPassageQuestionSetEntries readingPassageQuestionSetEntries = ReadingPassageQuestionSetEntries.builder()
                    .passageId(passageId)
                    .setId(questionSetId)
                    .questionId(questionId)
                    .orderNo(serialNumber)
                    .build();
            readingPassageQuestionSetEntriesList.add(readingPassageQuestionSetEntries);

        }
    }

}