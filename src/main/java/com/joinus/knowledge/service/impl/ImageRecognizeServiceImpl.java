package com.joinus.knowledge.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.RecognizeEduPaperCutRequest;
import com.aliyun.ocr_api20210707.models.RecognizeEduPaperCutResponse;
import com.aliyun.ocr_api20210707.models.RecognizeEduPaperStructedRequest;
import com.aliyun.ocr_api20210707.models.RecognizeEduPaperStructedResponse;
import com.baidu.aip.ocr.AipOcr;
import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.dto.CropImageDTO;
import com.joinus.knowledge.model.dto.CutQuestionSubjectDTO;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.param.CutImageFromPositionsParam;
import com.joinus.knowledge.model.param.ErasePenMarksParam;
import com.joinus.knowledge.model.param.GetCoordinateParam;
import com.joinus.knowledge.model.vo.CoordinatePoint;
import com.joinus.knowledge.model.vo.CutQuestionVO;
import com.joinus.knowledge.model.vo.OssFileVO;
import com.joinus.knowledge.service.AIChatService;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.service.ImageRecognizeService;
import com.joinus.knowledge.service.OssService;
import com.joinus.knowledge.utils.AliOssUtils;
import com.joinus.knowledge.utils.MinioUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class ImageRecognizeServiceImpl implements ImageRecognizeService {
    @Resource
    private Client aliyunOcrClient;
    @Resource
    private AipOcr aipOcrClient;
    @Autowired
    private MinioUtils minioUtils;
    @Resource
    private AliOssUtils aliOssUtils;
    @Resource
    private OssService ossService;
    @Resource
    private AIChatService aiChatService;
    @Resource
    private FilesService filesService;

    @Override
    public String paperCut(String imageUrl, String cutType, String imageType, String subject, Boolean outputOricood) {
        RecognizeEduPaperCutRequest request = new RecognizeEduPaperCutRequest()
                .setUrl(imageUrl)
                .setCutType(cutType)
                .setImageType(imageType)
                .setSubject(subject)
                .setOutputOricoord(outputOricood);
        RecognizeEduPaperCutResponse response;
        try {
            response = aliyunOcrClient.recognizeEduPaperCut(request);
            return response.getBody().getData();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public RecognizeEduPaperStructedResponse paperStructed(String imageUrl, String subject) {
        RecognizeEduPaperStructedRequest request = new RecognizeEduPaperStructedRequest()
                .setUrl(imageUrl)
                .setSubject(subject)
                .setNeedRotate(false)
                .setOutputOricoord(true);
        RecognizeEduPaperStructedResponse response;
        try {
            response = aliyunOcrClient.recognizeEduPaperStructed(request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return response;
    }

    @Override
    public CutQuestionVO paperStructedFromAli(String imgUrl, String type) {
        RecognizeEduPaperStructedResponse recognizeEduPaperStructedResponse = this.paperStructed(imgUrl, type);
        if (null == recognizeEduPaperStructedResponse) {
            log.info("调用接口异常 {}", JSONUtil.toJsonStr(recognizeEduPaperStructedResponse));
            throw new RuntimeException("解析图片失败");
        }
        if (!Integer.valueOf(200).equals(recognizeEduPaperStructedResponse.getStatusCode())) {
            log.info("解析图片失败 {}", JSONUtil.toJsonStr(recognizeEduPaperStructedResponse));
            throw new RuntimeException("解析图片失败");
        }
        if (null == recognizeEduPaperStructedResponse.getBody().getData()) {
            log.info("解析图片内容为空 {}", JSONUtil.toJsonStr(recognizeEduPaperStructedResponse));
            throw new RuntimeException("解析图片内容为空");
        }
        JSONObject body = JSONUtil.parseObj(recognizeEduPaperStructedResponse.getBody().getData());
        log.info("解析图片成功 {}", body);

        CutQuestionVO vo = CutQuestionVO.builder()
                .width(body.getInt("width"))
                .height(body.getInt("height"))
                .orgWidth(body.getInt("orgWidth"))
                .orgHeight(body.getInt("orgHeight"))
                .build();
        JSONArray partInfoArray = body.getJSONArray("part_info");
        List<CutQuestionSubjectDTO> cutQuestionSubjectDTOS = parseAliResponseToDTO(partInfoArray);
        vo.setSubjects(cutQuestionSubjectDTOS);
        return vo;
    }

    @Override
    public OssFileVO cutImageFromPositions(CutImageFromPositionsParam param) {
        String croppedOssKey = "";
        String presignedUrl = "";
        switch (param.getOssEnum()) {
            case OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB -> {
                croppedOssKey = this.cutImageFromPositionsViaAli(param);
                presignedUrl = aliOssUtils.generatePresignedUrl(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getBucket(), croppedOssKey);
            }
            case OssEnum.MINIO_EDU_KNOWLEDGE_HUB -> {
                croppedOssKey = this.cutImageFromPositionsViaImageIo(param);
                presignedUrl = minioUtils.getPresignedDownloadUrl(OssEnum.MINIO_EDU_KNOWLEDGE_HUB.getBucket(), croppedOssKey, croppedOssKey.substring(croppedOssKey.lastIndexOf("/") + 1));
            }
        }
        return OssFileVO.builder()
                .key(croppedOssKey)
                .presignedUrl(presignedUrl)
                .ossEnum(param.getOssEnum())
                .build();
    }

    private String cutImageFromPositionsViaImageIo(CutImageFromPositionsParam param) {
        String ossKey = param.getOssKey();
        String fileName = ossKey.substring(ossKey.lastIndexOf("/") + 1);
        String imgUrl = minioUtils.getPresignedDownloadUrl(OssEnum.MINIO_EDU_KNOWLEDGE_HUB.getBucket(), ossKey, fileName);
        try {
            log.info("开始根据坐标截取图片: {}", JSONUtil.toJsonStr(param));

            // 获取图片URL和坐标点
            List<CoordinatePoint> positions = param.getPositions();

            if (imgUrl == null || imgUrl.isEmpty() || positions == null || positions.isEmpty()) {
                log.error("图片URL或坐标点为空");
                throw new RuntimeException("图片URL或坐标点为空");
            }

            // 下载图片
            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(imgUrl))
                    .build();

            HttpResponse<InputStream> response = client.send(request, HttpResponse.BodyHandlers.ofInputStream());

            BufferedImage originalImage = javax.imageio.ImageIO.read(response.body());

            if (originalImage == null) {
                log.error("无法读取图片: {}", imgUrl);
                throw new RuntimeException("无法读取图片");
            }

            // 计算截取区域的边界
            int minX = Integer.MAX_VALUE;
            int minY = Integer.MAX_VALUE;
            int maxX = 0;
            int maxY = 0;

            for (CoordinatePoint point : positions) {
                int x = point.getX().intValue();
                int y = point.getY().intValue();

                minX = Math.min(minX, x);
                minY = Math.min(minY, y);
                maxX = Math.max(maxX, x);
                maxY = Math.max(maxY, y);
            }

            // 确保坐标在图片范围内
            minX = Math.max(0, minX);
            minY = Math.max(0, minY);
            maxX = Math.min(originalImage.getWidth(), maxX);
            maxY = Math.min(originalImage.getHeight(), maxY);

            // 计算宽度和高度
            int width = maxX - minX;
            int height = maxY - minY;

            if (width <= 0 || height <= 0) {
                log.error("无效的截取区域: width={}, height={}", width, height);
                throw new RuntimeException("无效的截取区域");
            }

            // 截取图片
            BufferedImage croppedImage = originalImage.getSubimage(minX, minY, width, height);

            // 将截取的图片转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            javax.imageio.ImageIO.write(croppedImage, "png", baos);
            byte[] imageBytes = baos.toByteArray();

            InputStream inputStream = new ByteArrayInputStream(imageBytes);

            String ossKeyPrefix = ossKey.substring(0, ossKey.lastIndexOf(".") + 1);
            String ossKeySuffix = ossKey.substring(ossKey.lastIndexOf(".") + 1);

            String croppedOssKey = ossKeyPrefix + "/" + UUID.randomUUID().toString() + "." + ossKeySuffix;
            minioUtils.uploadInputStream(OssEnum.MINIO_EDU_KNOWLEDGE_HUB.getBucket(), croppedOssKey, inputStream, imageBytes.length);
            return croppedOssKey;
        } catch (Exception e) {
            log.error("截取图片失败", e);
            throw new RuntimeException("截取图片失败: " + e.getMessage(), e);
        }
    }

    private String cutImageFromPositionsViaAli(CutImageFromPositionsParam param) {
        CropImageDTO cropImageDTO = getXYWHFromPositions(param.getPositions());
        String croppedOssKey = aliOssUtils.cropImage(param.getOssKey(), cropImageDTO.getX(), cropImageDTO.getY(), cropImageDTO.getW(), cropImageDTO.getH());
        return croppedOssKey;
    }

    private CropImageDTO getXYWHFromPositions(List<CoordinatePoint> positions) {
        CoordinatePoint firstPoint = positions.get(0);
        CoordinatePoint thirdPoint = positions.get(2);
        CropImageDTO cropImageDTO = new CropImageDTO();
        if (null == firstPoint || null == thirdPoint) {
            throw new RuntimeException("坐标点不能为空");
        }
        cropImageDTO.setX(firstPoint.getX().intValue());
        cropImageDTO.setY(firstPoint.getY().intValue());
        cropImageDTO.setW(thirdPoint.getX().intValue() - firstPoint.getX().intValue());
        cropImageDTO.setH(thirdPoint.getY().intValue() - firstPoint.getY().intValue());
        return cropImageDTO;
    }

    public static void main(String[] args) {
        String s = "{\n" +
                "  \"code\": 200,\n" +
                "  \"message\": \"操作成功\",\n" +
                "  \"data\": {\n" +
                "    \"algo_version\": \"2ca123f830dad3b81e147de7213b2e49afd8cd2f\",\n" +
                "    \"height\": 1672,\n" +
                "    \"orgHeight\": 1672,\n" +
                "    \"orgWidth\": 1402,\n" +
                "    \"page_id\": 0,\n" +
                "    \"page_title\": \"\",\n" +
                "    \"part_info\": [\n" +
                "      {\n" +
                "        \"part_title\": \"一、选择题(共10题,每题3分,共30分.在每题给出的四个选项中,只有一项符合题目要求)\",\n" +
                "        \"subject_list\": [\n" +
                "          {\n" +
                "            \"figure_list\": [],\n" +
                "            \"index\": 0,\n" +
                "            \"num_choices\": 4,\n" +
                "            \"pos_list\": [\n" +
                "              [\n" +
                "                {\n" +
                "                  \"x\": 31,\n" +
                "                  \"y\": 372\n" +
                "                },\n" +
                "                {\n" +
                "                  \"x\": 695,\n" +
                "                  \"y\": 372\n" +
                "                },\n" +
                "                {\n" +
                "                  \"x\": 695,\n" +
                "                  \"y\": 494\n" +
                "                },\n" +
                "                {\n" +
                "                  \"x\": 31,\n" +
                "                  \"y\": 494\n" +
                "                }\n" +
                "              ]\n" +
                "            ],\n" +
                "            \"prob\": 0,\n" +
                "            \"table_list\": [],\n" +
                "            \"text\": \"1.许多数学符号蕴含对称美,下列数学符号为中心对称图形的是()A.≥ B.= C.∵ D...\",\n" +
                "            \"type\": 0\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    ],\n" +
                "    \"prism_version\": \"1.0.9\",\n" +
                "    \"prism_wnum\": 0,\n" +
                "    \"width\": 1402\n" +
                "  }\n" +
                "}";

        CutQuestionSubjectDTO subjectDTO = new CutQuestionSubjectDTO();

        // 使用JSONUtil解析JSON字符串
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(s);
        cn.hutool.json.JSONObject data = jsonObject.getJSONObject("data");
        cn.hutool.json.JSONArray partInfoArray = data.getJSONArray("part_info");
        List<CutQuestionSubjectDTO> aliCutQuestionSubjectDTOS = parseAliResponseToDTO(partInfoArray);

        System.out.println("解析到的题目总数: " + aliCutQuestionSubjectDTOS.size());
        System.out.println("解析到的题目: " + JSONUtil.toJsonStr(aliCutQuestionSubjectDTOS));
    }

    private static List<CutQuestionSubjectDTO> parseAliResponseToDTO(JSONArray partInfoArray) {

        // 创建一个列表存储所有题目
        List<CutQuestionSubjectDTO> allSubjectDTOList = new java.util.ArrayList<>();

        // 使用Java 8 Stream API简化遍历
        partInfoArray.forEach(partInfoObj -> {
            cn.hutool.json.JSONObject partInfo = (cn.hutool.json.JSONObject) partInfoObj;
            cn.hutool.json.JSONArray subjectListArray = partInfo.getJSONArray("subject_list");

            if (subjectListArray != null && !subjectListArray.isEmpty()) {
                // 逐个解析每个subject对象
                for (int i = 0; i < subjectListArray.size(); i++) {
                    cn.hutool.json.JSONObject subjectObj = subjectListArray.getJSONObject(i);
                    CutQuestionSubjectDTO subjectDTO = JSONUtil.toBean(subjectObj, CutQuestionSubjectDTO.class);
                    allSubjectDTOList.add(subjectDTO);
                }
            }
        });

        System.out.println("解析到的题目总数: " + allSubjectDTOList.size());
        System.out.println("题目列表: " + JSONUtil.toJsonStr(allSubjectDTOList));
        return allSubjectDTOList;
    }

    private String removeHandwritingUrl(String imageUrl) {
         org.json.JSONObject jsonObject = aipOcrClient.removeHandwritingUrl(imageUrl);
         return jsonObject.getString("image_processed");
    }

    @Override
    public OssFileVO removeHandwriting(ErasePenMarksParam param) {
        String imageUrl = ossService.getPresignedDownloadUrl(param.getOssEnum(), param.getOssKey());
        String base64Str = removeHandwritingUrl(imageUrl);
        String ossKey = "";
        String presignedUrl = "";
        switch (param.getOssEnum()) {
            case ALIYUN_EDU_KNOWLEDGE_HUB -> {
                ossKey = aliOssUtils.uploadViaBase64(base64Str, "cropped", param.getOssKey().substring(param.getOssKey().lastIndexOf("/")+1));
                presignedUrl = aliOssUtils.generatePresignedUrl(ossKey);
            }
            case MINIO_EDU_KNOWLEDGE_HUB -> {
                ossKey = "tmp/remove-handwriting/" + UUID.randomUUID() + ".jpg";
                minioUtils.uploadBase64Image(param.getOssEnum().getBucket(), ossKey, base64Str);
                presignedUrl = minioUtils.getPresignedDownloadUrl(param.getOssEnum().getBucket(), ossKey, ossKey.substring(ossKey.lastIndexOf("/")+1));
            }
        }
        return OssFileVO.builder()
                .ossEnum(param.getOssEnum())
                .key(ossKey)
                .presignedUrl(presignedUrl)
                .build();
    }

    @Override
    public CutQuestionVO paperStructed(GetCoordinateParam param) {
        String presignedUrl = "";
        switch (param.getOssEnum()) {
            case ALIYUN_EDU_KNOWLEDGE_HUB -> {
                presignedUrl = aliOssUtils.generatePresignedUrl(param.getOssEnum().getBucket(), param.getOssKey());
            }
            case MINIO_EDU_KNOWLEDGE_HUB -> {
                presignedUrl = minioUtils.getPresignedDownloadUrl(param.getOssEnum().getBucket(), param.getOssKey(), param.getOssKey().substring(param.getOssKey().lastIndexOf("/")+1));
            }
        }
        return paperStructedFromAli(presignedUrl, param.getPaperSubjectType().getType());
    }

    @Override
    public void ocrHtml(String imageUrl, UUID fileId) {
        String promptText = """
            QwenVL HTML with image caption, 注意带上bbox坐标
            """;
        String assistantText = aiChatService.chat(promptText, imageUrl, AIModelType.JYSD_QWEN_VL);
        Pattern pattern = Pattern.compile("(?s)```(?:\\w+)?\\s*(.*?)\\s*```");
        Matcher matcher = pattern.matcher(assistantText);
        if (matcher.find()) {
            String htmlContent = matcher.group(1);
            File file = new File();
            file.setId(fileId);
            file.setOcrHtml(htmlContent);
            filesService.updateById(file);
        }
    }
}
