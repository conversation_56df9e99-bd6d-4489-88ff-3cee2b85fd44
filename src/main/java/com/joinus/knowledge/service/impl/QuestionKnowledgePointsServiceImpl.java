package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.dto.QuestionKnowledgePointDTO;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.entity.QuestionKnowledgePoint;
import com.joinus.knowledge.service.QuestionKnowledgePointsService;
import com.joinus.knowledge.mapper.QuestionKnowledgePointsMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_questions】的数据库操作Service实现
* @createDate 2025-03-01 10:22:43
*/
@Service
public class QuestionKnowledgePointsServiceImpl extends ServiceImpl<QuestionKnowledgePointsMapper, QuestionKnowledgePoint>
    implements QuestionKnowledgePointsService{

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createAssociation(UUID questionId, UUID knowledgePointId) {
        // 先检查是否已存在
        LambdaQueryWrapper<QuestionKnowledgePoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionKnowledgePoint::getQuestionId, questionId)
                  .eq(QuestionKnowledgePoint::getKnowledgePointId, knowledgePointId);

        List<QuestionKnowledgePoint> list = list(queryWrapper);

        if (CollUtil.isNotEmpty(list)) {
            // 已存在则返回成功
            return true;
        }
        
        // 不存在则创建
        return baseMapper.insertQuestionKnowledgePoints(questionId, knowledgePointId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateAssociationsByQuestionId(UUID questionId, List<UUID> knowledgePointIds) {

        // 没有新的关联
        if (knowledgePointIds == null || knowledgePointIds.isEmpty()) {
            return true;
        }
        
        // 添加新的关联
        for (UUID knowledgePointId : knowledgePointIds) {
            createAssociation(questionId, knowledgePointId);
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateAssociationsByKnowledgePointId(UUID knowledgePointId, List<UUID> questionIds) {
        // 删除该知识点的所有关联
        baseMapper.deleteByKnowledgePointId(knowledgePointId);
        
        // 没有新的关联
        if (questionIds == null || questionIds.isEmpty()) {
            return true;
        }
        
        // 添加新的关联
        for (UUID questionId : questionIds) {
            baseMapper.insertQuestionKnowledgePoints(questionId, knowledgePointId);
        }
        
        return true;
    }

    @Override
    public boolean deleteAssociation(UUID questionId, UUID knowledgePointId) {
        return baseMapper.deleteByQuestionIdAndKnowledgePointId(questionId, knowledgePointId) > 0;
    }

    @Override
    public boolean deleteAssociationsByQuestionId(UUID questionId) {
        return baseMapper.deleteByQuestionId(questionId) > 0;
    }

    @Override
    public boolean deleteAssociationsByKnowledgePointId(UUID knowledgePointId) {
        return baseMapper.deleteByKnowledgePointId(knowledgePointId) > 0;
    }

    @Override
    public List<UUID> getKnowledgePointIdsByQuestionId(UUID questionId) {
        List<QuestionKnowledgePoint> relations = baseMapper.selectByQuestionId(questionId);
        return relations.stream()
                .map(relation -> relation.getKnowledgePointId())
                .collect(Collectors.toList());
    }

    @Override
    public List<UUID> listQuestionIdsByKnowledgePointId(UUID knowledgePointId) {
        List<QuestionKnowledgePoint> relations = baseMapper.selectByKnowledgePointId(knowledgePointId);
        return relations.stream()
                .map(relation -> relation.getQuestionId())
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<MathKnowledgePoint> listKnowledgePointByQuestionId(UUID questionId, Integer grade, Integer semester, PublisherType publisher) {
        return baseMapper.listKnowledgePointByQuestionId(questionId, grade, semester, publisher);
    }

    @Override
    public UUID getSimilarQuestion(UUID questionId) {
        return baseMapper.getSimilarQuestion(questionId);
    }

    @Override
    public List<QuestionKnowledgePointDTO> listKnowledgePointDTOByQuestionId(UUID questionId) {
        return baseMapper.listKnowledgePointDTOByQuestionId(questionId);
    }

}
