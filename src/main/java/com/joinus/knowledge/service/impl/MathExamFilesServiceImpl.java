package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.MathExamFiles;
import com.joinus.knowledge.model.vo.FileVO;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.service.MathExamFilesService;
import com.joinus.knowledge.mapper.MathExamFilesMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_exam_files(试卷与文件关联表)】的数据库操作Service实现
* @createDate 2025-03-20 18:15:37
*/
@Service
public class MathExamFilesServiceImpl extends ServiceImpl<MathExamFilesMapper, MathExamFiles>
    implements MathExamFilesService{

    @Autowired
    private FilesService filesService;

    @Override
    public void removeByExamId(UUID id) {
        List<MathExamFiles> list = lambdaQuery().eq(MathExamFiles::getExamId, id).list();
        if (CollUtil.isNotEmpty(list)) {
            filesService.removeByIds(list.stream().map(MathExamFiles::getFileId).toList());
            remove(lambdaQuery().eq(MathExamFiles::getExamId, id).getWrapper());
        }
    }

    @Override
    public List<FileVO> listFilesByExamId(UUID id) {
        return baseMapper.listFilesByExamId(id);
    }
}




