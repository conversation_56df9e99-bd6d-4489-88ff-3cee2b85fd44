package com.joinus.knowledge.service.impl;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文本解析器：用于从文本中提取汉字、词语和句子。
 * 支持多种文本格式的智能解析。
 */
public class TextParser {
    // 拼音模式：匹配(拼音)汉字格式
    private static final Pattern PHONETIC_PATTERN = Pattern.compile("\\(\\p{Alpha}+\\)([\\u4e00-\\u9fa5])");
    // 汉字模式：匹配单个中文字符
    private static final Pattern CHINESE_CHAR_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5]");
    // 词语模式：匹配连续的中文字符
    private static final Pattern CHINESE_WORD_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5]+");
    // 句子模式：匹配以句号、问号、感叹号结尾的句子
    private static final Pattern SENTENCE_PATTERN = Pattern.compile("[^。！？]+[。！？]");

    /**
     * 解析文本，提取汉字、词语和句子
     * @param text 待解析文本
     * @return 解析结果对象，包含汉字、词语、句子列表
     */
    public static ParseResult parse(String text) {
        ParseResult result = new ParseResult();
        if (text == null || text.isEmpty()) return result;
        String[] lines = text.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;
            // 处理拼音行（如“（pinyin）字”或编号开头）
            if (line.startsWith("（") || line.matches("^\\d+\\..*")) {
                parsePhoneticLine(line, result.characters);
            } else if (line.startsWith("写字")) {
                parseSimpleLine(line, result.characters, "写字");
            } else if (line.startsWith("词语")) {
                parseSimpleLine(line, result.phrases, "词语");
            } else {
                // 普通行，提取汉字、词语、句子
                extractChineseChars(line, result.characters);
                extractChineseWords(line, result.phrases);
                extractSentences(line, result.sentences);
            }
        }
        return result;
    }

    /**
     * 提取所有单个汉字
     * @param text 输入文本
     * @return 汉字数组
     */
    public static String[] extractChineseCharsArray(String text) {
        if (text == null || text.isEmpty()) return new String[0];
        List<String> chars = new ArrayList<>();
        Matcher matcher = CHINESE_CHAR_PATTERN.matcher(text);
        while (matcher.find()) {
            chars.add(matcher.group());
        }
        return chars.toArray(new String[0]);
    }

    /**
     * 提取所有中文词语（连续的汉字，长度>=2）
     * @param text 输入文本
     * @return 词语数组
     */
    public static String[] extractChineseWordsArray(String text) {
        if (text == null || text.isEmpty()) return new String[0];
        List<String> words = new ArrayList<>();
        Matcher matcher = CHINESE_WORD_PATTERN.matcher(text);
        while (matcher.find()) {
            String word = matcher.group();
            if (word.length() >= 2) {
                words.add(word);
            }
        }
        return words.toArray(new String[0]);
    }

    /**
     * 提取所有句子（以。！？结尾）
     * @param text 输入文本
     * @return 句子数组
     */
    public static String[] extractSentencesArray(String text) {
        if (text == null || text.isEmpty()) return new String[0];
        List<String> sentences = new ArrayList<>();
        Matcher matcher = SENTENCE_PATTERN.matcher(text);
        while (matcher.find()) {
            sentences.add(matcher.group().trim());
        }
        return sentences.toArray(new String[0]);
    }

    /**
     * 解析带拼音的行，将汉字提取到列表
     * @param line 拼音行
     * @param output 输出列表
     */
    private static void parsePhoneticLine(String line, List<String> output) {
        Matcher m = PHONETIC_PATTERN.matcher(line);
        while (m.find()) {
            output.add(m.group(1));
        }
    }

    /**
     * 解析简单空格分隔行（如“写字/词语”行）
     * @param line 待解析行
     * @param output 输出列表
     * @param prefix 前缀（如“写字”或“词语”）
     */
    private static void parseSimpleLine(String line, List<String> output, String prefix) {
        line = line.replaceFirst(prefix, "").trim();
        String[] items = line.split("\\s+");
        for (String item : items) {
            if (!item.matches("\\d+") && !item.isEmpty()) {
                output.add(item);
            }
        }
    }

    /**
     * 提取汉字到指定列表
     * @param text 输入文本
     * @param output 输出列表
     */
    private static void extractChineseChars(String text, List<String> output) {
        Matcher matcher = CHINESE_CHAR_PATTERN.matcher(text);
        while (matcher.find()) {
            output.add(matcher.group());
        }
    }

    /**
     * 提取词语到指定列表（长度>=2）
     * @param text 输入文本
     * @param output 输出列表
     */
    private static void extractChineseWords(String text, List<String> output) {
        Matcher matcher = CHINESE_WORD_PATTERN.matcher(text);
        while (matcher.find()) {
            String word = matcher.group();
            if (word.length() >= 2) {
                output.add(word);
            }
        }
    }

    /**
     * 提取句子到指定列表
     * @param text 输入文本
     * @param output 输出列表
     */
    private static void extractSentences(String text, List<String> output) {
        Matcher matcher = SENTENCE_PATTERN.matcher(text);
        while (matcher.find()) {
            output.add(matcher.group().trim());
        }
    }

    /**
     * 解析结果类，封装所有解析内容
     */
    public static class ParseResult {
        public List<String> characters = new ArrayList<>(); // 单个汉字
        public List<String> phrases = new ArrayList<>();   // 词语
        public List<String> sentences = new ArrayList<>(); // 句子

        /**
         * 获取所有汉字的数组
         */
        public String[] getCharactersArray() {
            return characters.toArray(new String[0]);
        }
        /**
         * 获取所有词语的数组
         */
        public String[] getPhrasesArray() {
            return phrases.toArray(new String[0]);
        }
        /**
         * 获取所有句子的数组
         */
        public String[] getSentencesArray() {
            return sentences.toArray(new String[0]);
        }
        /**
         * 获取所有字词（汉字+词语，去重）
         */
        public String[] getAllWordsArray() {
            Set<String> allWords = new LinkedHashSet<>();
            allWords.addAll(characters);
            allWords.addAll(phrases);
            return allWords.toArray(new String[0]);
        }
        /**
         * 获取所有内容（汉字+词语+句子，去重）
         */
        public String[] getAllContentArray() {
            Set<String> allContent = new LinkedHashSet<>();
            allContent.addAll(characters);
            allContent.addAll(phrases);
            allContent.addAll(sentences);
            return allContent.toArray(new String[0]);
        }
        /**
         * 随机返回一个字或词
         */
        public String getRandomWord() {
            List<String> allWords = new ArrayList<>();
            allWords.addAll(characters);
            allWords.addAll(phrases);
            if (allWords.isEmpty()) return "";
            return allWords.get(new Random().nextInt(allWords.size()));
        }
    }

    /**
     * 示例主方法，可用于简单测试
     */
    public static void main(String[] args) {
        String inputText = """
                援 俱 弗 辩
                域 惯 圃 盐 溅 蕊 魏 搜 蚯 蚓 版 阶
                脆 拦 玻 璃 恶 怖
                词语：
                真理 领域 建树  司空见惯 疑问 敏感  提取  明显 无聊 不可思议 吻合 偶然 文献 证据  系统 整理 见微知著 灵感 机遇  机器 钟楼 洪亮 街心 盲人 坚硬 清脆
                单调 请求 加速  齿轮 玻璃 唯恐    丑恶  恐怖  证实
               """;
        // 解析文本
        ParseResult result = TextParser.parse(inputText);
        // 过滤数组，去除无效词
        String[] filterArray = {"识字", "写字", "词语", "：","识","字","写","字","词","语","生","注","音",":"};
        Arrays.stream(result.getAllWordsArray())
                .filter((word) -> !Arrays.asList(filterArray).contains(word))
                .forEach(word -> System.out.print(word + " "));
    }
}
