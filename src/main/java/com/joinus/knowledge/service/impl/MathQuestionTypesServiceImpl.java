package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.mapper.MathQuestionTypesMapper;
import com.joinus.knowledge.mapper.QuestionTypesMappingMapper;
import com.joinus.knowledge.model.dto.MathQuestionTypesSlimDTO;
import com.joinus.knowledge.model.entity.MathQuestionType;
import com.joinus.knowledge.model.entity.MathSection;
import com.joinus.knowledge.model.entity.QuestionTypesMapping;
import com.joinus.knowledge.model.po.MathQuestionTypePO;
import com.joinus.knowledge.model.vo.MathQuestionTypeVO;
import com.joinus.knowledge.service.MathQuestionTypesService;
import com.joinus.knowledge.service.MathSectionService;
import com.joinus.knowledge.service.QuestionTypesMappingService;
import com.joinus.knowledge.service.SectionQuestionTypesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_question_types】的数据库操作Service实现
* @createDate 2025-02-28 14:12:06
*/
@Service
public class MathQuestionTypesServiceImpl extends ServiceImpl<MathQuestionTypesMapper, MathQuestionType>
    implements MathQuestionTypesService{

    @Autowired
    private SectionQuestionTypesService sectionQuestionTypesService;
    @Autowired
    private MathSectionService mathSectionService;
    @Autowired
    private QuestionTypesMappingMapper questionTypesMappingMapper;
    @Autowired
    private QuestionTypesMappingService questionTypesMappingService;

    @Override
    public List<MathQuestionTypesSlimDTO> listSlim() {
        // 创建查询条件
        LambdaQueryWrapper<MathQuestionType> queryWrapper = new LambdaQueryWrapper<>();
        // 按排序号升序排序
        queryWrapper.orderByAsc(MathQuestionType::getSortNo);
        
        // 查询所有题型
        List<MathQuestionType> questionTypes = list(queryWrapper);
        
        // 转换为精简DTO
        return questionTypes.stream().map(type -> {
            MathQuestionTypesSlimDTO dto = new MathQuestionTypesSlimDTO();
            // 只复制需要的字段：id、name和sortNo
            dto.setId(type.getId());
            dto.setName(type.getName());
            dto.setSortNo(type.getSortNo());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void deleteById(UUID keypointId) {
        MathQuestionType mathQuestionType = MathQuestionType.builder()
                .id(keypointId)
                .deletedAt(new Date())
                .build();
        baseMapper.deleteById(mathQuestionType);
        //删除题型与题目的关系
        LambdaQueryWrapper<QuestionTypesMapping> wrapper = Wrappers.lambdaQuery(QuestionTypesMapping.class)
                .eq(QuestionTypesMapping::getQuestionTypeId, keypointId);
        questionTypesMappingMapper.delete(wrapper);
    }

    @Override
    public MathQuestionType save(Integer pageNo, String name, UUID textbookId, Integer sortNo) {
        List<MathSection> sections = mathSectionService.getByPageNo(pageNo, textbookId, null);
        if (CollUtil.isEmpty(sections)) {
            throw new RuntimeException("小节不存在，不能添加知识点。");
        }
        MathSection section = sections.get(0);

        MathQuestionType mathQuestionType = MathQuestionType.builder()
                .name(name)
                .sortNo(null == sortNo ? 1 : sortNo)
                .build();
        baseMapper.insert(mathQuestionType);
        sectionQuestionTypesService.createRelation(section.getId(), mathQuestionType.getId(), pageNo);
        return mathQuestionType;
//
//        LambdaQueryWrapper<MathQuestionType> wrapper = Wrappers.lambdaQuery(MathQuestionType.class)
//                .eq(MathQuestionType::getName, name);
//        List<MathQuestionType> mathQuestionTypes = baseMapper.selectList(wrapper);
//        if (CollUtil.isNotEmpty(mathQuestionTypes)) {
//            sectionQuestionTypesService.createRelation(section.getId(), mathQuestionTypes.get(0).getId(), pageNo);
//            return mathQuestionTypes.get(0);
//        } else {
//            MathQuestionType mathQuestionType = MathQuestionType.builder()
//                    .name(name)
//                    .sortNo(1)
//                    .build();
//            baseMapper.insert(mathQuestionType);
//            sectionQuestionTypesService.createRelation(section.getId(), mathQuestionType.getId(), pageNo);
//            return mathQuestionType;
//        }
    }

    @Override
    public List<MathQuestionType> listQuestionTypesByQuestionId(UUID questionId) {
        List<UUID> questionTypeIds = questionTypesMappingService.listQuestionIdsByQuestionTypeId(questionId);
        return lambdaQuery().in(MathQuestionType::getId, questionTypeIds)
                .list();
    }

    @Override
    public Map<UUID, List<MathQuestionTypeVO>> listQuestionTypeIds(List<UUID> questionIds) {
        HashMap<UUID, List<MathQuestionTypeVO>> map = new HashMap<>();

        List<QuestionTypesMapping> questionTypesMappings = questionTypesMappingService.listQuestionTypeMappings(questionIds);
        if (CollUtil.isEmpty(questionTypesMappings)) {
            return map;
        }

        List<MathQuestionType> list = lambdaQuery()
                .in(MathQuestionType::getId, questionTypesMappings.stream().map(QuestionTypesMapping::getQuestionTypeId).toList())
                .list();

        // 提前构建索引映射
        Map<UUID, MathQuestionType> questionTypeMap = list.stream()
                .collect(Collectors.toMap(MathQuestionType::getId, t -> t));

        Map<UUID, List<QuestionTypesMapping>> collect = questionTypesMappings.stream()
                .collect(Collectors.groupingBy(QuestionTypesMapping::getQuestionId));

        for (Map.Entry<UUID, List<QuestionTypesMapping>> entry : collect.entrySet()) {
            List<QuestionTypesMapping> values = entry.getValue();
            // 根据实际需求判断是否需要 size() > 1 的判断
            if (values.size() > 1) {
                List<MathQuestionTypeVO> qts = values.stream()
                        .map(QuestionTypesMapping::getQuestionTypeId)
                        .map(questionTypeMap::get)
                        .map(t -> BeanUtil.copyProperties(t, MathQuestionTypeVO.class))
                        .collect(Collectors.toList());
                map.put(entry.getKey(), qts);
            }
        }

        return map;
    }

    @Override
    public List<MathQuestionType> listByQuestionId(UUID id) {
        return baseMapper.listByQuestionId(id);
    }

    @Override
    public List<MathQuestionType> listByTextbookId(UUID textbookId) {
        return baseMapper.listByTextbookId(textbookId);
    }

    @Override
    public List<MathQuestionTypePO> listByQuestionIds(List<UUID> ids) {
        return baseMapper.listByQuestionIds(ids);
    }

    @Override
    public List<MathQuestionTypePO> listByQuestionIdsAndPublisher(List<UUID> ids, PublisherType publisher) {
        return baseMapper.listByQuestionIdsAndPublisher(ids, publisher);
    }

    @Override
    public List<MathQuestionTypeVO> list(String name, Integer grade, Integer semester, PublisherType publisher, UUID chapterId, String chapterName, UUID sectionId, String sectionName) {
        return baseMapper.list(name, grade, semester, publisher, chapterId, chapterName, sectionId, sectionName);
    }

    @Override
    public List<MathQuestionTypeVO> listByIds(List<UUID> ids) {
        return baseMapper.listByIds(ids);
    }

    @Override
    public List<MathQuestionTypeVO> listByIdsAndPublisher(List<UUID> ids, PublisherType publisher, Integer grade, Integer semester) {
        return baseMapper.listByIdsAndPublisher(ids, publisher, grade, semester);
    }

    @Override
    public List<MathQuestionTypeVO> listEnableAiQuestionCountByKnowledgePointIds(List<UUID> knowledgePointIds) {
        return baseMapper.listEnableAiQuestionCountByKnowledgePointIds(knowledgePointIds);
    }

    @Override
    public List<MathQuestionTypeVO> listBySectionIds(List<UUID> list) {
        return baseMapper.listBySectionIds(list);
    }

}
