package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathChapter;
import com.joinus.knowledge.model.vo.MathChapterVO;
import com.joinus.knowledge.service.MathChapterService;
import com.joinus.knowledge.mapper.MathChaptersMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_chapters】的数据库操作Service实现
* @createDate 2025-02-28 14:12:06
*/
@Service
public class MathChapterServiceImpl extends ServiceImpl<MathChaptersMapper, MathChapter>
    implements MathChapterService {

    @Override
    public List<MathChapter> listAllChaptersByBookId(UUID id) {

        LambdaQueryWrapper<MathChapter> eq = Wrappers.lambdaQuery(MathChapter.class)
                .eq(MathChapter::getTextbookId, id);

        return baseMapper.selectList(eq);
    }

    @Override
    public List<MathChapterVO> list(String name, Integer grade, Integer semester, PublisherType publisher) {
        return baseMapper.list(name, grade, semester, publisher);
    }
}




