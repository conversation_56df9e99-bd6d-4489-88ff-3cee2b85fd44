package com.joinus.knowledge.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baidu.aip.ocr.AipOcr;
import com.joinus.knowledge.enums.BaiDuOcrErrorMessage;
import com.joinus.knowledge.model.param.OcrHandWritingParam;
import com.joinus.knowledge.model.vo.OcrHandWritingVO;
import com.joinus.knowledge.service.OcrAIAbilityService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@AllArgsConstructor
@Service("ocrAIAbilityService")
public class OcrAIAbilityServiceImpl implements OcrAIAbilityService {

    private AipOcr aipOcrClient;

    @Override
    public List<OcrHandWritingVO> recognizeHandwrittenText(OcrHandWritingParam param) {
        HashMap<String, String> options = param.buildOptions();
        JSONObject jsonObject = aipOcrClient.handwritingUrl(param.getUrl(), options);

        if (jsonObject.has("error_code")) {
            String errorCode = jsonObject.get("error_code").toString();
            BaiDuOcrErrorMessage errorMessage = BaiDuOcrErrorMessage.getByCode(errorCode);
            throw new RuntimeException(errorMessage.getMsg());
        }

        int wordsResultNum = jsonObject.getInt("words_result_num");
        if (wordsResultNum > 0) {
            cn.hutool.json.JSONObject hJSONObject = JSONUtil.parseObj(jsonObject.toString());
            JSONArray jsonArray = hJSONObject.getJSONArray("words_result");
            return jsonArray.toList(OcrHandWritingVO.class);
        }
        return new ArrayList<>();
    }
}
