package com.joinus.knowledge.service.impl;

import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.config.SpeechAsrProperties;
import com.joinus.knowledge.service.SpeechAsrService;
import com.joinus.knowledge.utils.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@AllArgsConstructor
public class SpeechAsrServiceImpl implements SpeechAsrService {

    private TokenHolder tokenHolder;
    private SpeechAsrProperties speechAsrProperties;
    private AliOssUtils aliOssUtils;

    @Override
    public String baiduSpeechAsr(String path) throws IOException {
        String token = tokenHolder.getBaiduToken();
        return runJsonPostMethod(token, path);
    }

    public String runJsonPostMethod(String token, String path) throws IOException {
        String url = aliOssUtils.generatePresignedUrl(path);
        String format = path.substring(path.lastIndexOf(".") + 1);
        byte[] content = FilesUtil.getFileBytesByUrl(url);
        String speech = base64Encode(content);
        Map<String, Object> params = new HashMap();
        params.put("dev_pid", 80001);
        params.put("format", format);
        params.put("rate", 16000);
        params.put("token", token);
        params.put("cuid", String.valueOf(System.currentTimeMillis()));
        params.put("channel", "1");
        params.put("len", content.length);
        params.put("speech", speech);

        HttpRequestOptions<String, JSONObject> options = HttpRequestOptions.<String, JSONObject>builder()
                .url("https://vop.baidu.com/pro_api")
                .method(Method.POST)
                .headers(buildHeaders("Content-Type", "application/json; charset=utf-8"))
                .responseType(JSONObject.class)
                .bodyParams(JSONUtil.toJsonStr(params))
                .build();
        JSONObject result = HttpClient.execute(options);
        if (result != null && !"0".equals(result.getStr("err_no"))) {
            throw new IllegalArgumentException("百度语音识别失败" + result.getStr("err_msg"));
        }
        JSONArray array = JSONUtil.parseArray(result.getStr("result"));
        return array.getStr(0);
    }

    private Map<String, String> buildHeaders(String key, String value) {
        Map<String, String> headers = new HashMap<>();
        headers.put(key, value);
        return headers;
    }

    private String base64Encode(byte[] content) {
        Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(content);
    }

    @Override
    public String aliSpeechAsr(String path) throws IOException {
        String format = path.substring(path.lastIndexOf(".") + 1);
        int sampleRate = 16000;
        boolean enablePunctuationPrediction = true;
        boolean enableInverseTextNormalization = true;
        boolean enableVoiceDetection = false;
        String url = "https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/asr";
        String request = url;
        request = request + "?appkey=" + speechAsrProperties.getAliAppKey();
        request = request + "&format=" + format;
        request = request + "&sample_rate=" + sampleRate;
        if (enablePunctuationPrediction) {
            request = request + "&enable_punctuation_prediction=" + true;
        }
        if (enableInverseTextNormalization) {
            request = request + "&enable_inverse_text_normalization=" + true;
        }
        if (enableVoiceDetection) {
            request = request + "&enable_voice_detection=" + true;
        }
        System.out.println("Request: " + request);

        /**
         * 设置HTTPS头部字段：
         * 1.鉴权参数。
         * 2.Content-Type：application/octet-stream。
         */
        HashMap<String, String> headers = new HashMap<String, String>();
        headers.put("X-NLS-Token", tokenHolder.getAliToken());
        headers.put("Content-Type", "application/octet-stream");
        byte[] data = FilesUtil.getFileBytesByUrl(aliOssUtils.generatePresignedUrl(path));
        JSONObject result = HttpClient.sendOctetStreamData(request, headers, data);
        if (result != null && !"20000000".equals(result.getStr("status"))) {
            throw new IllegalArgumentException("阿里语音识别失败" + result.getStr("message"));
        }
        return result.getStr("result");
    }
}
