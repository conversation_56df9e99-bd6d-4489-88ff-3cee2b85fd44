package com.joinus.knowledge.service.impl;

import com.joinus.knowledge.model.param.ReadingAIAbilityParam;
import com.joinus.knowledge.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;

@Slf4j
@AllArgsConstructor
@Service("readingAIAbilityService")
public class ReadingAIAbilityServiceImpl implements ReadingAIAbilityService {

    private ReadingAIAbilityAsyncService readingAIAbilityAsyncService;
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public ReadingAIAbilityParam aiCorrect(ReadingAIAbilityParam param) {
        // 等待所有任务完成再返回结果
        CompletableFuture.allOf(
                param.getItems().stream()
                        .map(question -> CompletableFuture.runAsync(() -> {
                                            BigDecimal result = readingAIAbilityAsyncService.aiCorrect(param.getId(), question.getId(), question.getAnswer());
                                            question.setResult(result);
                                        },
                                        threadPoolTaskExecutor)
                                )
                        .toArray(CompletableFuture[]::new)
        ).join();
        return param;
    }

    @Override
    public void weakKnowledgePointAnalysis(ReadingAIAbilityParam param) {
        threadPoolTaskExecutor.execute(() -> readingAIAbilityAsyncService.weakKnowledgePointAnalysis(param));
    }

    @Override
    public void trainingSuggestion(ReadingAIAbilityParam param) {
        threadPoolTaskExecutor.execute(() -> readingAIAbilityAsyncService.trainingSuggestion(param));
    }

    @Override
    public void weakQuestionTypeAnalysis(ReadingAIAbilityParam param) {
        threadPoolTaskExecutor.execute(() -> readingAIAbilityAsyncService.weakQuestionTypeAnalysis(param));
    }

    @Override
    public void dealByAIAbility(ReadingAIAbilityParam param) {
        threadPoolTaskExecutor.execute(() -> readingAIAbilityAsyncService.dealByAIAbility(param));
    }

}
