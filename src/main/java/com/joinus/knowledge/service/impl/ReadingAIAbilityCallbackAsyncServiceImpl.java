package com.joinus.knowledge.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.config.AiReadingCallbackProperties;
import com.joinus.knowledge.service.ReadingAIAbilityCallbackAsyncService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.Strings;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@AllArgsConstructor
@Service("readingAIAbilityCallbackAsyncService")
public class ReadingAIAbilityCallbackAsyncServiceImpl implements ReadingAIAbilityCallbackAsyncService {

    private AiReadingCallbackProperties aiReadingCallbackProperties;

    @Override
    public void aiCorrectCallback(String testId, String questionId, int resultCode) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("id", testId);
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonArrayItem = new JSONObject();
        jsonArrayItem.set("id", questionId);
        jsonArrayItem.set("resultCode", resultCode);
        jsonArray.add(jsonArrayItem);
        jsonObject.set("items", jsonArray);
        String url = aiReadingCallbackProperties.getHost() + aiReadingCallbackProperties.getCorrectUrl();
        String result = HttpUtil.post(url, jsonObject.toString());
        log.info("aiCorrectCallback，testId：{}，questionId：{}，parameters：{}，result：{}", testId, questionId, jsonObject, result);
    }

    @Override
    public void weakKnowledgePointAnalysisCallback(String id, Integer type, List<String> resultList) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("id", id);
        jsonObject.set("type", type);
        JSONArray jsonArray = new JSONArray();
        resultList.forEach(result -> {
            JSONObject jsonArrayItem = new JSONObject();
            String[] array = result.split("###");
            Arrays.stream(array).forEach(item -> {
                String itemResult = StrUtil.subAfter(item, ":", false);
                if (StrUtil.contains(item, "薄弱知识点:")) {
                    jsonArrayItem.set("knowledgePoint", itemResult);
                } else if (StrUtil.contains(item, "优势:")) {
                    jsonArrayItem.set("strengths", itemResult);
                } else if (StrUtil.contains(item, "题型:")) {
                    itemResult = StrUtil.replace(itemResult, "题", "");
                    jsonArrayItem.set("questionType", itemResult);
                } else if (StrUtil.contains(item, "提升策略与答题方法:")) {
                    jsonArrayItem.set("suggestions", itemResult);
                } else if (StrUtil.contains(item, "劣势:")) {
                    jsonArrayItem.set("weaknessAnalysis", itemResult);
                }
            });
            jsonArray.add(jsonArrayItem);
        });
        jsonObject.set("items", jsonArray);
        log.info("weakKnowledgePointAnalysisCallback，id：{}，type：{}，parameters：{}", id, type, jsonObject);
        String url = aiReadingCallbackProperties.getHost() + aiReadingCallbackProperties.getWeakKnowledgePointAnalysisUrl();
        String result = HttpUtil.post(url, jsonObject.toString(), 3000);
        log.info("weakKnowledgePointAnalysisCallback，id：{}，result：{}", id, result);
    }

    @Override
    public void trainingSuggestionCallback(String id, Integer type, List<String> suggestions) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("id", id);
        jsonObject.set("type", type);
        JSONArray jsonArray = new JSONArray();
        suggestions.stream()
                .filter(suggestion -> StrUtil.contains(suggestion, "|"))
                .forEach(suggestion -> {
                    String[] array = suggestion.split("\\|");
                    for (String item : array){
                        JSONObject jsonArrayItem = new JSONObject();
                        jsonArrayItem.set("suggestionTitle", StrUtil.subBefore(item, ":", false));
                        jsonArrayItem.set("content", StrUtil.subAfter(item, ":", false));
                        jsonArray.add(jsonArrayItem);
                    }
                });
        jsonObject.set("items", jsonArray);
        log.info("trainingSuggestionCallback，id：{}，type：{}，parameters：{}", id, type, jsonObject);
        String url = aiReadingCallbackProperties.getHost() + aiReadingCallbackProperties.getTrainingSuggestionUrl();
        String result = HttpUtil.post(url, jsonObject.toString(), 3000);
        log.info("trainingSuggestionCallback，id：{}，result：{}", id, result);
    }

    @Override
    public void weakQuestionTypeAnalysisCallback(String id, Integer type, List<String> resultList) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("id", id);
        jsonObject.set("type", type);
        JSONArray jsonArray = new JSONArray();

        resultList.forEach(result -> {
            JSONObject jsonArrayItem = new JSONObject();
            String[] array = result.split("###");
            Arrays.stream(array).forEach(item -> {
                String itemResult = StrUtil.subAfter(item, ":", false);
                if (StrUtil.contains(item, "薄弱题型:")) {
                    jsonArrayItem.set("questionType", itemResult);
                } else if (StrUtil.contains(item, "提升策略与答题方法:")) {
                    jsonArrayItem.set("interventionPlan", itemResult);
                } else if (StrUtil.contains(item, "专项训练建议:")) {
                    jsonArrayItem.set("suggestion", itemResult);
                }
            });
            jsonArray.add(jsonArrayItem);
        });

        jsonObject.set("items", jsonArray);
        log.info("weakQuestionTypeAnalysisCallback，id：{}，type：{}，parameters：{}", id, type, jsonObject);
        String url = aiReadingCallbackProperties.getHost() + aiReadingCallbackProperties.getWeakQuestionTypeAnalysisUrl();
        String result = HttpUtil.post(url, jsonObject.toString(), 3000);
        log.info("weakQuestionTypeAnalysisCallback，id：{}，result：{}", id, result);
    }

    @Override
    public void callbackReadingCommon(String callbackId, Integer readingAIAbilityType, Object result) {
        String url = aiReadingCallbackProperties.getHost() + aiReadingCallbackProperties.getCommonUrl();
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("id", callbackId);
        jsonObject.set("readingAIAbilityType", readingAIAbilityType);
        jsonObject.set("result", result);
        HttpUtil.post(url,JSONUtil.toJsonStr(jsonObject), 3000);
    }
}
