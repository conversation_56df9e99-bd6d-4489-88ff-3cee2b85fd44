package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.MathQuestionRelationships;
import com.joinus.knowledge.service.MathQuestionRelationshipsService;
import com.joinus.knowledge.mapper.MathQuestionRelationshipsMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_relationships】的数据库操作Service实现
* @createDate 2025-03-29 10:18:30
*/
@Service
public class MathQuestionRelationshipsServiceImpl extends ServiceImpl<MathQuestionRelationshipsMapper, MathQuestionRelationships>
    implements MathQuestionRelationshipsService{

    @Override
    public MathQuestionRelationships getParentBookQuestion(UUID id) {
        LambdaQueryWrapper<MathQuestionRelationships> wrapper = Wrappers.lambdaQuery(MathQuestionRelationships.class)
                .eq(MathQuestionRelationships::getDerivedQuestionId, id);
        List<MathQuestionRelationships> mathQuestionRelationships = baseMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(mathQuestionRelationships)) {
            return mathQuestionRelationships.get(0);
        }
        return null;
    }

    @Override
    public List<MathQuestionRelationships> getDerivativeAiBookQuestion(UUID id) {
        LambdaQueryWrapper<MathQuestionRelationships> wrapper = Wrappers.lambdaQuery(MathQuestionRelationships.class)
                .eq(MathQuestionRelationships::getBaseQuestionId, id);
        return baseMapper.selectList(wrapper);
    }
}




