package com.joinus.knowledge.service.impl.tag;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.enums.ExamTagType;
import com.joinus.knowledge.model.entity.MathExamTag;
import com.joinus.knowledge.model.param.AddExamTagParam;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 标签约束验证器
 * 使用策略模式处理不同类型标签的约束
 */
@Slf4j
public class TagConstraintValidator {
    
    private final Map<ExamTagType, TagConstraintStrategy> strategies;
    private final Map<ExamTagType, Object> constraintStates;
    
    public TagConstraintValidator(List<MathExamTag> existingTags) {
        this.strategies = new HashMap<>();
        this.constraintStates = new HashMap<>();
        
        // 注册不同类型标签的约束策略
        registerStrategy(ExamTagType.ALIAS, new AliasTagConstraintStrategy());
        // 未来可以在这里添加其他类型的约束策略
        // registerStrategy(ExamTagType.ELITE_SCHOOL_EXAM_PAGERS, new EliteSchoolConstraintStrategy());

        // 示例：如果需要为名校试卷添加约束，只需要取消注释上面一行，并实现对应的策略类
        
        // 初始化约束状态
        initializeConstraintStates(existingTags);
    }
    
    /**
     * 注册标签约束策略
     */
    private void registerStrategy(ExamTagType tagType, TagConstraintStrategy strategy) {
        strategies.put(tagType, strategy);
    }
    
    /**
     * 初始化约束状态
     */
    private void initializeConstraintStates(List<MathExamTag> existingTags) {
        for (ExamTagType tagType : strategies.keySet()) {
            TagConstraintStrategy strategy = strategies.get(tagType);
            Object state = strategy.initializeState(existingTags);
            constraintStates.put(tagType, state);
        }
    }
    
    /**
     * 验证标签约束
     */
    public TagConstraintResult validateTag(UUID examId, AddExamTagParam param) {
        TagConstraintStrategy strategy = strategies.get(param.getType());
        if (strategy == null) {
            // 没有特殊约束的标签类型，直接通过
            return TagConstraintResult.success();
        }
        
        Object state = constraintStates.get(param.getType());
        return strategy.validateTag(examId, param, state);
    }
    
    /**
     * 记录已添加的标签，更新约束状态
     */
    public void recordAddedTag(AddExamTagParam param) {
        TagConstraintStrategy strategy = strategies.get(param.getType());
        if (strategy != null) {
            Object state = constraintStates.get(param.getType());
            Object newState = strategy.updateStateAfterAdd(param, state);
            constraintStates.put(param.getType(), newState);
        }
    }
}

/**
 * 标签约束策略接口
 */
interface TagConstraintStrategy {
    
    /**
     * 初始化约束状态
     */
    Object initializeState(List<MathExamTag> existingTags);
    
    /**
     * 验证标签约束
     */
    TagConstraintResult validateTag(UUID examId, AddExamTagParam param, Object state);
    
    /**
     * 添加标签后更新状态
     */
    Object updateStateAfterAdd(AddExamTagParam param, Object state);
}

/**
 * ALIAS 类型标签的约束策略
 * 约束：同一试卷只能有一个 isPrimary=true 的 ALIAS 标签
 */
class AliasTagConstraintStrategy implements TagConstraintStrategy {
    
    @Override
    public Object initializeState(List<MathExamTag> existingTags) {
        // 检查现有标签中是否已有主要别名
        boolean hasExistingPrimaryAlias = existingTags.stream()
                .filter(tag -> tag.getType() == ExamTagType.ALIAS)
                .anyMatch(tag -> TagPropertyUtils.isPrimaryTag(tag.getProperties()));

        return new AliasConstraintState(hasExistingPrimaryAlias, false);
    }
    
    @Override
    public TagConstraintResult validateTag(UUID examId, AddExamTagParam param, Object state) {
        if (!TagPropertyUtils.isPrimaryTag(param.getProperties())) {
            // 不是主要别名，无约束
            return TagConstraintResult.success();
        }

        AliasConstraintState aliasState = (AliasConstraintState) state;
        if (aliasState.hasExistingPrimaryAlias || aliasState.hasPrimaryAliasInCurrentBatch) {
            return TagConstraintResult.failure(
                String.format("试卷 %s 已存在 isPrimary=true 的 ALIAS 标签，跳过添加: %s",
                    examId, param.getValue())
            );
        }

        return TagConstraintResult.success();
    }
    
    @Override
    public Object updateStateAfterAdd(AddExamTagParam param, Object state) {
        AliasConstraintState aliasState = (AliasConstraintState) state;
        if (TagPropertyUtils.isPrimaryTag(param.getProperties())) {
            aliasState.hasPrimaryAliasInCurrentBatch = true;
        }
        return aliasState;
    }
    

    
    /**
     * ALIAS 约束状态
     */
    static class AliasConstraintState {
        boolean hasExistingPrimaryAlias;
        boolean hasPrimaryAliasInCurrentBatch;

        AliasConstraintState(boolean hasExistingPrimaryAlias, boolean hasPrimaryAliasInCurrentBatch) {
            this.hasExistingPrimaryAlias = hasExistingPrimaryAlias;
            this.hasPrimaryAliasInCurrentBatch = hasPrimaryAliasInCurrentBatch;
        }
    }
}

/*
 * 扩展示例：如何添加新的标签约束
 *
 * 1. 实现 TagConstraintStrategy 接口
 * 2. 在 TagConstraintValidator 构造函数中注册策略
 *
 * 示例：名校试卷标签约束（假设每个试卷最多只能有3个名校试卷标签）
 *
 * class EliteSchoolConstraintStrategy implements TagConstraintStrategy {
 *
 *     @Override
 *     public Object initializeState(List<MathExamTag> existingTags) {
 *         long count = existingTags.stream()
 *             .filter(tag -> tag.getType() == ExamTagType.ELITE_SCHOOL_EXAM_PAGERS)
 *             .count();
 *         return new EliteSchoolConstraintState((int) count, 0);
 *     }
 *
 *     @Override
 *     public TagConstraintResult validateTag(UUID examId, AddExamTagParam param, Object state) {
 *         EliteSchoolConstraintState eliteState = (EliteSchoolConstraintState) state;
 *         int totalCount = eliteState.existingCount + eliteState.currentBatchCount;
 *
 *         if (totalCount >= 3) {
 *             return TagConstraintResult.failure(
 *                 String.format("试卷 %s 的名校试卷标签已达到上限(3个)，跳过添加: %s",
 *                     examId, param.getValue())
 *             );
 *         }
 *
 *         return TagConstraintResult.success();
 *     }
 *
 *     @Override
 *     public Object updateStateAfterAdd(AddExamTagParam param, Object state) {
 *         EliteSchoolConstraintState eliteState = (EliteSchoolConstraintState) state;
 *         eliteState.currentBatchCount++;
 *         return eliteState;
 *     }
 *
 *     static class EliteSchoolConstraintState {
 *         int existingCount;
 *         int currentBatchCount;
 *
 *         EliteSchoolConstraintState(int existingCount, int currentBatchCount) {
 *             this.existingCount = existingCount;
 *             this.currentBatchCount = currentBatchCount;
 *         }
 *     }
 * }
 */
