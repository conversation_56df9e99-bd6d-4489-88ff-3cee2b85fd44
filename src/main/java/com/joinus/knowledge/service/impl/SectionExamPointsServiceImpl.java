package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.model.entity.SectionExamPoints;
import com.joinus.knowledge.service.SectionExamPointsService;
import com.joinus.knowledge.mapper.SectionExamPointsMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【section_exam_points】的数据库操作Service实现
* @createDate 2025-03-06 16:06:22
*/
@Service
public class SectionExamPointsServiceImpl extends ServiceImpl<SectionExamPointsMapper, SectionExamPoints>
    implements SectionExamPointsService{

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createAssociation(UUID sectionId, UUID examPointId) {
        // 先检查是否已存在
        SectionExamPoints relation = getOne(
            lambdaQuery().eq(SectionExamPoints::getSectionId, sectionId)
                        .eq(SectionExamPoints::getExamPointId, examPointId));
        
        if (relation != null) {
            // 已存在则返回成功
            return true;
        }
        
        // 创建新实体并保存
        SectionExamPoints newRelation = new SectionExamPoints();
        newRelation.setSectionId(sectionId);
        newRelation.setExamPointId(examPointId);
        return save(newRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateAssociationsBySectionId(UUID sectionId, List<UUID> examPointIds) {
        // 删除该小节的所有关联
        remove(lambdaQuery().eq(SectionExamPoints::getSectionId, sectionId));
        
        // 没有新的关联
        if (examPointIds == null || examPointIds.isEmpty()) {
            return true;
        }
        
        // 添加新的关联
        List<SectionExamPoints> relations = new ArrayList<>();
        for (UUID examPointId : examPointIds) {
            SectionExamPoints relation = new SectionExamPoints();
            relation.setSectionId(sectionId);
            relation.setExamPointId(examPointId);
            relations.add(relation);
        }
        
        return saveBatch(relations);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateAssociationsByExamPointId(UUID examPointId, List<UUID> sectionIds) {
        // 删除该考点的所有关联
        remove(lambdaQuery().eq(SectionExamPoints::getExamPointId, examPointId));
        
        // 没有新的关联
        if (sectionIds == null || sectionIds.isEmpty()) {
            return true;
        }
        
        // 添加新的关联
        List<SectionExamPoints> relations = new ArrayList<>();
        for (UUID sectionId : sectionIds) {
            SectionExamPoints relation = new SectionExamPoints();
            relation.setSectionId(sectionId);
            relation.setExamPointId(examPointId);
            relations.add(relation);
        }
        
        return saveBatch(relations);
    }

    @Override
    public boolean deleteAssociation(UUID sectionId, UUID examPointId) {
        return remove(lambdaQuery()
                    .eq(SectionExamPoints::getSectionId, sectionId)
                    .eq(SectionExamPoints::getExamPointId, examPointId));
    }

    @Override
    public boolean deleteAssociationsBySectionId(UUID sectionId) {
        return remove(lambdaQuery().eq(SectionExamPoints::getSectionId, sectionId));
    }

    @Override
    public boolean deleteAssociationsByExamPointId(UUID examPointId) {
        return remove(lambdaQuery().eq(SectionExamPoints::getExamPointId, examPointId));
    }

    @Override
    public List<UUID> getExamPointIdsBySectionId(UUID sectionId) {
        List<SectionExamPoints> relations = list(lambdaQuery().eq(SectionExamPoints::getSectionId, sectionId));
        return relations.stream()
                .map(SectionExamPoints::getExamPointId)
                .collect(Collectors.toList());
    }

    @Override
    public List<UUID> getSectionIdsByExamPointId(UUID examPointId) {
        List<SectionExamPoints> relations = list(lambdaQuery().eq(SectionExamPoints::getExamPointId, examPointId));
        return relations.stream()
                .map(SectionExamPoints::getSectionId)
                .collect(Collectors.toList());
    }
}
