package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.SectionQuestionType;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.po.SectionQuestionTypePO;
import jakarta.validation.constraints.NotNull;

import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_section_question_types】的数据库操作Service
* @createDate 2025-03-06 16:06:22
*/
public interface SectionQuestionTypesService extends IService<SectionQuestionType> {

    void createRelation(UUID sectionId, UUID questionTypeId, Integer pageIndex);

    void deleteRelation(UUID sectionId, UUID questionTypeId, Integer pageIndex);

    boolean createAssociation(@NotNull(message = "sectionId不能为空") UUID sectionId, @NotNull(message = "keyPointId不能为空") UUID keyPointId);
    
    /**
     * 删除题型与小节的关联
     */
    void removeEntity(SectionQuestionTypePO sectionQt);
}
