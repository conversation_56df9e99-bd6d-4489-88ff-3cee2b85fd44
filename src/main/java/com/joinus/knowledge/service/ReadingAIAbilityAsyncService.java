package com.joinus.knowledge.service;

import com.joinus.knowledge.model.param.ReadingAIAbilityParam;

import java.math.BigDecimal;

public interface ReadingAIAbilityAsyncService {

    /**
     * AI批改
     *
     * @param testId     测试ID
     * @param questionId 题目ID
     * @param answer     用户回答
     */
    BigDecimal aiCorrect(String testId, String questionId, String answer);

    /**
     * 薄弱知识点分析
     *
     * @param param
     */
    void weakKnowledgePointAnalysis(ReadingAIAbilityParam param);

    /**
     * 综合训练建议
     *
     * @param param
     */
    void trainingSuggestion(ReadingAIAbilityParam param);

    /**
     * 薄弱题型分析
     *
     * @param param
     */
    void weakQuestionTypeAnalysis(ReadingAIAbilityParam param);

    /**
     * 为语文阅读提供AI能力
     * @param param
     */
    void dealByAIAbility(ReadingAIAbilityParam param);
}
