package com.joinus.knowledge.service;

import com.joinus.knowledge.model.dto.QuestionTypesMappingDTO;
import com.joinus.knowledge.model.entity.QuestionTypesMapping;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_type_questions】的数据库操作Service
* @createDate 2025-03-01 10:22:43
*/
public interface QuestionTypesMappingService extends IService<QuestionTypesMapping> {
    
    /**
     * 创建题目和题型的关联
     */
    boolean createAssociation(UUID questionId, UUID questionTypeId);
    
    /**
     * 批量创建题目和题型的关联（一个题目关联多个题型）
     */
    boolean batchCreateAssociationsByQuestionId(UUID questionId, List<UUID> questionTypeIds);
    
    /**
     * 批量创建题目和题型的关联（一个题型关联多个题目）
     */
    boolean batchCreateAssociationsByQuestionTypeId(UUID questionTypeId, List<UUID> questionIds);
    
    /**
     * 删除题目和题型的关联
     */
    boolean deleteAssociation(UUID questionId, UUID questionTypeId);
    
    /**
     * 根据题目ID删除所有关联
     */
    boolean deleteAssociationsByQuestionId(UUID questionId);
    
    /**
     * 根据题型ID删除所有关联
     */
    boolean deleteAssociationsByQuestionTypeId(UUID questionTypeId);
    
    /**
     * 根据题目ID获取所有关联的题型ID
     */
    List<UUID> listQuestionTypeIdsByQuestionId(UUID questionId);

    /**
     * 根据题型ID获取所有关联的题目ID
     */
    List<UUID> listQuestionIdsByQuestionTypeId(UUID questionTypeId);

    List<QuestionTypesMapping> listQuestionTypeMappings(List<UUID> questionIds);

    /**
     * 根据题目ID获取所有关联的题型
     */
    List<QuestionTypesMappingDTO> listQuestionTypesMappingDTOByQuestionId(UUID questionId);

}
