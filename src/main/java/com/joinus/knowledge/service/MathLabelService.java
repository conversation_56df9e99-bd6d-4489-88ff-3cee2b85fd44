package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.MathLabel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.param.MathQuestionErrorLabelParam;
import com.joinus.knowledge.model.vo.MathLabelVO;
import com.joinus.knowledge.model.vo.MathQuestionErrorLabelVO;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_labels(标签表)】的数据库操作Service
* @createDate 2025-03-26 15:05:21
*/
public interface MathLabelService extends IService<MathLabel> {

    List<MathQuestionErrorLabelVO> listErrorLabels(UUID id);

    List<Map<String, List<MathQuestionErrorLabelVO>>> listErrorLabelsV1(UUID id);

    List<String> listCommonLabels(UUID id);

    List<MathQuestionErrorLabelVO> updateErrorLabels(UUID id, List<MathQuestionErrorLabelParam> param);

    List<Map<String, List<MathQuestionErrorLabelVO>>> updateErrorLabelsV1(UUID id, List<MathQuestionErrorLabelParam> param);

    List<MathLabel> listKnowledgeDomain();

    List<MathLabelVO> getLabelsWithNull();

    List<MathLabelVO> listCommonLabelsV1(UUID id);

    List<MathLabelVO> getLabelsWithNullV1();
}
