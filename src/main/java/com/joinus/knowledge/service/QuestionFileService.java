package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.QuestionFileType;
import com.joinus.knowledge.model.entity.QuestionFile;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_files】的数据库操作Service
* @createDate 2025-03-11 18:31:25
*/
public interface QuestionFileService extends IService<QuestionFile> {

    void saveOrUpdateBatch(UUID id, List<String> files);

    List<QuestionFile> selectByQuestionId(UUID questionId);

    void removeByQuestionId(UUID questionId);

    List<UUID> saveQuestionFileAndRelation(List<String> objectNames, UUID questionId, OssEnum ossEnum, QuestionFileType questionFileType);
}
