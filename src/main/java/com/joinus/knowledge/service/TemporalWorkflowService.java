package com.joinus.knowledge.service;

import com.joinus.knowledge.model.param.AnalyzeExamParam;

public interface TemporalWorkflowService {

    void extractInfoFromImageByLLM(String bookId);

    void analyzeExam(AnalyzeExamParam examParam);

    void cutExam(AnalyzeExamParam examParam);

    void analyzeKnowledgePointsForExam(AnalyzeExamParam examParam);

    void knowledgeDomainLabel(Integer count);

    void generateQuestionBatch(String labelName, Integer count, String publisher);

    void imageOcr(String bookId);

    void reanalyzeExamKnowledgePoint(AnalyzeExamParam param);
}
