package com.joinus.knowledge.service;

import com.joinus.knowledge.model.dto.DerivativeFileDTO;
import com.joinus.knowledge.model.dto.TextbookFileDTO;
import com.joinus.knowledge.model.entity.FileDerivative;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【file_derivatives】的数据库操作Service
* @createDate 2025-03-06 22:57:14
*/
public interface FileDerivativeService extends IService<FileDerivative> {

    List<DerivativeFileDTO> listByTextbookId(UUID bookId);
}
