package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.MathExamFiles;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.vo.FileVO;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_exam_files(试卷与文件关联表)】的数据库操作Service
* @createDate 2025-03-20 18:15:37
*/
public interface MathExamFilesService extends IService<MathExamFiles> {

    void removeByExamId(UUID id);

    List<FileVO> listFilesByExamId(UUID id);
}
