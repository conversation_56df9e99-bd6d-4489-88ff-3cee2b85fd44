package com.joinus.knowledge.service;

import java.io.IOException;

/**
 * 百度语音识别服务
 *
 * <AUTHOR>
 * @date 2025/3/27 15:38
 */
public interface SpeechAsrService {
    /**
     * 百度语音识别
     *
     * @param path 文件路径
     * @return
     * @throws IOException
     */
    String baiduSpeechAsr(String path) throws IOException;

    /**
     * 阿里语音识别
     *
     * @param path 文件路径
     * @return
     * @throws IOException
     */
    String aliSpeechAsr(String path) throws IOException;
}
