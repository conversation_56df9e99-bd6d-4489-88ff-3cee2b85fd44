package com.joinus.knowledge.service;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.dto.QuestionKnowledgePointDTO;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.entity.QuestionKnowledgePoint;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_questions】的数据库操作Service
* @createDate 2025-03-01 10:22:43
*/
public interface QuestionKnowledgePointsService extends IService<QuestionKnowledgePoint> {
    
    /**
     * 创建题目和知识点的关联
     */
    boolean createAssociation(UUID questionId, UUID knowledgePointId);
    
    /**
     * 批量创建题目和知识点的关联（一个题目关联多个知识点）
     */
    boolean batchCreateAssociationsByQuestionId(UUID questionId, List<UUID> knowledgePointIds);
    
    /**
     * 批量创建题目和知识点的关联（一个知识点关联多个题目）
     */
    boolean batchCreateAssociationsByKnowledgePointId(UUID knowledgePointId, List<UUID> questionIds);
    
    /**
     * 删除题目和知识点的关联
     */
    boolean deleteAssociation(UUID questionId, UUID knowledgePointId);
    
    /**
     * 根据题目ID删除所有关联
     */
    boolean deleteAssociationsByQuestionId(UUID questionId);
    
    /**
     * 根据知识点ID删除所有关联
     */
    boolean deleteAssociationsByKnowledgePointId(UUID knowledgePointId);
    
    /**
     * 根据题目ID获取所有关联的知识点ID
     */
    List<UUID> getKnowledgePointIdsByQuestionId(UUID questionId);
    
    /**
     * 根据知识点ID获取所有关联的题目ID
     */
    List<UUID> listQuestionIdsByKnowledgePointId(UUID knowledgePointId);


    List<MathKnowledgePoint> listKnowledgePointByQuestionId(UUID questionId, Integer grade, Integer semester, PublisherType publisher);

    UUID getSimilarQuestion(UUID questionId);

    List<QuestionKnowledgePointDTO> listKnowledgePointDTOByQuestionId(UUID questionId);

}
