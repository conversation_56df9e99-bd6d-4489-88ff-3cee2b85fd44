package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.QuestionExamPoints;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【question_exam_points】的数据库操作Service
* @createDate 2025-03-01 10:22:43
*/
public interface QuestionExamPointsService extends IService<QuestionExamPoints> {
    
    /**
     * 创建题目和考点的关联
     */
    boolean createAssociation(UUID questionId, UUID examPointId);
    
    /**
     * 批量创建题目和考点的关联（一个题目关联多个考点）
     */
    boolean batchCreateAssociationsByQuestionId(UUID questionId, List<UUID> examPointIds);
    
    /**
     * 批量创建题目和考点的关联（一个考点关联多个题目）
     */
    boolean batchCreateAssociationsByExamPointId(UUID examPointId, List<UUID> questionIds);
    
    /**
     * 删除题目和考点的关联
     */
    boolean deleteAssociation(UUID questionId, UUID examPointId);
    
    /**
     * 根据题目ID删除所有关联
     */
    boolean deleteAssociationsByQuestionId(UUID questionId);
    
    /**
     * 根据考点ID删除所有关联
     */
    boolean deleteAssociationsByExamPointId(UUID examPointId);
    
    /**
     * 根据题目ID获取所有关联的考点ID
     */
    List<UUID> getExamPointIdsByQuestionId(UUID questionId);
    
    /**
     * 根据考点ID获取所有关联的题目ID
     */
    List<UUID> getQuestionIdsByExamPointId(UUID examPointId);
}
