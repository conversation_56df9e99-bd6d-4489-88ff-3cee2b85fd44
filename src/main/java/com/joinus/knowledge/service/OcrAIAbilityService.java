package com.joinus.knowledge.service;

import com.joinus.knowledge.model.param.OcrHandWritingParam;
import com.joinus.knowledge.model.vo.OcrHandWritingVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * OCR AI能力接口
 */
public interface OcrAIAbilityService {

    /**
     * 识别手写文本并返回识别结果列表。
     *
     * @param param 包含手写文本识别所需参数的对象
     * @return 包含识别结果的OcrHandWritingVO对象列表
     */
    List<OcrHandWritingVO> recognizeHandwrittenText(@Valid OcrHandWritingParam param);


}

