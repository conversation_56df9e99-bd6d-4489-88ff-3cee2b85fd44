package com.joinus.knowledge.service;

import com.joinus.knowledge.model.param.SolveQuestionParam;
import reactor.core.publisher.Mono;

/**
 * 流式内容存储服务
 * 用于处理流式响应内容的存储，支持多种存储目标
 */
public interface StreamContentStorageService {
    
    /**
     * 存储流式响应内容
     * 
     * @param requestId 请求ID
     * @param param 请求参数
     * @param content 完整的响应内容
     * @param processingTime 处理时间（毫秒）
     * @param sourceIp 请求来源IP
     * @param sourcePath 请求来源路径
     * @return 存储结果
     */
    Mono<String> storeContent(
        String requestId, 
        SolveQuestionParam param,
        String content, 
        long processingTime,
        String sourceIp,
        String sourcePath
    );
}
