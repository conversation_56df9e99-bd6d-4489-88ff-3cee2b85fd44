package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.dto.ImageData;
import com.joinus.knowledge.model.entity.MathAnswerFiles;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_answer_files(答案与文件关联表)】的数据库操作Service
* @createDate 2025-05-22 18:38:54
*/
public interface MathAnswerFilesService extends IService<MathAnswerFiles> {

    List<UUID> saveAnswerFileAndRelation(List<ImageData> imageDataList, UUID answerId);

    void removeByAnswerId(UUID answerId);
}
