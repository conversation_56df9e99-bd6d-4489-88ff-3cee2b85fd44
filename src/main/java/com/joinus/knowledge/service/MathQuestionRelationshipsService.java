package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.MathQuestionRelationships;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_relationships】的数据库操作Service
* @createDate 2025-03-29 10:18:30
*/
public interface MathQuestionRelationshipsService extends IService<MathQuestionRelationships> {

    MathQuestionRelationships getParentBookQuestion(UUID id);

    List<MathQuestionRelationships> getDerivativeAiBookQuestion(UUID id);
}
