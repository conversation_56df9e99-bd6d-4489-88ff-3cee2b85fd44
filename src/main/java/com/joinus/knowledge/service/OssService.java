package com.joinus.knowledge.service;

import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.vo.OssFileVO;

import java.io.IOException;
import java.io.InputStream;

public interface OssService {


    OssFileVO uploadInputStreamToOss(OssEnum ossEnum, String businessType, String ossKey, InputStream inputStream, long size) throws IOException;

    String getPresignedDownloadUrl(OssEnum ossEnum, String ossKey);

    OssFileVO getPresignedInfo(OssEnum ossEnum, String ossKey);
}
