package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.QuestionGraphicsScript;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.param.GraphicsSubmitParam;
import com.joinus.knowledge.model.param.QuestionGraphicsScriptParam;

import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_graphics_scripts(问题图案生成脚本)】的数据库操作Service
* @createDate 2025-04-17 16:04:14
*/
public interface QuestionGraphicsScriptService extends IService<QuestionGraphicsScript> {

    Integer claimScripts(Integer count, String username);

    void submit(GraphicsSubmitParam graphicsSubmitParam);

    void ignore(UUID questionId, String remark);

    void verify(UUID questionId, boolean verified, String remark);
}
