package com.joinus.knowledge.utils;

import com.joinus.knowledge.enums.PromptEnum;
import com.joinus.knowledge.model.entity.PromptTemplate;
import com.joinus.knowledge.service.PromptTemplateService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class PromptUtils {

    @Resource
    private PromptTemplateService promptTemplateService;

    private final ConcurrentHashMap<String, String> promptContentMap = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, PromptTemplate> promptTemplateMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        List<PromptTemplate> promptTemplates = promptTemplateService.list();
        promptTemplates.forEach(promptTemplate -> {
            promptContentMap.put(promptTemplate.getName(), promptTemplate.getContent());
            promptTemplateMap.put(promptTemplate.getName(), promptTemplate);
        });
    }

    public String getPromptTemplate(PromptEnum promptEnum) {
        return promptContentMap.get(promptEnum.toString());
    }


    public PromptTemplate getPromptTemplateEntity(PromptEnum promptEnum) {
        return promptTemplateMap.get(promptEnum.toString());
    }
}
