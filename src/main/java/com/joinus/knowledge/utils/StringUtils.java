package com.joinus.knowledge.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class StringUtils {

    private static final String IMG_SRC_REGEX = "(<img[^>]*?data-s3-key=\"[^\"]*\"[^>]*?src=\")[^\"]*(\")";
    // 匹配有data-s3-key的img标签，data-s3-enum是可选的，并处理属性之间可能存在的逗号
    private static final String IMG_DATA_PATTERN = "<img[^>]*data-s3-enum=\"([^\"]*)\"[^>]*data-s3-key=\"([^\"]*)\"[^>]*src=\"\"[^>]*/>|<img[^>]*data-s3-key=\"([^\"]*)\"[^>]*src=\"\"[^>]*/>";

    public static Pattern emptyPattern = Pattern.compile(IMG_DATA_PATTERN);

    public static String encodeContent(String content) {
        if (content == null) {
            return "";
        }
        // 将img标签中的src属性值替换为空，保留src=""
        return content.replaceAll(IMG_SRC_REGEX, "$1$2");
    }
    
    /**
     * 根据data-s3-enum和data-s3-key生成src值
     * 
     * @param s3Enum OssEnum值
     * @param s3Key 对象存储键值
     * @return 生成的src值
     */
    private static String generateSrcFromS3Data(String s3Enum, String s3Key) {
        try {

            // 根据OssEnum和s3Key生成URL
            // 这里只是一个示例，实际实现可能需要根据具体业务逻辑调整
            return "/api/oss/" + s3Enum + "/" + s3Key;
        } catch (IllegalArgumentException e) {
            log.warn("Invalid OssEnum value: {}", s3Enum);
            // 如果s3Enum不是有效的OssEnum值，返回一个基本的URL
            return "/api/oss/default/" + s3Key;
        }
    }

    public static void main(String[] args) {
        // 测试字符串
        String content = "aaaa , bbbb, <img data-s3-enum=\"MATH_QUESTION\" data-s3-key=\"test.png1\" src=\"111111\" />......\n" +
                "<img data-s3-key=\"test.png2\" src=\"222222\" />.....";
        // 加密内容
        String encodeContent = encodeContent(content);
        System.out.println("加密：" + encodeContent);
        
        // 使用新的解密方法
//        String decodeContent = decodeContent(encodeContent);
//        System.out.println("新方法解密：" + decodeContent);
        
        // 使用原始的解密方法
        String decodeContentOriginal = decodeContent(encodeContent);
        System.out.println("原始方法解密：" + decodeContentOriginal);
    }


    /**
     * 解密内容，使用data-s3-enum和data-s3-key生成src值
     * 这个方法使用改进的正则表达式，可以处理有或没有data-s3-enum的情况
     * 
     * @param content 要解密的内容
     * @return 解密后的内容
     */
    public static String decodeContentV2(String content) {
        if (content == null) {
            return "";
        }
        
        // 匹配所有src为空且包含data-s3-key的img标签，data-s3-enum是可选的
        Matcher matcher = emptyPattern.matcher(content);

        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            // 获取data-s3-enum和data-s3-key属性值
            String s3Enum = matcher.group(1); // data-s3-enum可能为null
            String s3Key = null;
            
            // 如果第一个模式匹配（包含data-s3-enum和data-s3-key）
            if (s3Enum != null) {
                s3Key = matcher.group(2);
            } 
            // 如果第二个模式匹配（只有data-s3-key）
            else {
                s3Key = matcher.group(3);
                s3Enum = "aliyun"; // 默认值
            }
            
            if (s3Key != null && !s3Key.isEmpty()) {
                // 根据规则生成新的src值
                String newSrc = generateSrcFromS3Data(s3Enum, s3Key);
                
                // 替换空的src属性为新生成的值
                String replacement = matcher.group().replace("src=\"\"", "src=\"" + newSrc + "\"");
                matcher.appendReplacement(sb, replacement.replace("$", "\\$").replace("\\", "\\\\"));
            }
        }
        
        matcher.appendTail(sb);
        return sb.toString();
    }


    private static Pattern imgTagPattern = Pattern.compile("<img[^>]*src=\"\"[^>]*>");
    private static Pattern dataS3EnumPattern = Pattern.compile("data-s3-enum=\"([^\"]*)\"");
    private static Pattern dataS3KeyPattern = Pattern.compile("data-s3-key=\"([^\"]*)\"");
    /**
     * 原始的解密方法，但增加了对data-s3-enum的提取和处理
     * 
     * @param content 要解密的内容
     * @return 解密后的内容
     */
    public static String decodeContent(String content) {
        if (content == null) {
            return "";
        }


        Matcher imgMatcher = imgTagPattern.matcher(content);
        StringBuffer sb = new StringBuffer();

        while (imgMatcher.find()) {
            String tag = imgMatcher.group();

            // 提取 data-s3-enum 属性（如果存在）
            Matcher enumMatcher = dataS3EnumPattern.matcher(tag);
            String s3Enum = enumMatcher.find() ? enumMatcher.group(1) : "aliyun";  // 默认 aliyun

            // 提取 data-s3-key 属性（必须有）
            Matcher keyMatcher = dataS3KeyPattern.matcher(tag);
            if (!keyMatcher.find()) {
                continue; // 或根据需求作处理
            }
            String s3Key = keyMatcher.group(1);

            // 根据修正后的 s3Enum 和 s3Key 生成新的 src
            String newSrc = generateSrcFromS3Data(s3Enum, s3Key);
            String newTag = tag.replace("src=\"\"", "src=\"" + newSrc + "\"");

            imgMatcher.appendReplacement(sb, newTag.replace("$", "\\$")); // 注意替换 $ 符号避免干扰
        }
        imgMatcher.appendTail(sb);

        return sb.toString();
    }

}