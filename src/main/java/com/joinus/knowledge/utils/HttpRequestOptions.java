package com.joinus.knowledge.utils;

import cn.hutool.http.Method;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * HttpRequestOptions类用于封装HTTP请求的相关参数
 * 它是一个泛型类，允许通过<T, R>指定请求体和响应体的类型
 */
@Data
@Builder
public class HttpRequestOptions<T, R> {

    /**
     * 请求的URL地址
     */
    private String url;

    /**
     * HTTP请求方法，如GET、POST等
     */
    private Method method;

    /**
     * 请求的头信息，以键值对的形式存储
     */
    private Map<String, String> headers;

    /**
     * 查询参数，以键值对的形式存储
     */
    private Map<String, Object> queryParams;

    /**
     * 请求体参数，其类型由泛型T指定
     */
    private T bodyParams;

    /**
     * 响应体的类型，由泛型R指定，用于解析响应
     */
    private Class<R> responseType;

}
