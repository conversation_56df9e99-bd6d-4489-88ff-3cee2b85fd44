package com.joinus.knowledge.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Base64;

@Slf4j
public class Base64Utils {

    /**
     * 将URL指向的图片转换为Base64编码的字符串
     *
     * @param imageUrl 图片的URL地址
     * @return 图片的Base64编码字符串，如果转换失败则返回null
     */
    public static String convertImageUrlToBase64(String imageUrl) {
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            log.error("图片URL不能为空");
            return null;
        }

        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = null;

        try {
            // 创建URL对象
            URL url = new URL(imageUrl);

            // 打开连接
            URLConnection connection = url.openConnection();
            connection.setConnectTimeout(10000); // 设置连接超时为10秒
            connection.setReadTimeout(30000);    // 设置读取超时为30秒

            // 获取输入流
            inputStream = connection.getInputStream();
            outputStream = new ByteArrayOutputStream();

            byte[] buffer = new byte[4096];
            int bytesRead;

            // 读取图片数据
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 将图片数据转换为Base64字符串
            byte[] imageBytes = outputStream.toByteArray();
            return Base64.getEncoder().encodeToString(imageBytes);

        } catch (MalformedURLException e) {
            log.error("URL格式不正确: {}", imageUrl);
            return null;
        } catch (IOException e) {
            log.error("读取图片数据时发生IO异常: {}", imageUrl);
            return null;
        } catch (OutOfMemoryError e) {
            log.error("图片过大导致内存溢出: {}", imageUrl);
            return null;
        } catch (Exception e) {
            log.error("转换图片到Base64时发生未知异常: {}", imageUrl);
            return null;
        } finally {
            // 关闭资源
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                log.error("关闭流资源时发生异常", e);
            }
        }
    }
    
    // 测试方法
    public static void main(String[] args) {
        String imageUrl = "";
        String base64Image = convertImageUrlToBase64(imageUrl);
        System.out.println("Base64编码结果: " + base64Image);
    }
}