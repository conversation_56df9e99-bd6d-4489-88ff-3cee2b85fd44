package com.joinus.knowledge.utils;

import lombok.extern.slf4j.Slf4j;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;

/**
 * Markdown 工具类，用于将 Markdown 转换为 HTML
 */
@Slf4j
public class MarkdownUtils {

    private static final Parser PARSER = Parser.builder().build();
    private static final HtmlRenderer HTML_RENDERER = HtmlRenderer.builder().build();

    /**
     * 将 Markdown 文本转换为 HTML
     *
     * @param markdown Markdown 文本
     * @return HTML 文本
     */
    public static String toHtml(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return "";
        }
        
        try {
            Node document = PARSER.parse(markdown);
            return HTML_RENDERER.render(document);
        } catch (Exception e) {
            log.error("Markdown 转 HTML 失败", e);
            return markdown; // 转换失败时返回原始文本
        }
    }
}
