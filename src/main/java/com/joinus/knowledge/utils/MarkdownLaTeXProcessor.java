package com.joinus.knowledge.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Markdown 中 LaTeX 公式处理工具类
 */
@Slf4j
public class MarkdownLaTeXProcessor {

    // 行内公式匹配模式：$...$
    private static final Pattern INLINE_LATEX_PATTERN = Pattern.compile("\\$(.*?)\\$");
    
    // 块级公式匹配模式：$$...$$
    private static final Pattern BLOCK_LATEX_PATTERN = Pattern.compile("\\$\\$(.*?)\\$\\$", Pattern.DOTALL);
    
    // 行内公式匹配模式：\(...\)
    private static final Pattern INLINE_PAREN_LATEX_PATTERN = Pattern.compile("\\\\\\((.*?)\\\\\\)");
    
    // 块级公式匹配模式：\[...\]
    private static final Pattern BLOCK_BRACKET_LATEX_PATTERN = Pattern.compile("\\\\\\[(.*?)\\\\\\]", Pattern.DOTALL);
    
    // AsciiMath 公式匹配模式：`...`
    private static final Pattern ASCII_MATH_PATTERN = Pattern.compile("`(.*?)`");
    
    /**
     * 将 Markdown 文本中的 LaTeX 公式转换为 SVG 并嵌入到 HTML 中
     *
     * @param markdown Markdown 文本
     * @param useImgTag 是否使用 img 标签
     * @return 处理后的 HTML 文本
     */
    public static String processToHtml(String markdown, boolean useImgTag) {
        if (markdown == null || markdown.isEmpty()) {
            return "";
        }
        
        try {
            // 预处理Markdown文本，处理换行符
            String processed = preprocessMarkdown(markdown);
            
            // 处理所有公式格式
            
            // 处理块级公式
            processed = processBlockLaTeX(processed, useImgTag);              // $$...$$
            processed = processBlockBracketLaTeX(processed, useImgTag);       // \[...\]
            
            // 处理行内公式
            processed = processInlineLaTeX(processed, useImgTag);             // $...$
            processed = processInlineParenLaTeX(processed, useImgTag);        // \(...\)
            
            // 处理 AsciiMath 公式
            processed = processAsciiMath(processed, useImgTag);               // `...`
            
            // 在Markdown转HTML前，先还原所有特殊标记为标准形式
            processed = restoreSpecialMarkers(processed);
            
            // 最后将 Markdown 转换为 HTML
            String html = MarkdownUtils.toHtml(processed);
            return postProcessHtml(html);
        } catch (Exception e) {
            log.error("处理 Markdown 中的 LaTeX 公式失败", e);
            return MarkdownUtils.toHtml(markdown); // 处理失败时直接转换为 HTML
        }
    }
    
    /**
     * 预处理Markdown文本，标准化换行符格式
     */
    private static String preprocessMarkdown(String markdown) {
        // 统一换行符为\n
        markdown = markdown.replace("\r\n", "\n").replace("\r", "\n");
        
        // 将文本中的\\n转换为特殊标记以避免与实际换行符混淆
        markdown = markdown.replace("\\n", "___ESCAPED_NEWLINE___");
        
        // 对连续的换行符进行预处理，避免影响LaTeX解析
        markdown = markdown.replace("\n\n", "___DOUBLE_NEWLINE___");
        
        return markdown;
    }
    
    /**
     * 恢复特殊标记为标准形式的文本
     */
    private static String restoreSpecialMarkers(String text) {
        // 恢复特殊的换行符标记为实际的换行符
        text = text.replace("___ESCAPED_NEWLINE___", "\n");
        text = text.replace("___DOUBLE_NEWLINE___", "\n\n");
        
        return text;
    }
    
    /**
     * 处理块级 LaTeX 公式 ($$...$$)
     */
    private static String processBlockLaTeX(String markdown, boolean useImgTag) {
        Matcher matcher = BLOCK_LATEX_PATTERN.matcher(markdown);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String latex = matcher.group(1).trim();
            
            // 恢复任何预处理过的换行符
            latex = latex.replace("___DOUBLE_NEWLINE___", "\n");
            
            // 处理LaTeX内的特殊换行符标记
            // 注意：LaTeX中的\\通常表示换行，需要特别处理
            latex = latex.replace("___ESCAPED_NEWLINE___", "\\\\");
            
            String replacement;
            
            try {
                if (useImgTag) {
                    replacement = LaTeXToSVGConverter.convertToImgTag(latex, 15);
                } else {
                    replacement = LaTeXToSVGConverter.convertToSVG(latex, 15);
                }
                
                // 检查结果，确保转换成功
                if (replacement.isEmpty() || 
                    (useImgTag && replacement.contains("src=\"data:image/svg+xml;base64,\""))) {
                    log.warn("LaTeX转SVG结果为空或无效: {}", latex);
                    // 当转换失败时，保留原始LaTeX以便后续调试
                    replacement = "$$" + latex + "$$";
                }
            } catch (Exception e) {
                log.error("处理块级公式失败: {}", latex, e);
                replacement = "$$" + latex + "$$";
            }
            
            // 替换特殊字符，避免 Matcher.appendReplacement 出错
            replacement = Matcher.quoteReplacement(replacement);
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 处理块级 LaTeX 公式 (\[...\])
     */
    private static String processBlockBracketLaTeX(String markdown, boolean useImgTag) {
        Matcher matcher = BLOCK_BRACKET_LATEX_PATTERN.matcher(markdown);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String latex = matcher.group(1).trim();
            
            // 恢复任何预处理过的换行符
            latex = latex.replace("___DOUBLE_NEWLINE___", "\n");
            
            // 处理LaTeX内的特殊换行符标记
            // 注意：LaTeX中的\\通常表示换行，需要特别处理
            latex = latex.replace("___ESCAPED_NEWLINE___", "\\\\");
            
            String replacement;
            
            try {
                if (useImgTag) {
                    replacement = LaTeXToSVGConverter.convertToImgTag(latex, 15);
                } else {
                    replacement = LaTeXToSVGConverter.convertToSVG(latex, 15);
                }
                
                // 检查结果，确保转换成功
                if (replacement.isEmpty() || 
                    (useImgTag && replacement.contains("src=\"data:image/svg+xml;base64,\""))) {
                    log.warn("LaTeX转SVG结果为空或无效: {}", latex);
                    // 当转换失败时，保留原始LaTeX以便后续调试
                    replacement = "\\[" + latex + "\\]";
                }
            } catch (Exception e) {
                log.error("处理块级公式失败: {}", latex, e);
                replacement = "\\[" + latex + "\\]";
            }
            
            // 替换特殊字符，避免 Matcher.appendReplacement 出错
            replacement = Matcher.quoteReplacement(replacement);
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 处理行内 LaTeX 公式 ($...$)
     */
    private static String processInlineLaTeX(String markdown, boolean useImgTag) {
        Matcher matcher = INLINE_LATEX_PATTERN.matcher(markdown);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String latex = matcher.group(1).trim();
            
            // 恢复任何预处理过的换行符
            latex = latex.replace("___DOUBLE_NEWLINE___", "\n");
            
            // 处理LaTeX内的特殊换行符标记
            // 注意：LaTeX中的\\通常表示换行，需要特别处理
            latex = latex.replace("___ESCAPED_NEWLINE___", "\\\\");
            
            String replacement;
            
            try {
                if (useImgTag) {
                    replacement = LaTeXToSVGConverter.convertToImgTag(latex, 15);
                } else {
                    replacement = LaTeXToSVGConverter.convertToSVG(latex, 15);
                }
                
                // 检查结果，确保转换成功
                if (replacement.isEmpty() || 
                    (useImgTag && replacement.contains("src=\"data:image/svg+xml;base64,\""))) {
                    log.warn("LaTeX转SVG结果为空或无效: {}", latex);
                    // 当转换失败时，保留原始LaTeX以便后续调试
                    replacement = "$" + latex + "$";
                }
            } catch (Exception e) {
                log.error("处理行内公式失败: {}", latex, e);
                replacement = "$" + latex + "$";
            }
            
            // 替换特殊字符，避免 Matcher.appendReplacement 出错
            replacement = Matcher.quoteReplacement(replacement);
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 处理行内 LaTeX 公式 (\(...\))
     */
    private static String processInlineParenLaTeX(String markdown, boolean useImgTag) {
        Matcher matcher = INLINE_PAREN_LATEX_PATTERN.matcher(markdown);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String latex = matcher.group(1).trim();
            
            // 恢复任何预处理过的换行符
            latex = latex.replace("___DOUBLE_NEWLINE___", "\n");
            
            // 处理LaTeX内的特殊换行符标记
            // 注意：LaTeX中的\\通常表示换行，需要特别处理
            latex = latex.replace("___ESCAPED_NEWLINE___", "\\\\");
            
            String replacement;
            
            try {
                if (useImgTag) {
                    replacement = LaTeXToSVGConverter.convertToImgTag(latex, 15);
                } else {
                    replacement = LaTeXToSVGConverter.convertToSVG(latex, 15);
                }
                
                // 检查结果，确保转换成功
                if (replacement.isEmpty() || 
                    (useImgTag && replacement.contains("src=\"data:image/svg+xml;base64,\""))) {
                    log.warn("LaTeX转SVG结果为空或无效: {}", latex);
                    // 当转换失败时，保留原始LaTeX以便后续调试
                    replacement = "\\(" + latex + "\\)";
                }
            } catch (Exception e) {
                log.error("处理行内公式失败: {}", latex, e);
                replacement = "\\(" + latex + "\\)";
            }
            
            // 替换特殊字符，避免 Matcher.appendReplacement 出错
            replacement = Matcher.quoteReplacement(replacement);
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 处理 AsciiMath 公式 (`...`)
     * 注意：我们将 AsciiMath 转换为 LaTeX 后再处理
     */
    private static String processAsciiMath(String markdown, boolean useImgTag) {
        Matcher matcher = ASCII_MATH_PATTERN.matcher(markdown);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String asciiMath = matcher.group(1).trim();
            
            // 恢复任何预处理过的换行符
            asciiMath = asciiMath.replace("___DOUBLE_NEWLINE___", "\n");
            
            // 处理LaTeX内的特殊换行符标记
            // 注意：LaTeX中的\\通常表示换行，需要特别处理
            asciiMath = asciiMath.replace("___ESCAPED_NEWLINE___", "\\\\");
            
            String replacement;
            
            try {
                // 注意：这里我们直接将 AsciiMath 作为 LaTeX 处理
                // 真正的 AsciiMath 转换需要额外的库支持
                if (useImgTag) {
                    replacement = LaTeXToSVGConverter.convertToImgTag(asciiMath, 15);
                } else {
                    replacement = LaTeXToSVGConverter.convertToSVG(asciiMath, 15);
                }
                
                // 检查结果，确保转换成功
                if (replacement.isEmpty() || 
                    (useImgTag && replacement.contains("src=\"data:image/svg+xml;base64,\""))) {
                    log.warn("LaTeX转SVG结果为空或无效: {}", asciiMath);
                    // 当转换失败时，保留原始LaTeX以便后续调试
                    replacement = "`" + asciiMath + "`";
                }
            } catch (Exception e) {
                log.error("处理 AsciiMath 公式失败: {}", asciiMath, e);
                replacement = "`" + asciiMath + "`";
            }
            
            // 替换特殊字符，避免 Matcher.appendReplacement 出错
            replacement = Matcher.quoteReplacement(replacement);
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 后处理转换结果，修复输出格式
     */
    public static String postProcessHtml(String html) {
        if (html == null || html.isEmpty()) {
            return "";
        }
        
        // 确保移除任何可能残留的特殊标记
        html = html.replace("___DOUBLE_NEWLINE___", "<br><br>");
        html = html.replace("___ESCAPED_NEWLINE___", "<br>");
        
        // 修复任何空的base64图像链接
        html = html.replace("src=\"data:image/svg+xml;base64,\"", 
            "src=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiI+PHRleHQgeD0iMCIgeT0iMTUiIGZpbGw9InJlZCI+RXJyb3I8L3RleHQ+PC9zdmc+\"");
        
        return html;
    }
    
    /**
     * 将 Markdown 文本中的 LaTeX 公式转换为 SVG 并嵌入到 HTML 中
     * 默认使用 img 标签
     *
     * @param markdown Markdown 文本
     * @return 处理后的 HTML 文本
     */
    public static String processToHtml(String markdown) {
        return processToHtml(markdown, true);
    }
}
