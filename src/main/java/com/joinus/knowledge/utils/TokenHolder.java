package com.joinus.knowledge.utils;

import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nls.client.AccessToken;
import com.joinus.knowledge.config.SpeechAsrProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * token的获取类
 * 将apiKey和secretKey换取token，注意有效期保存在expiresAt
 */
@Slf4j
@Component
@AllArgsConstructor
public class TokenHolder {

    private SpeechAsrProperties speechAsrProperties;
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取token，refresh 方法后调用有效
     *
     * @return
     */
    public String getBaiduToken() {
        /**
         * url , Token的url，http可以改为https
         */
        final String url = "http://aip.baidubce.com/oauth/2.0/token";
        /**
         * asr的权限 scope 是  "audio_voice_assistant_get"
         * tts 的权限 scope 是 "audio_tts_post"
         */
        final String scope = "audio_voice_assistant_get";

        String apiKey = speechAsrProperties.getBaiduAppKey();
        String secretKey = speechAsrProperties.getBaiduAppSecret();
        //从redis获取
        Object cache = redisTemplate.opsForValue().get(apiKey);
        if (null != cache) {
            //没有过期
            JSONObject entries = JSONUtil.parseObj(cache);
            if (System.currentTimeMillis() < entries.getLong("expiresAt")) {
                return entries.getStr("token");
            }
        }
        String getTokenURL = url + "?grant_type=client_credentials"
                + "&client_id=" + HttpClient.urlEncode(apiKey) + "&client_secret=" + HttpClient.urlEncode(secretKey);
        // 打印的url出来放到浏览器内可以复现
        System.out.println("token url:" + getTokenURL);
        HttpRequestOptions<String, JSONObject> options = HttpRequestOptions.<String, JSONObject>builder()
                .url(getTokenURL)
                .method(Method.GET)
                .responseType(JSONObject.class)
                .build();
        JSONObject result = HttpClient.execute(options);

        if (null == result.getStr("access_token")) {
            // 返回没有access_token字段
            throw new IllegalArgumentException("access_token not obtained, " + result);
        }
        if (null == result.getStr("scope")) {
            // 返回没有scope字段
            throw new IllegalArgumentException("scopenot obtained, " + result);
        }
        // scope = null, 忽略scope检查

        if (!result.getStr("scope").contains(scope)) {
            throw new IllegalArgumentException("scope not exist, " + scope + "," + result);
        }
        String token = result.getStr("access_token");
        long expiresAt = System.currentTimeMillis() + result.getLong("expires_in") * 1000;
        //过期时间存到redis
        log.info("token:{},expiresAt:{}", token, expiresAt);
        Map<String, Object> map = new HashMap<>();
        map.put("token", token);
        map.put("expiresAt", expiresAt);
        redisTemplate.opsForValue().setIfAbsent(apiKey, JSONUtil.toJsonStr(map));
        return token;
    }

    public String getAliToken() throws IOException {
        String aliAppKey = speechAsrProperties.getAliAppKeyId();
        //从redis获取
        Object cache = redisTemplate.opsForValue().get(aliAppKey);
        if (null != cache) {
            //没有过期
            JSONObject entries = JSONUtil.parseObj(cache);
            if (System.currentTimeMillis() < entries.getLong("expiresAt")) {
                return entries.getStr("token");
            }
        }
        AccessToken accessToken = new AccessToken(aliAppKey, speechAsrProperties.getAliAppSecret());
        accessToken.apply();
        String token = accessToken.getToken();
        long expireTime = accessToken.getExpireTime();
        //过期时间存到redis
        log.info("token:{},expiresAt:{}", token, expireTime);
        Map<String, Object> map = new HashMap<>();
        map.put("token", token);
        map.put("expiresAt", expireTime);
        redisTemplate.opsForValue().setIfAbsent(aliAppKey, JSONUtil.toJsonStr(map));
        return token;
    }
}
