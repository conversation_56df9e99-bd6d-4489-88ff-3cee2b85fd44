package com.joinus.knowledge.utils;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.*;
import com.aliyun.oss.common.comm.Protocol;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.common.utils.IOUtils;
import com.aliyun.oss.model.*;
import com.aliyun.sts20150401.models.AssumeRoleResponse;
import com.aliyun.sts20150401.models.AssumeRoleResponseBody;
import com.aliyun.tea.TeaException;
import com.joinus.knowledge.config.AliOssProperties;
import com.joinus.knowledge.config.base.BaseException;
import com.joinus.knowledge.enums.OssEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.util.*;

@Slf4j
@Component
@AllArgsConstructor
public class AliOssUtils {

    private AliOssProperties aliOssProperties;

    /**
     * 创建OSSClient实例。
     *
     * @return OSSClient实例。
     */
    private OSS buildOssClient() {
        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(aliOssProperties.getAccessKeyId(), aliOssProperties.getAccessKeySecret());
        // 创建OSSClient实例。
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        // 设置是否支持CNAME。CNAME用于将自定义域名绑定到目标Bucket。
        clientBuilderConfiguration.setSupportCname(true);
        // HTTPS协议
        clientBuilderConfiguration.setProtocol(Protocol.HTTPS);
        OSS ossClient = OSSClientBuilder.create()
                .endpoint(aliOssProperties.getEndpoint())
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(aliOssProperties.getRegion())
                .build();
        return ossClient;
    }

    /**
     * sts 生成ossClient。用于为前端生成token
     *
     * @return 生成的临时凭证。
     * @throws Exception
     */
    private com.aliyun.sts20150401.Client createClient() throws Exception {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId(aliOssProperties.getAccessKeyId())
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret(aliOssProperties.getAccessKeySecret());
        // Endpoint 请参考 https://api.aliyun.com/product/Sts
//        config.endpoint = aliOssProperties.getEndpoint();
        config.endpoint = "sts.cn-beijing.aliyuncs.com";
        return new com.aliyun.sts20150401.Client(config);
    }

    /**
     * 为前端生成token
     *
     * @return 生成的临时凭证。
     * @throws Exception
     */
    public AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials getToken() throws Exception {
        com.aliyun.sts20150401.Client client = createClient();
        com.aliyun.sts20150401.models.AssumeRoleRequest assumeRoleRequest = new com.aliyun.sts20150401.models.AssumeRoleRequest()
                .setDurationSeconds(15 * 60L)
                .setRoleArn(aliOssProperties.getSts().getRoleArn())
                .setRoleSessionName("edu-knowledge-hub");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            AssumeRoleResponse assumeRoleResponse = client.assumeRoleWithOptions(assumeRoleRequest, runtime);
            log.info("assumeRoleResponse: {} ", JSONUtil.toJsonStr(assumeRoleResponse));
            return assumeRoleResponse.getBody().getCredentials();
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            log.info("TeaException: {} ", error);
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData());
            log.info("Exception: {} ", JSONUtil.toJsonStr(_error));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    /**
     * 上传图片
     * @param bytes
     * @param prefix
     * @param suffix
     * @return
     */
    public String upload(byte[] bytes, String prefix, String suffix) {
        OSS ossClient = buildOssClient();
        try {
            String objectName = buildObjectName(prefix, suffix);
            if (log.isDebugEnabled()) {
                log.debug("生成阿里OSS Object Key：{}", objectName);
            }
            ossClient.putObject(aliOssProperties.getBucketName(), objectName, new ByteArrayInputStream(bytes));
            return objectName;
        } catch (OSSException oe) {
            log.error("捕获到OSS异常！异常原因：" + oe.getErrorMessage() + "，异常编码：" + oe.getErrorCode() + "，请求ID：" + oe.getRequestId() + "，Host ID：" + oe.getHostId());
            throw new BaseException("图片上传失败：" + oe.getErrorMessage(), 400);
        } catch (ClientException ce) {
            log.error("捕获到OSS客户端通信异常！异常原因：" + ce.getErrorMessage() + "，异常编码：" + ce.getErrorCode() + "，请求ID：" + ce.getRequestId());
            throw new BaseException("图片上传失败：网络异常！", 400);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * base64上传
     * @param base64
     * @param prefix
     * @param suffix
     * @return
     */
    public String uploadViaBase64(String base64, String prefix, String suffix) {
        OSS ossClient = buildOssClient();
        try {
            // 处理可能存在的Base64前缀
            String[] parts = base64.split(",");
            byte[] bytes;
            if (parts.length > 1) {
                bytes = Base64.getDecoder().decode(parts[1]);
            } else {
                bytes = Base64.getDecoder().decode(base64);
            }

            String objectName = buildObjectName(prefix, suffix);
            if (log.isDebugEnabled()) {
                log.debug("生成阿里OSS Object Key：{}", objectName);
            }
            ossClient.putObject(aliOssProperties.getBucketName(), objectName, new ByteArrayInputStream(bytes));
            return objectName;
        } catch (IllegalArgumentException e) {
            log.error("Base64解码失败", e);
            throw new BaseException("图片Base64格式错误", 400);
        } catch (OSSException oe) {
            // 原有异常处理保持不变
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }

    /**
     * 生成ossKey
     * @param prefix
     * @param suffix
     * @return
     */
    public String buildObjectName(String prefix, String suffix) {
        StringBuilder stringBuilder = new StringBuilder();
        LocalDate localDate = LocalDate.now();
        stringBuilder.append(aliOssProperties.getBaseDir())
                .append(StrUtil.isBlank(prefix) ? "default" : prefix)
                .append(CharPool.SLASH)
                .append(localDate.getYear())
                .append(CharPool.SLASH)
                .append(localDate.getMonthValue())
                .append(CharPool.SLASH)
                .append(localDate.getDayOfMonth())
                .append(CharPool.SLASH)
                .append(suffix);
        return stringBuilder.toString();
    }

    /**
     * 生成临时链接
     *
     * @param objectName
     * @return
     */
    public String generatePresignedUrl(String objectName) {
        OSS ossClient = buildOssClient();
        URL signedUrl = null;
        // 指定生成的签名URL过期时间，单位为毫秒。本示例以设置过期时间为1小时为例。
        Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000L);

        // 生成签名URL。
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(aliOssProperties.getBucketName(), objectName, HttpMethod.GET);
        // 设置过期时间。
        request.setExpiration(expiration);

        // 通过HTTP GET请求生成签名URL。
        signedUrl = ossClient.generatePresignedUrl(request);
        if (log.isDebugEnabled()) {
            log.debug("生成对象签名URL：{}", signedUrl);
        }
        return signedUrl.toString();
    }

    /**
     * 生成临时链接
     * @param bucketName
     * @param objectName
     * @return
     */
    public String generatePresignedUrl(String bucketName, String objectName) {
        OSS ossClient = buildOssClient();
        URL signedUrl = null;
        // 指定生成的签名URL过期时间，单位为毫秒。本示例以设置过期时间为1小时为例。
        Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000L);

        // 生成签名URL。
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, objectName, HttpMethod.GET);
        // 设置过期时间。
        request.setExpiration(expiration);

        // 通过HTTP GET请求生成签名URL。
        signedUrl = ossClient.generatePresignedUrl(request);
        if (log.isDebugEnabled()) {
            log.debug("生成对象签名URL：{}", signedUrl);
        }
        return signedUrl.toString();
    }

    /**
     * 生成pdf预览
     * @param bucketName
     * @param objectName
     * @return
     */
    public String generatePdfPreview(String bucketName, String objectName) {
        // 设置样式，样式中包含文档预览参数。
        OSS ossClient = buildOssClient();
        String style = "doc/preview,export_1,print_1/watermark,text_5YaF6YOo6LWE5paZ,size_30,t_60";
        // 指定签名URL过期时间为3600秒)
        Date expiration = new Date(System.currentTimeMillis() + 300 * 1000 );
        GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(bucketName, objectName, HttpMethod.GET);
        req.setExpiration(expiration);
        req.setProcess(style);
        URL signedUrl = ossClient.generatePresignedUrl(req);
        return signedUrl.toString();
    }


    public static void generatePdfPreviewStatic(String bucketName, String objectName) throws com.aliyuncs.exceptions.ClientException {
        OSS ossClient = getOssClient();

        try {
            // 文档处理参数
            String style = "doc/preview,export_1,print_1/watermark,text_5YaF6YOo6LWE5paZ,size_30,t_60";
            // 指定签名URL过期时间为3600秒)
            Date expiration = new Date(System.currentTimeMillis() + 300 * 1000 );
            GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(bucketName, objectName, HttpMethod.GET);
            req.setExpiration(expiration);
            req.setProcess(style);
            URL signedUrl = ossClient.generatePresignedUrl(req);
            System.out.println(signedUrl);
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }




    public List<Map<String, Object>> getUrls(String urls) {
        if (StrUtil.isNotBlank(urls)) {
            String[] urlArray = urls.split(",");
            List<Map<String, Object>> urlList = new ArrayList<>();
            for (String url : urlArray) {
                Map<String, Object> map = new HashMap<>();
                map.put("key", url);
                map.put("value", generatePresignedUrl(url));
                urlList.add(map);
            }
            return urlList;
        }
        return null;
    }

    /**
     * 云端裁剪图片
     * @return
     */
    public String cropImage(String ossKey, Integer x, Integer y, Integer w, Integer h) {
        String bucketName = OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getBucket();

        // 创建OSSClient实例。
        OSS ossClient = buildOssClient();
        try {

            StringBuilder sbStyle = new StringBuilder();
            Formatter styleFormatter = new Formatter(sbStyle);
            String styleType = StrUtil.format("image/crop,w_{},h_{},x_{},y_{}", w, h, x, y);
            // 将处理后的图片命名为example-resize.png并保存到当前Bucket。
            // 填写Object完整路径。Object完整路径中不能包含Bucket名称。
            String directory = ossKey.substring(0, ossKey.lastIndexOf('.'));
            String filename = UUID.randomUUID().toString() + "." + ossKey.substring(ossKey.lastIndexOf('.') + 1);
            String targetImage = directory + "/" + filename;
            styleFormatter.format("%s|sys/saveas,o_%s,b_%s", styleType,
                    BinaryUtil.toBase64String(targetImage.getBytes()),
                    BinaryUtil.toBase64String(bucketName.getBytes()));
            System.out.println(sbStyle.toString());
            ProcessObjectRequest request = new ProcessObjectRequest(bucketName, ossKey, sbStyle.toString());
            GenericResult processResult = ossClient.processObject(request);
            String json = IOUtils.readStreamAsString(processResult.getResponse().getContent(), "UTF-8");
            processResult.getResponse().getContent().close();
            JSONObject jsonObject = JSONUtil.parseObj(json);
            if (!jsonObject.getStr("status").equals("OK")) {
                throw new BaseException("裁剪图片失败", 400);
            }
            return jsonObject.getStr("object");

        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException | IOException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }



    public static void main(String[] args) throws com.aliyuncs.exceptions.ClientException {
//        String s = cropImageStatic();
//        System.out.println(s);
//        String s1 = generatePresignedUrlStatic("edu-knowledge-hub", "uat/math-question/2025/3/20/cropped/****************************************-b429-48c2-a8bd-ad2fab08eb28.png");
//        System.out.println(s1);

        generatePdfPreviewStatic("edu-knowledge-hub", "uat/math-training/pdf/2025/3/27/986669b0-7a41-4f2d-a1ca-34a1dccaf7c4.pdf");
        System.out.println("==========");
    }

    public static OSS getOssClient() {
        String endpoint = "https://oss-edu-knowledge-hub.qingyulan.net";
        CredentialsProvider credentialsProvider = new DefaultCredentialProvider("LTAI5tH52JJv1MG3sH8eGWE2", "******************************");
        // 填写Bucket所在地域。以华东1（杭州）为例，Region填写为cn-hangzhou。
        String region = "cn-beijing";

        // 创建OSSClient实例。
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSupportCname(true);
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        OSS ossClient = OSSClientBuilder.create()
                .endpoint(endpoint)
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(region)
                .build();
        return ossClient;
    }
    public static String cropImageStatic() {
        // 填写Bucket名称，例如examplebucket。
        String bucketName = "edu-knowledge-hub";
        // 填写Object完整路径。Object完整路径中不能包含Bucket名称。
        String sourceImage = "uat/math-question/2025/3/20/****************************************-b429-48c2-a8bd-ad2fab08eb28.png";

        // 创建OSSClient实例。
        OSS ossClient = getOssClient();
        try {

            StringBuilder sbStyle = new StringBuilder();
            Formatter styleFormatter = new Formatter(sbStyle);
            String styleType = "image/crop,w_846,h_140,x_105,y_375";
            // 将处理后的图片命名为example-resize.png并保存到当前Bucket。
            // 填写Object完整路径。Object完整路径中不能包含Bucket名称。
            String directory = sourceImage.substring(0, sourceImage.lastIndexOf('.'));
            String filename = UUID.randomUUID().toString() + "." + sourceImage.substring(sourceImage.lastIndexOf('.') + 1);
            String targetImage = directory + "/" + filename;
            System.out.println(targetImage);
//            styleFormatter.format("%s|sys/saveas,o_%s,b_%s", styleType,
//                    BinaryUtil.toBase64String(targetImage.getBytes()),
//                    BinaryUtil.toBase64String(bucketName.getBytes()));
//            System.out.println(sbStyle.toString());
//            ProcessObjectRequest request = new ProcessObjectRequest(bucketName, sourceImage, sbStyle.toString());
//            GenericResult processResult = ossClient.processObject(request);
//            String json = IOUtils.readStreamAsString(processResult.getResponse().getContent(), "UTF-8");
//            processResult.getResponse().getContent().close();
//            System.out.println(json);

        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }

    public static String generatePresignedUrlStatic(String bucketName, String objectName) {
        OSS ossClient = getOssClient();
        URL signedUrl = null;
        // 指定生成的签名URL过期时间，单位为毫秒。本示例以设置过期时间为1小时为例。
        Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000L);

        // 生成签名URL。
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, objectName, HttpMethod.GET);
        // 设置过期时间。
        request.setExpiration(expiration);

        // 通过HTTP GET请求生成签名URL。
        signedUrl = ossClient.generatePresignedUrl(request);
        if (log.isDebugEnabled()) {
            log.debug("生成对象签名URL：{}", signedUrl);
        }
        return signedUrl.toString();
    }
}
