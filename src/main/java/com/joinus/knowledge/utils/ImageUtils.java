package com.joinus.knowledge.utils;

import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.awt.image.BufferedImage;

/**
 * 图片处理工具类
 */
@Slf4j
public class ImageUtils {

    /**
     * 调整图片大小
     *
     * @param originalImage 原始图片
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @return 调整后的图片
     */
    public static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
        BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics2D = resizedImage.createGraphics();
        
        // 设置图像插值算法
        graphics2D.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        graphics2D.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        graphics2D.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 绘制调整后的图像
        graphics2D.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        graphics2D.dispose();
        
        return resizedImage;
    }
    
    /**
     * 计算按比例缩放后的尺寸
     *
     * @param originalWidth 原始宽度
     * @param originalHeight 原始高度
     * @param maxWidth 最大宽度限制
     * @param maxHeight 最大高度限制
     * @return 新的尺寸 [width, height]
     */
    public static int[] calculateDimensions(int originalWidth, int originalHeight, int maxWidth, int maxHeight) {
        int[] dimensions = new int[2];
        
        // 如果原始尺寸已经小于最大限制，保持不变
        if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
            dimensions[0] = originalWidth;
            dimensions[1] = originalHeight;
            return dimensions;
        }
        
        // 计算宽高比
        double ratio = (double) originalWidth / (double) originalHeight;
        
        // 按宽度计算
        int newWidth = maxWidth;
        int newHeight = (int) (newWidth / ratio);
        
        // 如果计算出的高度超过最大高度，则按高度限制计算
        if (newHeight > maxHeight) {
            newHeight = maxHeight;
            newWidth = (int) (newHeight * ratio);
        }
        
        dimensions[0] = newWidth;
        dimensions[1] = newHeight;
        
        return dimensions;
    }
}
