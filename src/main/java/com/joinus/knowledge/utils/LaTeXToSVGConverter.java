package com.joinus.knowledge.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.batik.dom.GenericDOMImplementation;
import org.apache.batik.svggen.SVGGeneratorContext;
import org.apache.batik.svggen.SVGGraphics2D;
import org.scilab.forge.jlatexmath.TeXConstants;
import org.scilab.forge.jlatexmath.TeXFormula;
import org.scilab.forge.jlatexmath.TeXIcon;
import org.w3c.dom.DOMImplementation;
import org.w3c.dom.Document;

import java.awt.*;
import java.io.StringWriter;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * LaTeX 转 SVG 工具类
 */
@Slf4j
public class LaTeXToSVGConverter {

    // 最大缓存容量限制，防止内存泄漏
    private static final int MAX_CACHE_SIZE = 1000;
    
    // 使用 ConcurrentHashMap 作为缓存，线程安全
    private static final Map<String, String> svgCache = new ConcurrentHashMap<>(256);
    private static final Map<String, String> imgTagCache = new ConcurrentHashMap<>(256);
    
    /**
     * 清除所有缓存
     */
    public static void clearCache() {
        svgCache.clear();
        imgTagCache.clear();
        log.info("LaTeX 转换缓存已清空");
    }
    
    /**
     * 获取当前缓存大小
     * 
     * @return 缓存大小
     */
    public static int getCacheSize() {
        return svgCache.size() + imgTagCache.size();
    }

    /**
     * 将 LaTeX 公式转换为 SVG 字符串
     *
     * @param latex LaTeX 公式
     * @param size 字体大小
     * @return SVG 字符串
     */
    public static String convertToSVG(String latex, float size) {
        // 生成缓存键
        String cacheKey = latex + "_" + size;
        
        // 检查缓存
        String cachedSvg = svgCache.get(cacheKey);
        if (cachedSvg != null) {
            return cachedSvg;
        }
        
        try {
            // 对LaTeX进行预处理，添加样式声明使符号与字母大小更一致
            latex = preProcessLatex(latex);
            
            // 创建 TeXFormula 实例
            TeXFormula formula = new TeXFormula(latex);
            
            // 创建 TeXIcon，使用STYLE_DISPLAY以确保公式大小更一致
            TeXIcon icon = formula.createTeXIcon(TeXConstants.STYLE_DISPLAY, size);
            
            // 设置图标的前景色为黑色
            icon.setForeground(Color.BLACK);
            
            // 设置图标的内部边距
            icon.setInsets(new Insets(1, 1, 1, 1));
            
            // 获取图标的尺寸
            int width = icon.getIconWidth();
            int height = icon.getIconHeight();
            
            // 创建 SVG 文档
            DOMImplementation domImpl = GenericDOMImplementation.getDOMImplementation();
            String svgNS = "http://www.w3.org/2000/svg";
            Document document = domImpl.createDocument(svgNS, "svg", null);
            
            // 创建 SVG 生成器
            SVGGeneratorContext ctx = SVGGeneratorContext.createDefault(document);
            SVGGraphics2D g2 = new SVGGraphics2D(ctx, true);
            
            // 设置背景为透明
            g2.setSVGCanvasSize(new Dimension(width, height));
            
            // 绘制 LaTeX 公式
            icon.paintIcon(null, g2, 0, 0);
            
            // 输出 SVG 字符串
            StringWriter writer = new StringWriter();
            g2.stream(writer, true);
            String svg = writer.toString();
            
            // 存入缓存（如果缓存未满）
            if (svgCache.size() < MAX_CACHE_SIZE) {
                svgCache.put(cacheKey, svg);
            }
            
            return svg;
        } catch (Exception e) {
            log.error("LaTeX 转 SVG 失败: {}", latex, e);
            return ""; // 转换失败时返回空字符串
        }
    }
    
    /**
     * 将 LaTeX 公式转换为 SVG 并嵌入到 img 标签中
     *
     * @param latex LaTeX 公式
     * @param size 字体大小
     * @return 包含 SVG 的 img 标签
     */
    public static String convertToImgTag(String latex, float size) {
        // 生成缓存键
        String cacheKey = latex + "_" + size + "_img";
        
        // 检查缓存
        String cachedImgTag = imgTagCache.get(cacheKey);
        if (cachedImgTag != null) {
            return cachedImgTag;
        }
        
        try {
            // 检查是否为复杂结构
            boolean isComplex = isComplexStructure(latex);
            
            String svg = convertToSVG(latex, size);
            String base64 = Base64.getEncoder().encodeToString(svg.getBytes());
            
            String imgTag;
            
            if (isComplex) {
                // 对于复杂公式，使用居中样式
                imgTag = "<div style=\"text-align:center; margin:8px 0;\">" +
                         "<img src=\"data:image/svg+xml;base64," + base64 + "\" " +
                         "alt=\"" + escape(latex) + "\" style=\"vertical-align:middle;\">" +
                         "</div>";
            } else {
                // 对于简单公式，使用行内样式
                imgTag = "<img src=\"data:image/svg+xml;base64," + base64 + "\" " +
                         "alt=\"" + escape(latex) + "\" style=\"vertical-align:middle; margin:0 2px;\">";
            }
            
            // 存入缓存
            if (imgTagCache.size() < MAX_CACHE_SIZE) {
                imgTagCache.put(cacheKey, imgTag);
            }
            
            return imgTag;
        } catch (Exception e) {
            log.error("LaTeX 转 SVG img 标签失败: {}", latex, e);
            return latex; // 转换失败时返回原始 LaTeX
        }
    }
    
    /**
     * 检查是否为复杂数学结构（如矩阵、数组、多行公式等）
     * 
     * @param latex LaTeX内容
     * @return 是否为复杂结构
     */
    private static boolean isComplexStructure(String latex) {
        if (latex == null) return false;
        
        // 检查是否包含特定的环境或命令
        return latex.contains("\\begin{array}") || 
               latex.contains("\\begin{matrix}") || 
               latex.contains("\\begin{pmatrix}") || 
               latex.contains("\\begin{bmatrix}") || 
               latex.contains("\\begin{cases}") || 
               latex.contains("\\begin{align}") || 
               latex.contains("\\left\\{") || 
               latex.contains("\\frac") ||
               (latex.contains("\\left") && latex.contains("\\right"));
    }
    
    /**
     * 将 LaTeX 公式转换为 SVG，使用默认字体大小 (18)
     *
     * @param latex LaTeX 公式
     * @return SVG 字符串
     */
    public static String convertToSVG(String latex) {
        return convertToSVG(latex, 18);
    }
    
    /**
     * 将 LaTeX 公式转换为 SVG 并嵌入到 img 标签中，使用默认字体大小 (18)
     *
     * @param latex LaTeX 公式
     * @return 包含 SVG 的 img 标签
     */
    public static String convertToImgTag(String latex) {
        return convertToImgTag(latex, 18);
    }
    
    /**
     * 预处理LaTeX公式，添加全局样式使符号与字母大小更一致
     * 
     * @param latex 原始LaTeX公式
     * @return 添加样式后的LaTeX公式
     */
    private static String preProcessLatex(String latex) {
        if (latex == null || latex.isEmpty()) {
            return latex;
        }
        
        // 检查是否已经包含了样式声明
        if (latex.contains("\\DeclareMathSizes") || latex.contains("\\begin{document}")) {
            return latex;
        }
        
        // 处理几何符号，使其在各种公式环境中保持一致的大小
        latex = latex.replace("\\triangle", "\\displaystyle{\\triangle}");
        latex = latex.replace("\\angle", "\\displaystyle{\\angle}");
        latex = latex.replace("\\square", "\\displaystyle{\\square}");
        latex = latex.replace("\\circle", "\\displaystyle{\\circle}");
        
        // 确保整个公式使用displaystyle，使符号与字母大小更协调
        if (!latex.startsWith("\\displaystyle")) {
            latex = "\\displaystyle{" + latex + "}";
        }
        
        return latex;
    }
    
    /**
     * 转义HTML特殊字符
     * 
     * @param input 输入文本
     * @return 转义后的文本
     */
    private static String escape(String input) {
        if (input == null) return "";
        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#39;");
    }
}
