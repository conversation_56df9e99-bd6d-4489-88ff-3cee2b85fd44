package com.joinus.knowledge.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.JsonSchemaGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 一个高效、线程安全的JSON Schema生成器和缓存。
 * 遵循以下最佳实践：
 * 1. 重用ObjectMapper和JsonSchemaGenerator实例。
 * 2. 缓存已生成的Schema，避免重复计算。
 * 3. 直接将Java对象转换为JsonNode，避免不必要的String转换。
 */
public final class JsonSchemaCache {

    private static final Logger log = LoggerFactory.getLogger(JsonSchemaCache.class);

    // 1. 创建静态、线程安全的ObjectMapper和SchemaGenerator实例以供重用
    // ObjectMapper是线程安全的
    private static final ObjectMapper MAPPER = new ObjectMapper().enable(SerializationFeature.INDENT_OUTPUT);
    private static final JsonSchemaGenerator SCHEMA_GENERATOR = new JsonSchemaGenerator(MAPPER);

    // 2. 创建一个线程安全的缓存来存储已生成的Schema
    private static final ConcurrentMap<Class<?>, JsonNode> SCHEMA_CACHE = new ConcurrentHashMap<>();

    // 私有化构造函数，防止实例化
    private JsonSchemaCache() {}

    /**
     * 获取指定Class的JSON Schema。
     * 首先尝试从缓存中获取，如果不存在，则生成、缓存并返回。
     *
     * @param clazz 要为其生成Schema的类
     * @return 代表该类Schema的JsonNode，如果生成失败则返回null
     */
    public static JsonNode getSchema(Class<?> clazz) {
        if (clazz == null) {
            return null;
        }
        // 3. 使用ConcurrentHashMap.computeIfAbsent实现高效的“检查-然后-计算”原子操作
        // 这比自己写if/else加锁更简洁、更安全。
        try {
            return SCHEMA_CACHE.computeIfAbsent(clazz, c -> {
                try {
                    log.info("Cache miss for class: {}. Generating new JSON Schema.", c.getName());
                    // 从给定的Class生成JsonSchema对象
                    JsonSchema schema = SCHEMA_GENERATOR.generateSchema(c);
                    // 直接将JsonSchema对象转换为JsonNode，这是最高效的方式
                    return MAPPER.valueToTree(schema);
                } catch (JsonProcessingException e) {
                    // 在lambda表达式中，需要将受检异常包装成运行时异常
                    throw new RuntimeException("Failed to generate schema for class: " + c.getName(), e);
                }
            });
        } catch (Exception e) {
            // 捕获生成过程中可能抛出的运行时异常
            log.error("Error generating or retrieving schema for class: {}", clazz.getName(), e);
            return null;
        }
    }
}