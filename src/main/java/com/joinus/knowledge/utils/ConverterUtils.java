package com.joinus.knowledge.utils;
import com.joinus.knowledge.enums.ContentPartType;
import com.joinus.knowledge.model.po.ContentPart;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ConverterUtils {

    public static String convertToJsonStr(String inputString) {
        Pattern pattern = Pattern.compile("(?s)```(?:\\w+)?\\s*(.*?)\\s*```");
        Matcher matcher = pattern.matcher(inputString);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return inputString;
    }

    public static String removeQuestionPrefix(String input) {
        String prefix = "- 题干：";
        return removePrefix(input, prefix);
    }

    public static String removeExamPrefix(String input) {
        String prefix = "- 试卷名称：";
        return removePrefix(input, prefix);
    }

    /**
     * 标准化所有类型的横线字符为标准连字符 (-)
     *
     * @param text 需要处理的文本
     * @return 标准化后的文本
     */
    public static String normalizeDashes(String text) {
        if (text == null) {
            return null;
        }

        return text
                .replace("−", "-")    // U+2212 MINUS SIGN
                .replace("–", "-")    // U+2013 EN DASH
                .replace("—", "-")    // U+2014 EM DASH
                .replace("‐", "-")    // U+2010 HYPHEN
                .replace("‑", "-")    // U+2011 NON-BREAKING HYPHEN
                .replace("⸺", "-")    // U+2E3A TWO-EM DASH
                .replace("⸻", "-")    // U+2E3B THREE-EM DASH
                .replace("﹣", "-")    // U+FE63 SMALL HYPHEN-MINUS
                .replace("－", "-");   // U+FF0D FULLWIDTH HYPHEN-MINUS
    }

    public static String rebuildExamName(String input) {
        return normalizeDashes(
                input.replace("（", "(")
                        .replace("）", ")")
                        .replaceAll("\\s+", "")
        );
    }

    public static String convertKnowledgeDomain(String input) {
        String regex = "知识领域：([^\\r\\n]+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return input;
    }

    private static String removePrefix(String input, String prefix) {
        if (input != null && input.trim().startsWith(prefix)) {
            input = input.trim();
            return input.substring(prefix.length()).trim();
        }
        return input;
    }

    public static BigDecimal extractNumber(String input) {
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            return new BigDecimal(matcher.group());
        }
        return BigDecimal.ZERO;
    }

    public static String fixInvalidBackslashesInJson(String jsonString) {
        StringBuilder result = new StringBuilder(jsonString.length() * 2);
        boolean inEscape = false;

        for (int i = 0; i < jsonString.length(); i++) {
            char c = jsonString.charAt(i);

            if (inEscape) {
                // 已经在转义序列中
                inEscape = false;

                if (c == '"' || c == '\\' || c == '/' ||
                        c == 'b' || c == 'f' || c == 'n' || c == 'r' || c == 't') {
                    // JSON标准转义字符
                    result.append('\\').append(c);
                } else if (c == 'u') {
                    // Unicode转义开始
                    // 检查后面是否有4个十六进制字符
                    if (i + 4 < jsonString.length() &&
                            isHexDigit(jsonString.charAt(i+1)) &&
                            isHexDigit(jsonString.charAt(i+2)) &&
                            isHexDigit(jsonString.charAt(i+3)) &&
                            isHexDigit(jsonString.charAt(i+4))) {
                        // 有效的Unicode转义
                        result.append('\\').append(c);
                    } else {
                        // 无效的Unicode转义，添加额外的反斜杠
                        result.append('\\').append('\\').append(c);
                    }
                } else {
                    // 非标准转义字符，需要额外的反斜杠
                    result.append('\\').append('\\').append(c);
                }
            } else if (c == '\\') {
                // 进入转义模式
                inEscape = true;
            } else {
                // 普通字符
                result.append(c);
            }
        }

        // 处理字符串末尾的反斜杠
        if (inEscape) {
            result.append('\\').append('\\');
        }

        return result.toString();
    }

    private static boolean isHexDigit(char c) {
        return (c >= '0' && c <= '9') ||
                (c >= 'a' && c <= 'f') ||
                (c >= 'A' && c <= 'F');
    }

    public static List<ContentPart> parseContent(String content) {
        List<ContentPart> contentParts = new ArrayList<>();

        // 正则表达式匹配<img>标签
        Pattern pattern = Pattern.compile("<img\\s+[^>]*?src=\\\"([^\\\"]*)\\\"[^>]*?/?>");
        Matcher matcher = pattern.matcher(content);

        int lastEnd = 0;

        // 遍历所有匹配项
        while (matcher.find()) {
            // 获取<img>标签前的文本
            String textBefore = content.substring(lastEnd, matcher.start());
            if (!textBefore.isEmpty()) {
                contentParts.add(ContentPart.builder()
                        .contentPartType(ContentPartType.TEXT)
                        .text(textBefore)
                        .build());
            }

            // 获取图片URL
            String imageUrl = matcher.group(1);
            contentParts.add(ContentPart.builder()
                    .contentPartType(ContentPartType.IMAGE_URL)
                    .url(imageUrl)
                    .build());

            lastEnd = matcher.end();
        }

        // 添加最后一段文本（如果有）
        if (lastEnd < content.length()) {
            String textAfter = content.substring(lastEnd);
            if (!textAfter.isBlank()) {
                contentParts.add(ContentPart.builder()
                        .contentPartType(ContentPartType.TEXT)
                        .text(textAfter)
                        .build());
            }
        }

        return contentParts;
    }

    public static String clearImgSrc(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }

        String patternString = "(?i)(<img\\s+(?:[^>]*?\\s+)?src\\s*=\\s*['\"])[^'\"]*?(['\"][^>]*?>)";
        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(content);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1) + matcher.group(2));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    public static List<ContentPart> buildContentPart(String content,List<String> imageUrls) {
        List<ContentPart> contentParts = new ArrayList<>();
        contentParts.add(ContentPart.builder()
                .contentPartType(ContentPartType.TEXT)
                .text(content)
                .build());
        imageUrls.forEach(imageUrl -> {
            contentParts.add(ContentPart.builder()
                    .contentPartType(ContentPartType.IMAGE_URL)
                    .url(imageUrl)
                    .build());
        });
        return contentParts;
    }
}
