package com.joinus.knowledge.utils;

import io.minio.*;
import io.minio.http.Method;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * MinIO工具类，提供文件上传等基础功能
 */
@Component
public class MinioUtils {

    @Resource
    private MinioClient minioClient;

    private final int DEFAULT_EXPERY = 1;

    private final TimeUnit DEFAULT_EXPERY_TIMEUNIT = TimeUnit.DAYS;

    /**
     * 检查存储桶是否存在，不存在则创建
     * 
     * @param bucketName 存储桶名称
     * @throws Exception 操作异常
     */
    public void ensureBucketExists(String bucketName) throws Exception {
        boolean found = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (!found) {
            // 创建存储桶
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }
    }
    
    /**
     * 上传文件到MinIO服务器
     * 
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param fileName 要上传的本地文件路径
     * @return 上传成功返回true，否则返回false
     */
    public boolean uploadFile(String bucketName, String objectName, String fileName) {
        try {
            // 确保存储桶存在
            ensureBucketExists(bucketName);
            
            // 上传文件
            minioClient.uploadObject(
                    UploadObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .filename(fileName)
                            .build());
            
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 上传输入流到MinIO服务器
     * 
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param inputStream 输入流
     * @param size 输入流大小
     * @return 上传成功返回true，否则返回false
     */
    public boolean uploadInputStream(String bucketName, String objectName, InputStream inputStream,
                                     long size) {
        try {
            // 确保存储桶存在
            ensureBucketExists(bucketName);
            
            // 上传输入流
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(inputStream, size, -1)
                            .build());
            
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 上传Base64编码的图片到MinIO服务器
     *
     * @param bucketName  存储桶名称
     * @param objectName  对象名称
     * @param base64Image Base64编码的图片字符串(不包含前缀如"data:image/jpeg;base64,")
     * @return 上传成功返回true，否则返回false
     */
    public boolean uploadBase64Image(String bucketName, String objectName, String base64Image) {
        try {
            // 确保存储桶存在
            ensureBucketExists(bucketName);

            // 解码Base64字符串
            byte[] imageBytes = Base64.getDecoder().decode(base64Image);
            ByteArrayInputStream bais = new ByteArrayInputStream(imageBytes);

            // 上传解码后的图片数据
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(bais, imageBytes.length, -1)
                            .contentType("image/png")
                            .build());

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取文件的临时预览链接（默认过期时间）
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 临时访问URL
     */
    public String getPresignedObjectUrl(String bucketName, String objectName) {
        return getPresignedObjectUrl(bucketName, objectName, DEFAULT_EXPERY, DEFAULT_EXPERY_TIMEUNIT);
    }

    /**
     * 获取文件的临时预览链接
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param expiry     过期时间
     * @param timeUnit   时间单位
     * @return 临时访问URL
     */
    public String getPresignedObjectUrl(String bucketName, String objectName, Integer expiry, TimeUnit timeUnit) {
        try {
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucketName)
                            .object(objectName)
                            .expiry(expiry, timeUnit)
                            .build());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 使用S3兼容方式获取文件URL
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return S3兼容的预签名URL
     */
    public String getS3CompatibleUrl(String bucketName, String objectName) {
        try {
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucketName)
                            .object(objectName)
                            .expiry(DEFAULT_EXPERY, DEFAULT_EXPERY_TIMEUNIT)
                            .build());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取文件的临时下载链接（默认过期时间）
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param filename   下载后的文件名
     * @return 临时下载URL
     */
    public String getPresignedDownloadUrl(String bucketName, String objectName, String filename) {
        return getPresignedDownloadUrl(bucketName, objectName, filename, DEFAULT_EXPERY, DEFAULT_EXPERY_TIMEUNIT);
    }

    /**
     * 获取文件的临时下载链接
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param filename   下载后的文件名
     * @param expiry     过期时间
     * @param timeUnit   时间单位
     * @return 临时下载URL
     */
    public String getPresignedDownloadUrl(String bucketName, String objectName, String filename,
                                          Integer expiry, TimeUnit timeUnit) {
        try {
            Map<String, String> reqParams = new HashMap<>();
            reqParams.put("response-content-disposition", "attachment; filename=\"" + filename + "\"");

            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucketName)
                            .object(objectName)
                            .expiry(expiry, timeUnit)
                            .extraQueryParams(reqParams)
                            .build());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 基于分享ID获取临时下载URL
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param expiry 过期时间（秒）
     * @return 分享URL
     */
    public String getShareUrl(String bucketName, String objectName, int expiry) {
        try {
            StatObjectResponse stat = minioClient.statObject(
                    StatObjectArgs.builder().bucket(bucketName).object(objectName).build());

            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucketName)
                            .object(objectName)
                            .expiry(expiry)
                            .build());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }



}