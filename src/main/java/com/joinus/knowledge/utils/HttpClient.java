package com.joinus.knowledge.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.http.*;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Objects;

/**
 * HTTP客户端工具类，用于向第三方系统发送HTTP请求
 */
@Slf4j
public class HttpClient {

    /**
     * 执行HTTP请求
     *
     * @param options HTTP请求的配置选项
     * @param <T>     请求参数的类型
     * @param <R>     响应结果的类型
     * @return 响应结果的泛型对象
     * @throws HttpClientException 当请求失败时抛出的异常
     */
    public static <T, R> R execute(HttpRequestOptions<T, R> options) throws HttpClientException {
        // 生成唯一的请求ID
        final String id = UUID.fastUUID().toString(true);
        // 创建基础的HTTP请求
        HttpRequest request = createBaseRequest(options);
        // 记录请求详情
        logRequestDetails(options, id);
        try (HttpResponse response = request.execute()) {
            // 处理响应结果
            return processResponse(response, options.getResponseType(), id);
        } catch (HttpException e) {
            // 处理HTTP异常
            handleException(id, e);
        } catch (Exception e) {
            // 处理其他异常
            handleException(id, e);
        }
        return null;
    }

    /**
     * 记录请求详情
     *
     * @param options HTTP请求的配置选项
     * @param id      请求的唯一ID
     * @param <R>     响应结果的类型
     * @param <T>     请求参数的类型
     */
    private static <R, T> void logRequestDetails(HttpRequestOptions<T, R> options, String id) {
        if (log.isInfoEnabled()) {
            log.info("调用第三方HTTP请求，id：{}，相关参数: {}", id, options);
        }
    }

    /**
     * 创建基础的HTTP请求
     *
     * @param options HTTP请求的配置选项
     * @param <T>     请求参数的类型
     * @param <R>     响应结果的类型
     * @return HttpRequest对象
     */
    private static <T, R> HttpRequest createBaseRequest(HttpRequestOptions<T, R> options) {
        HttpRequest request = HttpRequest.of(buildUrlWithParams(options))
                .method(options.getMethod());

        // 对于POST或PUT请求，设置请求体和内容类型
        if (options.getMethod() == Method.POST || options.getMethod() == Method.PUT) {
            if (Objects.nonNull(options.getBodyParams())) {
                request.body(JSONUtil.toJsonStr(options.getBodyParams()));
            }

            request.contentType(ContentType.JSON.getValue());
        }

        // 设置请求头
        if (CollectionUtil.isNotEmpty(options.getHeaders())) {
            options.getHeaders().forEach(request::header);
        }
        return request;
    }

    /**
     * 构建带有查询参数的URL
     *
     * @param options HTTP请求的配置选项
     * @param <T>     请求参数的类型
     * @param <R>     响应结果的类型
     * @return 带有查询参数的URL字符串
     */
    private static <T, R> String buildUrlWithParams(HttpRequestOptions<T, R> options) {
        if (CollectionUtil.isEmpty(options.getQueryParams())) {
            return options.getUrl();
        }
        return HttpUtil.urlWithForm(options.getUrl(), options.getQueryParams(), null, true);
    }

    /**
     * 处理响应结果
     *
     * @param response     HTTP响应对象
     * @param responseType 响应结果的类类型
     * @param id           请求的唯一ID
     * @param <R>          响应结果的类型
     * @return 响应结果的泛型对象
     * @throws HttpClientException 当响应状态非OK时抛出的异常
     */
    private static <R> R processResponse(HttpResponse response, Class<R> responseType, String id) {
        logResponseDetails(response, id);
        if (!response.isOk()) {
            throw new HttpClientException();
        }
        return JSONUtil.toBean(response.body(), responseType);
    }

    /**
     * 记录响应详情
     *
     * @param response HTTP响应对象
     * @param id       请求的唯一ID
     */
    private static void logResponseDetails(HttpResponse response, String id) {
        if (log.isInfoEnabled()) {
            log.info("调用第三方HTTP请求，id：{}，响应数据: {}", id, response.body());
        }
    }

    /**
     * 异常处理方法
     *
     * @param id 请求的唯一ID
     * @param e  异常对象
     * @throws HttpClientException 自定义HTTP客户端异常
     */
    private static void handleException(String id, Exception e) {
        throw new HttpClientException("调用第三方HTTP请求失败，请求id：" + id + "，请联系系统管理员！", e);
    }

    /**
     * UrlEncode， UTF-8 编码
     *
     * @param str 原始字符串
     * @return
     */
    public static String urlEncode(String str) {
        String result = null;
        try {
            result = URLEncoder.encode(str, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 发送application/octet-stream格式body
     *
     * @param url
     * @param headers
     * @param data
     * @return
     */
    public static JSONObject sendOctetStreamData(String url, HashMap<String, String> headers, byte[] data) {
        HttpRequest request = HttpRequest.of(url).method(Method.POST);
        request.contentType(ContentType.OCTET_STREAM.getValue());
        request.body(data);
        // 设置请求头
        if (CollectionUtil.isNotEmpty(headers)) {
            headers.forEach(request::header);
        }
        // 生成唯一的请求ID
        final String id = UUID.fastUUID().toString(true);
        try (HttpResponse response = request.execute()) {
            // 处理响应结果
            return processResponse(response, JSONObject.class, id);
        } catch (HttpException e) {
            // 处理HTTP异常
            handleException(id, e);
        } catch (Exception e) {
            // 处理其他异常
            handleException(id, e);
        }
        return null;
    }
}
