package com.joinus.knowledge.utils;

/**
 * 自定义HttpClient异常类，继承自RuntimeException
 * 用于处理HttpClient相关的异常情况，提供了一系列构造函数以适应不同的异常处理需求
 */
public class HttpClientException extends RuntimeException {

    /**
     * 默认构造函数，无异常消息
     */
    public HttpClientException() {
        super();
    }

    /**
     * 带异常消息的构造函数
     *
     * @param message 异常的消息说明
     */
    public HttpClientException(String message) {
        super(message);
    }

    /**
     * 带异常消息和原因的构造函数
     *
     * @param message 异常的消息说明
     * @param cause   异常的原因，通常是一个Throwable对象
     */
    public HttpClientException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 仅带异常原因的构造函数
     *
     * @param cause 异常的原因，通常是一个Throwable对象
     */
    public HttpClientException(Throwable cause) {
        super(cause);
    }

}
