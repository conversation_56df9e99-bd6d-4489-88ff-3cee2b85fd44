package com.joinus.knowledge.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.joinus.knowledge.config.typehandler.LtreeTypeHandler;
import com.joinus.knowledge.config.typehandler.PostgresUUIDTypeHandler;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.UUID;

/**
 * MyBatis-Plus configuration
 */
@Configuration
@MapperScan("com.joinus.knowledge.mapper")
public class MybatisPlusConfig {

    /**
     * 自定义MyBatis-Plus配置，注册TypeHandler
     */
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            // 注册PostgresUUIDTypeHandler
            configuration.getTypeHandlerRegistry().register(UUID.class, PostgresUUIDTypeHandler.class);
            // 注册LtreeTypeHandler
            configuration.getTypeHandlerRegistry().register(LtreeTypeHandler.class);
        };
    }

    /**
     * Configure MyBatis-Plus plugins
     * @return configured interceptor
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // Pagination support
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.POSTGRE_SQL));
        
        // Prevent full table update and delete
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
        
        // Optimistic locking support
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        
        return interceptor;
    }
    
    /**
     * 自定义SQL注入器
     */
    @Bean
    public ISqlInjector sqlInjector() {
        return new DefaultSqlInjector();
    }
}
