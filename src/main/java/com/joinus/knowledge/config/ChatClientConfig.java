package com.joinus.knowledge.config;

import com.volcengine.ark.runtime.service.ArkService;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Configuration
public class ChatClientConfig {

    @Value("${spring.ai.openai.base-url}")
    private String baseUrl;
    @Value("${spring.ai.openai.api-key}")
    private String apiKey;
    @Value("${spring.ai.openai.chat.options.model}")
    private String model;

    @Value("${spring.ai.deepseek-r1.base-url}")
    private String deepSeekR1BaseUrl;
    @Value("${spring.ai.deepseek-r1.api-key}")
    private String deepSeekR1ApiKey;
    @Value("${spring.ai.deepseek-r1.chat.options.model}")
    private String deepSeekR1Model;

    @Value("${volcengine.api-key}")
    private String volcengineApiKey;
    @Value("${volcengine.base-url}")
    private String volcengineBaseUrl;

    static ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);

    @Bean
    public OpenAiApi jysdQwenVLOpenAiApi() {
        return OpenAiApi.builder().apiKey(apiKey).baseUrl(baseUrl).build();
    }

    @Bean
    public OpenAiApi jysdDeepSeekR1OpenAiApi() {
        return OpenAiApi.builder().apiKey(deepSeekR1ApiKey).baseUrl(deepSeekR1BaseUrl).build();
    }

    @Bean
    public ChatClient jysdQwenVLChatClient() {
        return ChatClient.builder(OpenAiChatModel.builder().defaultOptions(OpenAiChatOptions.builder().model(model).build()).openAiApi(OpenAiApi.builder().baseUrl(baseUrl).apiKey(apiKey).build()).build()).build();
    }

    @Bean
    public ChatClient jysdDeepSeekR1ChatClient() {
        return ChatClient.builder(OpenAiChatModel.builder().defaultOptions(OpenAiChatOptions.builder().model(deepSeekR1Model).build()).openAiApi(OpenAiApi.builder().baseUrl(deepSeekR1BaseUrl).apiKey(deepSeekR1ApiKey).build()).build()).build();
    }

    @Bean
    public ArkService arkService() {
        return ArkService.builder()
                .dispatcher(new Dispatcher())
                .connectionPool(connectionPool)
                .timeout(Duration.ofSeconds(600))
                .connectTimeout(Duration.ofSeconds(20))
                .retryTimes(2)
                .apiKey(volcengineApiKey)
                .baseUrl(volcengineBaseUrl)
                .build();
    }
}
