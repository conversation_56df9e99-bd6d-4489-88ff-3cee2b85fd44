package com.joinus.knowledge.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.joinus.knowledge.config.base.BaseEntity;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

/**
 * Custom meta object handler for auto-filling fields
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    /**
     * Auto-fill for insert operations
     * @param metaObject meta object
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        // Support for both naming conventions
        LocalDateTime now = LocalDateTime.now();
        Date currentDate = new Date();
        
        // Default value for deleted field (0 = not deleted)
        this.strictInsertFill(metaObject, "deletedAt", Date.class, null);

        // New BaseEntity fields
        this.strictInsertFill(metaObject, "createdAt", Date.class, currentDate);
        this.strictInsertFill(metaObject, "updatedAt", Date.class, currentDate);
        
        // 生成UUID
        generateUUIDForEntity(metaObject);
    }

    /**
     * Auto-fill for update operations
     * @param metaObject meta object
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        // Support for both naming conventions
        this.strictUpdateFill(metaObject, "updatedAt", Date.class, new Date());
    }
    
    /**
     * 为实体生成UUID
     * @param metaObject 元对象
     */
    private void generateUUIDForEntity(MetaObject metaObject) {
        // 获取元对象的原始对象
        Object originalObject = metaObject.getOriginalObject();
        
        // 如果是BaseEntity的子类，并且id为空，则生成UUID
        if (originalObject instanceof BaseEntity) {
            BaseEntity entity = (BaseEntity) originalObject;
            entity.generateUUID();
        }
    }
}
