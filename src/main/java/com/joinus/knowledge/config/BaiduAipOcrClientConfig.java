package com.joinus.knowledge.config;

import com.baidu.aip.ocr.AipOcr;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BaiduAipOcrClientConfig {

    @Value("${baidu.aip.orc.app-id:56041263}")
    private String appId;
    @Value("${baidu.aip.orc.api.key:DAD9Ub0EpuIKbZs35tdkdVW7}")
    private String apiKey;
    @Value("${baidu.aip.orc.secret.key:el9VEaaE8bNlrVR3Zzt0mF8LVJLRGTf5}")
    private String secretKey;

    @Bean
    public AipOcr aipOcrClient() {
        AipOcr client =  new AipOcr(appId, apiKey, secretKey);
        client.setConnectionTimeoutInMillis(2000);
        return client;
    }
}
