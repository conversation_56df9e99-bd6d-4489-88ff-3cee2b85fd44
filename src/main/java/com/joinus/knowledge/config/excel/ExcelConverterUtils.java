package com.joinus.knowledge.config.excel;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;

import java.io.InputStream;

/**
 * Excel转换器工具类
 * 提供便捷的方法来注册常用的转换器
 */
public class ExcelConverterUtils {

    /**
     * 创建带有常用转换器的ExcelReaderBuilder
     * 
     * @param inputStream Excel文件输入流
     * @param headClass 头部类
     * @return 配置好转换器的ExcelReaderBuilder
     */
    public static ExcelReaderBuilder createReaderWithCommonConverters(InputStream inputStream, Class<?> headClass) {
        return EasyExcelFactory.read(inputStream)
                .head(headClass)
                .registerConverter(new UUIDConverter())
                .registerConverter(new GenericEnumConverter())
                .registerConverter(new JSONObjectConverter());
    }

    /**
     * 创建带有指定转换器的ExcelReaderBuilder
     * 
     * @param inputStream Excel文件输入流
     * @param headClass 头部类
     * @param converters 自定义转换器
     * @return 配置好转换器的ExcelReaderBuilder
     */
    public static ExcelReaderBuilder createReaderWithConverters(InputStream inputStream, Class<?> headClass, 
                                                              com.alibaba.excel.converters.Converter<?>... converters) {
        ExcelReaderBuilder builder = EasyExcelFactory.read(inputStream)
                .head(headClass)
                .registerConverter(new UUIDConverter())
                .registerConverter(new GenericEnumConverter())
                .registerConverter(new JSONObjectConverter());
        
        // 注册自定义转换器
        for (com.alibaba.excel.converters.Converter<?> converter : converters) {
            builder.registerConverter(converter);
        }
        
        return builder;
    }
}
