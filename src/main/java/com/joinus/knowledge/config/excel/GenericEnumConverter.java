package com.joinus.knowledge.config.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * 通用枚举转换器
 * 支持多种枚举值获取策略：
 * 1. 优先使用@EnumValue注解标记的字段
 * 2. 其次查找常见的值字段：value、name、type、desc
 * 3. 最后使用枚举的name()方法
 * 
 * 使用方法：
 * @ExcelProperty(value = "枚举字段", index = 0, converter = GenericEnumConverter.class)
 * private SomeEnum enumField;
 */
@Slf4j
public class GenericEnumConverter implements Converter<Enum<?>> {

    @Override
    public Class<Enum<?>> supportJavaTypeKey() {
        return (Class<Enum<?>>) (Class<?>) Enum.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 从Excel读取时，将字符串转换为枚举
     */
    @Override
    public Enum<?> convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
                                   GlobalConfiguration globalConfiguration) {
        String stringValue = cellData.getStringValue();
        if (stringValue == null || stringValue.trim().isEmpty()) {
            return null;
        }

        // 获取枚举类型
        Class<? extends Enum<?>> enumClass = getEnumClass(contentProperty);
        if (enumClass == null) {
            throw new RuntimeException("无法确定枚举类型");
        }

        return convertStringToEnum(stringValue.trim(), enumClass);
    }

    /**
     * 写入Excel时，将枚举转换为字符串
     */
    @Override
    public WriteCellData<String> convertToExcelData(Enum<?> value, ExcelContentProperty contentProperty,
                                                  GlobalConfiguration globalConfiguration) {
        if (value == null) {
            return new WriteCellData<>("");
        }

        String enumValue = getEnumValue(value);
        return new WriteCellData<>(enumValue);
    }

    /**
     * 获取枚举类型
     */
    @SuppressWarnings("unchecked")
    private Class<? extends Enum<?>> getEnumClass(ExcelContentProperty contentProperty) {
        try {
            // 从字段类型获取枚举类
            Field field = contentProperty.getField();
            if (field != null && field.getType().isEnum()) {
                return (Class<? extends Enum<?>>) field.getType();
            }

            // 从泛型参数获取
            Type genericType = field.getGenericType();
            if (genericType instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) genericType;
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                if (actualTypeArguments.length > 0 && actualTypeArguments[0] instanceof Class) {
                    Class<?> clazz = (Class<?>) actualTypeArguments[0];
                    if (clazz.isEnum()) {
                        return (Class<? extends Enum<?>>) clazz;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取枚举类型失败", e);
        }
        return null;
    }

    /**
     * 将字符串转换为枚举
     */
    @SuppressWarnings("unchecked")
    private <T extends Enum<T>> T convertStringToEnum(String value, Class<? extends Enum<?>> enumClass) {
        try {
            // 1. 首先尝试通过枚举名称匹配
            try {
                return Enum.valueOf((Class<T>) enumClass, value);
            } catch (IllegalArgumentException ignored) {
                // 继续尝试其他方式
            }

            // 2. 通过@EnumValue注解标记的字段匹配
            T enumByAnnotation = findEnumByAnnotatedField(value, (Class<T>) enumClass);
            if (enumByAnnotation != null) {
                return enumByAnnotation;
            }

            // 3. 通过常见字段名匹配
            T enumByCommonField = findEnumByCommonFields(value, (Class<T>) enumClass);
            if (enumByCommonField != null) {
                return enumByCommonField;
            }

            // 4. 如果都没找到，抛出异常
            throw new RuntimeException("无法将字符串 '" + value + "' 转换为枚举 " + enumClass.getSimpleName() + 
                    "。支持的值：" + getSupportedValues((Class<T>) enumClass));

        } catch (Exception e) {
            throw new RuntimeException("枚举转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通过@EnumValue注解标记的字段查找枚举
     */
    private <T extends Enum<T>> T findEnumByAnnotatedField(String value, Class<T> enumClass) {
        try {
            Field[] fields = enumClass.getDeclaredFields();
            Field enumValueField = null;

            // 查找带@EnumValue注解的字段
            for (Field field : fields) {
                if (field.isAnnotationPresent(EnumValue.class)) {
                    enumValueField = field;
                    break;
                }
            }

            if (enumValueField != null) {
                enumValueField.setAccessible(true);
                for (T enumConstant : enumClass.getEnumConstants()) {
                    Object fieldValue = enumValueField.get(enumConstant);
                    if (fieldValue != null && value.equals(fieldValue.toString())) {
                        return enumConstant;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("通过@EnumValue注解查找枚举失败", e);
        }
        return null;
    }

    /**
     * 通过常见字段名查找枚举
     */
    private <T extends Enum<T>> T findEnumByCommonFields(String value, Class<T> enumClass) {
        String[] commonFieldNames = {"value", "name", "type", "desc", "description"};
        
        for (String fieldName : commonFieldNames) {
            try {
                Field field = enumClass.getDeclaredField(fieldName);
                field.setAccessible(true);
                
                for (T enumConstant : enumClass.getEnumConstants()) {
                    Object fieldValue = field.get(enumConstant);
                    if (fieldValue != null && value.equals(fieldValue.toString())) {
                        return enumConstant;
                    }
                }
            } catch (NoSuchFieldException ignored) {
                // 字段不存在，继续尝试下一个
            } catch (Exception e) {
                log.warn("通过字段 {} 查找枚举失败", fieldName, e);
            }
        }
        return null;
    }

    /**
     * 获取枚举的字符串值
     */
    private String getEnumValue(Enum<?> enumValue) {
        try {
            // 1. 优先使用@EnumValue注解标记的字段
            Field[] fields = enumValue.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(EnumValue.class)) {
                    field.setAccessible(true);
                    Object value = field.get(enumValue);
                    return value != null ? value.toString() : enumValue.name();
                }
            }

            // 2. 尝试常见字段名
            String[] commonFieldNames = {"value", "name", "type", "desc"};
            for (String fieldName : commonFieldNames) {
                try {
                    Field field = enumValue.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object value = field.get(enumValue);
                    if (value != null) {
                        return value.toString();
                    }
                } catch (NoSuchFieldException ignored) {
                    // 字段不存在，继续尝试下一个
                }
            }

            // 3. 使用枚举名称
            return enumValue.name();

        } catch (Exception e) {
            log.warn("获取枚举值失败，使用枚举名称", e);
            return enumValue.name();
        }
    }

    /**
     * 获取枚举支持的所有值，用于错误提示
     */
    private <T extends Enum<T>> String getSupportedValues(Class<T> enumClass) {
        StringBuilder sb = new StringBuilder();
        for (T enumConstant : enumClass.getEnumConstants()) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append(getEnumValue(enumConstant));
        }
        return sb.toString();
    }
}
