# EasyExcel 转换器使用指南

本项目提供了一套完整的EasyExcel转换器，用于处理常见的数据类型转换。

## 🎯 推荐使用方案

### ⭐ GenericEnumConverter - 通用枚举转换器（强烈推荐）
**一个转换器处理所有枚举类型，无需为每个枚举单独创建转换器！**

```java
// ✅ 所有枚举都使用同一个转换器
@ExcelProperty(value = "标签类型", index = 0, converter = GenericEnumConverter.class)
private ExamTagType type;

@ExcelProperty(value = "试卷状态", index = 1, converter = GenericEnumConverter.class)
private ExamStateEnum state;

@ExcelProperty(value = "出版社", index = 2, converter = GenericEnumConverter.class)
private PublisherType publisher;
```

## 可用的转换器

### 1. UUIDConverter - UUID转换器
用于处理UUID类型的字段。

```java
@ExcelProperty(value = "试卷ID", index = 0, converter = UUIDConverter.class)
private UUID examId;
```

### 2. GenericEnumConverter - 通用枚举转换器 ⭐
支持所有枚举类型的自动转换，具有以下特性：

- **智能匹配策略**：
  1. 优先通过枚举名称匹配（如：`ALIAS`）
  2. 通过`@EnumValue`注解标记的字段匹配
  3. 通过常见字段名匹配：`value`、`name`、`type`、`desc`、`description`
  
- **支持的枚举类型**：
  - `ExamTagType`：通过`value`字段匹配（如：`ALIAS`、`ELITE_SCHOOL_EXAM_PAGERS`）
  - `ExamStateEnum`：通过`@EnumValue`注解的`value`字段匹配（如：`TODO`、`DONE`）
  - `PublisherType`：通过`value`字段匹配（如：`北师大`、`人教`）
  - `QuestionType`：通过`type`字段匹配（如：`单选题`、`填空题`）

```java
@ExcelProperty(value = "标签类型", index = 2, converter = GenericEnumConverter.class)
private ExamTagType type;

@ExcelProperty(value = "试卷状态", index = 3, converter = GenericEnumConverter.class)
private ExamStateEnum state;
```

### 3. JSONObjectConverter - JSON对象转换器
用于处理JSON字符串与JSONObject之间的转换。

```java
@ExcelProperty(value = "标签属性", index = 4, converter = JSONObjectConverter.class)
private JSONObject properties;
```

## 使用方法

### 🚀 方法一：使用通用转换器（强烈推荐）

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExcelParam implements Serializable {

    @ExcelProperty(value = "试卷ID", index = 0, converter = UUIDConverter.class)
    private UUID examId;

    // ✅ 所有枚举都使用GenericEnumConverter
    @ExcelProperty(value = "标签类型", index = 1, converter = GenericEnumConverter.class)
    private ExamTagType type;

    @ExcelProperty(value = "试卷状态", index = 2, converter = GenericEnumConverter.class)
    private ExamStateEnum state;

    @ExcelProperty(value = "出版社", index = 3, converter = GenericEnumConverter.class)
    private PublisherType publisher;

    @ExcelProperty(value = "属性", index = 4, converter = JSONObjectConverter.class)
    private JSONObject properties;
}
```

**优势：**
- ✅ 一个转换器处理所有枚举
- ✅ 自动识别枚举值字段
- ✅ 无需维护多个转换器
- ✅ 新增枚举无需额外配置

### 方法二：使用工具类

```java
// 使用工具类创建带有常用转换器的Reader
List<ExcelParam> dataList = ExcelConverterUtils
    .createReaderWithCommonConverters(file.getInputStream(), ExcelParam.class)
    .sheet()
    .doReadSync();
```

### 方法三：手动注册转换器

```java
List<ExcelParam> dataList = EasyExcel.read(file.getInputStream())
    .head(ExcelParam.class)
    .registerConverter(new UUIDConverter())
    .registerConverter(new GenericEnumConverter())
    .registerConverter(new JSONObjectConverter())
    .sheet()
    .doReadSync();
```

## Excel文件格式要求

### 枚举字段的Excel值格式

| 枚举类型 | Excel中的值 | 说明 |
|---------|------------|------|
| ExamTagType | `ALIAS` 或 `别名` | 支持枚举名称或描述 |
| ExamStateEnum | `TODO` 或 `待分析` | 支持value值或描述 |
| PublisherType | `北师大` | 使用中文描述 |
| QuestionType | `单选题` | 使用中文类型名称 |

### UUID字段格式
```
550e8400-e29b-41d4-a716-************
```

### JSON字段格式
```json
{"key1": "value1", "key2": "value2"}
```

## 错误处理

转换器会提供详细的错误信息：

- **UUID格式错误**：`无法将字符串 'invalid-uuid' 转换为UUID`
- **枚举值错误**：`无法将字符串 'INVALID' 转换为枚举 ExamTagType。支持的值：ALIAS(别名), ELITE_SCHOOL_EXAM_PAGERS(名校试卷)`
- **JSON格式错误**：`JSON格式错误: invalid json string`

## 扩展自定义枚举转换器

如果需要为特定枚举创建专用转换器：

```java
public class CustomEnumConverter implements Converter<CustomEnum> {
    
    @Override
    public Class<CustomEnum> supportJavaTypeKey() {
        return CustomEnum.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public CustomEnum convertToJavaData(ReadCellData<?> cellData, 
                                      ExcelContentProperty contentProperty,
                                      GlobalConfiguration globalConfiguration) {
        // 自定义转换逻辑
        String value = cellData.getStringValue();
        return CustomEnum.fromValue(value);
    }

    @Override
    public WriteCellData<String> convertToExcelData(CustomEnum value, 
                                                  ExcelContentProperty contentProperty,
                                                  GlobalConfiguration globalConfiguration) {
        return new WriteCellData<>(value.getValue());
    }
}
```
