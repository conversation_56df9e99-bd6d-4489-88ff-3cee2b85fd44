package com.joinus.knowledge.config.excel;

import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * EasyExcel JSONObject转换器
 * 用于处理Excel中JSON字符串与JSONObject之间的转换
 */
public class JSONObjectConverter implements Converter<JSONObject> {

    @Override
    public Class<JSONObject> supportJavaTypeKey() {
        return JSONObject.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 从Excel读取时，将JSON字符串转换为JSONObject
     */
    @Override
    public JSONObject convertToJavaData(ReadCellData<?> cellData, 
                                      ExcelContentProperty contentProperty,
                                      GlobalConfiguration globalConfiguration) {
        String jsonString = cellData.getStringValue();
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            return JSONUtil.parseObj(jsonString.trim());
        } catch (JSONException e) {
            throw new RuntimeException("JSON格式错误: " + jsonString, e);
        }
    }

    /**
     * 写入Excel时，将JSONObject转换为JSON字符串
     */
    @Override
    public WriteCellData<String> convertToExcelData(JSONObject value, 
                                                  ExcelContentProperty contentProperty,
                                                  GlobalConfiguration globalConfiguration) {
        return new WriteCellData<>(value != null ? value.toString() : "");
    }
}
