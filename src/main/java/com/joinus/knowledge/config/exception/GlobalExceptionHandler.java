package com.joinus.knowledge.config.exception;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.config.base.BaseException;
import com.joinus.knowledge.config.base.BusinessException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

/**
 * 全局异常处理器
 * 使用 RestControllerAdvice 注解，确保与 Spring Boot 3.4.3 兼容
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    public Result businessExceptionHandler(BusinessException ex) {
        log.warn("业务异常: {}", ex.getMessage());
        return Result.error(ex.getCode(), ex.getMessage());
    }

    /**
     * 处理基础异常
     */
    @ExceptionHandler(BaseException.class)
    @ResponseBody
    public Result baseExceptionHandler(BaseException ex) {
        log.error("基础异常: {}", ex.getMessage());
        return Result.error(ex.getCode(), ex.getMessage());
    }

    /**
     * 处理方法参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public Result handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        String msg = ex.getBindingResult().getFieldError().getDefaultMessage();
        log.warn("参数验证异常: {}", msg);
        return Result.error(HttpStatus.BAD_REQUEST.value(), msg);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public Result handleBindException(BindException ex) {
        String msg = ex.getBindingResult().getFieldError().getDefaultMessage();
        log.warn("绑定异常: {}", msg);
        return Result.error(HttpStatus.BAD_REQUEST.value(), msg);
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseBody
    public Result handleMaxSizeException(MaxUploadSizeExceededException exc) {
        String msg = "文件大小超过限制";
        log.warn("文件上传异常: {}", msg);
        return Result.error(HttpStatus.BAD_REQUEST.value(), msg);
    }

    /**
     * 处理所有其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Result handleException(HttpServletRequest request, Exception ex) {
        String msg;
        int code;

        if (ex instanceof org.springframework.web.servlet.NoHandlerFoundException) {
            code = HttpStatus.NOT_FOUND.value();
            msg = "请求的资源不存在: " + request.getRequestURI();
            log.warn("资源不存在: {}", request.getRequestURI());
        } else if (ex instanceof org.springframework.web.HttpRequestMethodNotSupportedException) {
            code = HttpStatus.METHOD_NOT_ALLOWED.value();
            msg = "不支持的请求方法: " + request.getMethod();
            log.warn("不支持的请求方法: {}", request.getMethod());
        } else if (ex instanceof RuntimeException) {
            code = HttpStatus.BAD_REQUEST.value();
            msg = "运行时异常: " + ex.getMessage();
            log.error("运行时异常", ex);
        } else {
            code = HttpStatus.INTERNAL_SERVER_ERROR.value();
            msg = "系统内部错误: " + ex.getMessage();
            log.error("未预期的异常", ex);
        }

        return Result.error(code, msg);
    }
}
