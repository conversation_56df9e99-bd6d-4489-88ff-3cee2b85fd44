package com.joinus.knowledge.config.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

/**
 * PostgreSQL的UUID类型处理器
 * 处理Java的UUID对象和PostgreSQL数据库UUID类型之间的转换
 * 这个处理器特别针对PostgreSQL的UUID数据类型进行了优化
 */
@MappedTypes(UUID.class)
@MappedJdbcTypes(JdbcType.OTHER)
public class PostgresUUIDTypeHandler extends BaseTypeHandler<UUID> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, UUID parameter, JdbcType jdbcType) throws SQLException {
        ps.setObject(i, parameter);
    }

    @Override
    public UUID getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object uuid = rs.getObject(columnName);
        if (uuid == null) {
            return null;
        }
        return toUUID(uuid);
    }

    @Override
    public UUID getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object uuid = rs.getObject(columnIndex);
        if (uuid == null) {
            return null;
        }
        return toUUID(uuid);
    }

    @Override
    public UUID getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object uuid = cs.getObject(columnIndex);
        if (uuid == null) {
            return null;
        }
        return toUUID(uuid);
    }

    private static UUID toUUID(Object uuid) {
        if (uuid instanceof UUID) {
            return (UUID) uuid;
        }
        return UUID.fromString(uuid.toString());
    }
}
