package com.joinus.knowledge.config.typehandler;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.CallableStatement;
import java.sql.SQLException;
import java.sql.Types;

import com.joinus.knowledge.enums.PgAIModelType;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

@MappedTypes(PgAIModelType.class)
public class AiModelTypeHandler extends BaseTypeHandler<PgAIModelType> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, PgAIModelType parameter, JdbcType jdbcType)
            throws SQLException {
        // Postgres 需要用 Types.OTHER 来处理自定义枚举类型
        ps.setObject(i, parameter.name(), Types.OTHER);
    }

    @Override
    public PgAIModelType getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String name = rs.getString(columnName);
        return name == null ? null : PgAIModelType.valueOf(name);
    }

    @Override
    public PgAIModelType getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String name = rs.getString(columnIndex);
        return name == null ? null : PgAIModelType.valueOf(name);
    }

    @Override
    public PgAIModelType getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String name = cs.getString(columnIndex);
        return name == null ? null : PgAIModelType.valueOf(name);
    }
}