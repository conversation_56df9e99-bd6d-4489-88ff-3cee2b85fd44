package com.joinus.knowledge.config.logging;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;

/**
 * 请求响应日志过滤器
 * 用于包装请求和响应对象，以便可以多次读取请求体和响应体
 */
@Slf4j
@Component
public class RequestResponseLoggingFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 检查是否为流式API
        String requestURI = httpRequest.getRequestURI();
        boolean isStreamingEndpoint = isStreamingEndpoint(requestURI);
        
        // 对于流式API，不使用ContentCachingResponseWrapper
        if (isStreamingEndpoint) {
            log.debug("检测到流式API: {}, 不包装响应体", requestURI);
            // 只包装请求
            ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(httpRequest);
            chain.doFilter(requestWrapper, response);
        } else {
            // 对于普通API，包装请求和响应
            ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(httpRequest);
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(httpResponse);
            
            try {
                // 继续过滤器链
                chain.doFilter(requestWrapper, responseWrapper);
            } finally {
                // 注意: 响应体内容在拦截器的afterCompletion方法中被复制回原始流
            }
        }
    }
    
    /**
     * 判断是否为流式API端点
     */
    private boolean isStreamingEndpoint(String requestURI) {
        // 检查URL是否包含流式API的关键字
        return requestURI != null && (
            requestURI.contains("/stream") || 
            requestURI.contains("/sse") || 
            requestURI.contains("/push")
        );
    }
}
