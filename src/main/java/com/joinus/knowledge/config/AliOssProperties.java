package com.joinus.knowledge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "ali.oss")
public class AliOssProperties {

    /** 阿里OSS配置参数 **/
    private String region;
    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    /** 阿里OSS配置参数 **/
    private String baseDir;

    private Sts sts;  // 对应ali.oss.sts前缀的配置

    @Data
    public static class Sts {
        private String roleArn;  // 对应ali.oss.sts.role-arn
    }

}
