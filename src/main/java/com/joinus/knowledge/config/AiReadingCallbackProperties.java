package com.joinus.knowledge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "ai.reading.callback")
public class AiReadingCallbackProperties {

    /**
     * 回调接口服务器地址
     */
    private String host;
    /**
     * AI批改回调接口地址
     */
    private String correctUrl;
    /**
     * 薄弱知识点分析回调接口地址
     */
    private String weakKnowledgePointAnalysisUrl;
    /**
     * 综合训练建议回调接口地址
     */
    private String trainingSuggestionUrl;
    /**
     * 薄弱题型分析回调接口地址
     */
    private String weakQuestionTypeAnalysisUrl;

    /**
     * AI语文通用回调地址
     */
    private String commonUrl;

}
