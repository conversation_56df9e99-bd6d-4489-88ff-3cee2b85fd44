package com.joinus.knowledge.config;

import com.joinus.knowledge.config.logging.RequestResponseLoggingInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置类
 * 用于注册拦截器等
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private RequestResponseLoggingInterceptor requestResponseLoggingInterceptor;

    @Value("${cors.allowed-headers:User-Name}")
    private String[] allowedHeaders;

    // 响应头配置
    @Value("${cors.exposed-headers:Authorization}")
    private String[] exposedHeaders;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加请求响应日志拦截器，应用于所有请求
        registry.addInterceptor(requestResponseLoggingInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/error"); // 排除错误页面
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")  // 或指定具体域名
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders(allowedHeaders)
                .exposedHeaders(exposedHeaders)  // 暴露自定义header
                .allowCredentials(true)
                .maxAge(3600);
    }
}
