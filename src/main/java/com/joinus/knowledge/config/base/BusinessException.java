package com.joinus.knowledge.config.base;

import org.springframework.http.HttpStatus;

/**
 * 业务逻辑异常
 * 用于表示由于业务逻辑错误导致的异常，通常对应 HTTP 400 错误
 */
public class BusinessException extends RuntimeException {
    
    private final Integer code;
    
    /**
     * 创建一个业务逻辑异常，默认使用 400 (BAD_REQUEST) 错误码
     * 
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
        this.code = HttpStatus.BAD_REQUEST.value();
    }
    
    /**
     * 创建一个业务逻辑异常，使用指定的错误码
     * 
     * @param message 错误消息
     * @param code 错误码
     */
    public BusinessException(String message, Integer code) {
        super(message);
        this.code = code;
    }
    
    /**
     * 获取错误码
     * 
     * @return 错误码
     */
    public Integer getCode() {
        return code;
    }
}
