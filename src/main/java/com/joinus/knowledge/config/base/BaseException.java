/**
 * <AUTHOR>
 * @Date 2024/12/12 16:14
 */
package com.joinus.knowledge.config.base;

import lombok.Getter;

public class BaseException extends RuntimeException {
    private final String message;
    @Getter
    private final Integer code;

    public BaseException(String message, Integer code) {
        super(message);
        this.message = message;
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

}
