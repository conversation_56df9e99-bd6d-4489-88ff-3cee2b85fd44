package com.joinus.knowledge.config.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * Base entity class with common fields for all entities
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BaseEntity implements Serializable {
    
    /**
     * Primary key - UUID
     */
    @TableId(type = IdType.INPUT)
    private UUID id;
    
    /**
     * Creation time - auto-filled when inserting
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;
    
    /**
     * Update time - auto-filled when inserting or updating
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;
    
    /**
     * Deletion flag - for logical delete
     */
    @TableLogic(value = "null", delval = "now()")
    private Date deletedAt;
    
    /**
     * 在实体保存前生成UUID
     */
    public void generateUUID() {
        if (this.id == null) {
            this.id = UUID.randomUUID();
        }
    }
    
    /**
     * Serial version UID
     */
    private static final long serialVersionUID = 1L;
}
