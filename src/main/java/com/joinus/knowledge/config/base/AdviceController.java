//package com.joinus.knowledge.config.base;
//
//import com.joinus.knowledge.common.Result;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.HttpStatus;
//import org.springframework.validation.BindException;
//import org.springframework.web.bind.MethodArgumentNotValidException;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.multipart.MaxUploadSizeExceededException;
//import org.springframework.web.servlet.mvc.support.RedirectAttributes;
//
//@ControllerAdvice
//@Slf4j
//public class AdviceController {
//    /**
//     * 处理业务逻辑异常
//     */
//    @ExceptionHandler(BusinessException.class)
//    @ResponseBody
//    public Result<Message> businessExceptionHandler(BusinessException ex) {
//        log.warn("业务异常: {}", ex.getMessage());
//        return Result.error(ex.getCode(), ex.getMessage());
//    }
//
//    /**
//     * 处理通用异常
//     */
//    @ExceptionHandler(Exception.class)
//    @ResponseBody
//    public Result<Message> exceptionHandler(HttpServletRequest request, HttpServletResponse response,
//                                                           Exception ex) {
//        String msg;
//        Integer code;
//
//        if (ex instanceof BaseException) {
//            msg = ex.getMessage();
//            code = ((BaseException) ex).getCode();
//        } else if (ex instanceof MethodArgumentNotValidException) {
//            msg = ((MethodArgumentNotValidException) ex).getBindingResult().getFieldError().getDefaultMessage();
//            code = HttpStatus.BAD_REQUEST.value();
//        } else if (ex instanceof BindException) {
//            msg = ((BindException) ex).getBindingResult().getFieldError().getDefaultMessage();
//            code = HttpStatus.BAD_REQUEST.value();
//        } else if (ex instanceof RuntimeException) {
//            // 特别处理 RuntimeException，使用 400 错误码
//            msg = ex.getMessage();
//            code = HttpStatus.BAD_REQUEST.value();
//            log.error("RuntimeException: {}", msg, ex);
//        } else {
//            // 其他类型的异常使用 500 错误码
//            msg = ex.getMessage() != null ? ex.getMessage() : "服务器内部错误";
//            code = HttpStatus.INTERNAL_SERVER_ERROR.value();
//            log.error("Unexpected exception: {}", msg, ex);
//        }
//
//        return Result.error(code, msg);
//    }
//
//    /**
//     * 处理文件上传大小超限异常
//     */
//    @ExceptionHandler(MaxUploadSizeExceededException.class)
//    @ResponseBody
//    public Result<Message> handleMaxSizeException(MaxUploadSizeExceededException exc, RedirectAttributes redirectAttributes) {
//        String msg = "文件大小超过限制";
//        return Result.error(HttpStatus.BAD_REQUEST.value(), msg);
//    }
//}
