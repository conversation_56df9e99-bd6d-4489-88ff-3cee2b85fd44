package com.joinus.knowledge.config.handler;

/**
 * 自定义Date类型逻辑删除处理器
 * 处理SQL查询中逻辑删除字段为Date类型的情况
 */
public class DateLogicDeleteHandler {
    
    /**
     * 修改SQL拼接方式，处理timestamp类型
     * @param tableName 表名
     * @param fieldName 字段名
     * @return 生成的SQL片段
     */
    public static String getDeletedCondition(String tableName, String fieldName) {
        StringBuilder sql = new StringBuilder();
        sql.append(tableName).append(".").append(fieldName).append(" IS NULL");
        return sql.toString();
    }
    
    /**
     * 生成更新SQL，设置删除字段为当前时间
     * @param tableName 表名
     * @param fieldName 字段名
     * @return 生成的SQL片段
     */
    public static String getSetDeletedSql(String tableName, String fieldName) {
        return tableName + "." + fieldName + " = NOW()";
    }
}
