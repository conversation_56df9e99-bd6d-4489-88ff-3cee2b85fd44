package com.joinus.knowledge.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Knife4j配置类
 */
@Configuration
public class Knife4jConfig {

    @Bean
    public OpenAPI springOpenAPI() {
        // 创建API基本信息
        return new OpenAPI()
                .info(new Info()
                        .title("教育知识平台 API文档")
                        .description("本文档描述了教育知识平台服务接口定义")
                        .version("v1.0.0")
                        .contact(new Contact()
                                .name("教育知识平台团队")
                                .email("<EMAIL>")
                                .url("https://www.example.com")))
                .externalDocs(new ExternalDocumentation()
                        .description("更多信息请访问")
                        .url("https://example.com/docs"))
                // 添加认证方案
                .components(new Components()
                        .addSecuritySchemes("basicAuth", 
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("basic")
                                        .description("Knife4j接口文档基本认证")))
                // 应用认证到所有接口
                .addSecurityItem(new SecurityRequirement().addList("basicAuth"));
    }
}
