package com.joinus.knowledge.temporal.workflow.impl;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.QuestionFile;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import com.joinus.knowledge.model.vo.KnowledgePointsAndDifficultyVO;
import com.joinus.knowledge.temporal.activity.AnalyzeExamKnowledgePointsActivity;
import com.joinus.knowledge.temporal.activity.CutMathExamActivity;
import com.joinus.knowledge.temporal.activity.MathExamActivity;
import com.joinus.knowledge.temporal.workflow.MathExamReanalyzeKnowledgePointWorkflow;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 切题工作流实现类
 */
@Slf4j
@Component
@WorkflowImpl(taskQueues = GlobalConstants.MATH_EXAM_REANALYZE_KNOWLWDGE_POINT_TASK_QUEUE)
public class MathExamReanalyzeKnowledgePointWorkflowImpl implements MathExamReanalyzeKnowledgePointWorkflow {
    
    @Value("${temporal.activity.timeout.minutes:10}")
    private int activityTimeoutMinutes;
    
    @Value("${temporal.activity.retry.maxAttempts:2}")
    private int maxRetryAttempts;
    
    /**
     * 用户选择母卷，但是母卷对应用户选择的教材版本没有分析，需要重新分析
     * 如母卷已经针对北师大版的知识点分析过，但是没有对人教版的知识点分析过，需要重新分析
     *
     * @param examParam 试卷参数
     */
    @Override
    public void submit(AnalyzeExamParam examParam) {
        // 创建活动选项，设置超时和重试策略
        ActivityOptions options = ActivityOptions.newBuilder()
                .setStartToCloseTimeout(Duration.ofMinutes(activityTimeoutMinutes))
                .setRetryOptions(RetryOptions.newBuilder()
                        .setMaximumAttempts(maxRetryAttempts)
                        .build())
                .build();
        
        // 创建活动存根
        MathExamActivity mathExamActivity = Workflow.newActivityStub(MathExamActivity.class, options);
        AnalyzeExamKnowledgePointsActivity analyzeActivity = Workflow.newActivityStub(AnalyzeExamKnowledgePointsActivity.class, options);


        try {
            // 获取试卷信息
            UUID examId = examParam.getExamId();
            String logPrefix = "MathExamReanalyzeKnowledgePointWorkflow " + examId;
            Workflow.getLogger(MathExamReanalyzeKnowledgePointWorkflowImpl.class).info("开始处理试卷切题: examId={}", examId);
            log.info(logPrefix + "开始处理试卷 param {}", JSONUtil.toJsonStr(examParam));

            // 获取考试信息
            MathExam exam = mathExamActivity.getExamById(examId);
            if (exam == null) {
                throw new IllegalArgumentException(logPrefix + "未找到ID为 " + examId + " 的考试");
            }
            if (null == examParam.getPublisher()) {
                log.info(logPrefix + "未指定教材版本: examId={}", examId);
                throw new IllegalArgumentException(logPrefix + "未指定教材版本");
            }
            if (exam.getState() != ExamStateEnum.DONE) {
                log.info(logPrefix + "考试之前未解析完成，请等待考试完成: examId={}", examId);
                throw new IllegalArgumentException(logPrefix + "考试之前未解析完成，请等待考试完成");
            }

            solveQuestionAndRelateKnowledgePoints(exam, examParam.getPublisher(), mathExamActivity, analyzeActivity);
            // 设置自动分析结果
            examParam.setAutoAnalyzeSuccess(true);

            // 回调通知
            log.info("{} 执行回调 {}", logPrefix, JSONUtil.toJsonStr(examParam));
            analyzeActivity.callbackForAutoAnalyze(examParam, logPrefix);

            log.info("{} 考试分析工作流完成", logPrefix);
        } catch (Exception e) {
            Workflow.getLogger(MathExamReanalyzeKnowledgePointWorkflowImpl.class).error("切题工作流执行异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    private void solveQuestionAndRelateKnowledgePoints(MathExam mathExam, PublisherType publisher, MathExamActivity mathExamActivity, AnalyzeExamKnowledgePointsActivity analyzeActivity) {
        // 根据试卷id查询题目列表 - 使用CutMathExamActivity的getExamQuestions方法
        List<MathQuestion> questions = mathExamActivity.getExamQuestions(mathExam.getId());
        Workflow.getLogger(this.getClass()).info("获取到题目数量: {}", questions.size());

        if (questions.isEmpty()) {
            Workflow.getLogger(this.getClass()).warn("试卷中没有题目: examId={}", mathExam.getId());
            mathExamActivity.updateExamState(mathExam.getId(), ExamStateEnum.DONE);
            return;
        }

        List<QuestionFile> questionFiles = mathExamActivity.listQuestionFile(questions.stream().map(MathQuestion::getId).toList());

        Map<UUID, List<UUID>> questionFileList = questionFiles.stream()
                .collect(Collectors.groupingBy(QuestionFile::getQuestionId, Collectors.mapping(QuestionFile::getFileId, Collectors.toList())));

        // 方案一：使用 Promise.allOf() 进行批量异步处理（推荐）
        int maxConcurrency = 8; // 最大并发数
        List<List<MathQuestion>> questionBatches = Lists.partition(questions, maxConcurrency);

        for (List<MathQuestion> batch : questionBatches) {
            // 为当前批次创建异步任务
            List<Promise<Map<String, Object>>> promises = batch.stream()
                    .map(question -> Async.function(() -> {
                        try {
                            return solveSingleQuestionAndRelateKnowledgePoints(question, mathExam, questionFileList, analyzeActivity, publisher);
                        } catch (Exception e) {
                            Workflow.getLogger(this.getClass()).error("处理题目失败: questionId={}, 错误={}",
                                    question.getId(), e.getMessage());
                            // 返回空结果，不中断整个流程
                            return null;
                        }
                    }))
                    .toList();

            // 等待当前批次的所有任务完成
            Promise.allOf(promises).get();

            Workflow.getLogger(this.getClass()).info("完成批次处理，批次大小: {}", batch.size());
        }
    }

    private Map<String, Object> solveSingleQuestionAndRelateKnowledgePoints(MathQuestion question, MathExam mathExam, Map<UUID, List<UUID>> questionFileList, AnalyzeExamKnowledgePointsActivity analyzeActivity, PublisherType publisher) {
        HashMap<String, Object> result = new HashMap<>();
        analyzeActivity.solveQuestion(question, mathExam, questionFileList);
        KnowledgePointsAndDifficultyVO knowledgePointsAndDifficultyVO = analyzeActivity.analyzeQuestionKnowledgePoints(question, mathExam.getGrade(), mathExam.getSemester(), publisher);
        result.put("question", question);
        result.put("knowledgePointsAndDifficultyVO", knowledgePointsAndDifficultyVO);
        return result;
    }
}
