package com.joinus.knowledge.temporal.workflow.impl;

import com.google.common.collect.Lists;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.ExamSourceType;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.temporal.activity.AnalyzeExamKnowledgePointsActivity;
import com.joinus.knowledge.temporal.activity.MathExamActivity;
import com.joinus.knowledge.temporal.workflow.AnalyzeQuestionsKnowledgePointWorkflow;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@WorkflowImpl(taskQueues = GlobalConstants.ANALYZE_QUESTION_KNOWLEDGE_POINTS_TASK_QUEUE)
public class AnalyzeQuestionsKnowledgePointWorkflowImpl implements AnalyzeQuestionsKnowledgePointWorkflow {

    @Override
    public void execute() {
        ActivityOptions options = ActivityOptions.newBuilder()
                .setStartToCloseTimeout(Duration.ofMinutes(10))
                .setRetryOptions(RetryOptions.newBuilder()
                        .setMaximumAttempts(2)
                        .setInitialInterval(Duration.ofSeconds(1))
                        .setMaximumInterval(Duration.ofMinutes(1))
                        .setBackoffCoefficient(2.0)
                        .build())
                .build();
        AnalyzeExamKnowledgePointsActivity analyzeExamKnowledgePointsActivity = Workflow.newActivityStub(AnalyzeExamKnowledgePointsActivity.class, options);
        MathExamActivity mathExamActivity = Workflow.newActivityStub(MathExamActivity.class, options);
        //查询人工识别过的中考试卷，常规考试卷
        List<MathExam> exams = mathExamActivity.listExamByStateAndSources(ExamStateEnum.HUMAN_RECOGNIZED, List.of(ExamSourceType.PAST_EXAM_PAPER, ExamSourceType.REGULAR_EXAM_PAPER));

        Workflow.getLogger(this.getClass()).info("获取到待分析中考真题试卷，常规考试卷数量: {}", exams.size());

        for (MathExam mathExam : exams) {
            List<MathQuestion> questions = mathExamActivity.getExamQuestionDetails(mathExam.getId());
            if (questions.isEmpty()) {
                continue;
            }

            Workflow.getLogger(this.getClass()).info("真题试卷: {},试题数量: {}", mathExam.getId(), questions.size());

            processQuestionsBatchAsync(questions, mathExam, analyzeExamKnowledgePointsActivity);

            Workflow.getLogger(this.getClass()).info("真题试卷分析完成: {}", mathExam.getId());

            mathExamActivity.updateExamState(mathExam.getId(), ExamStateEnum.DONE);

            Workflow.getLogger(this.getClass()).info("真题试卷更新状态DONE: {}", mathExam.getId());

        }
    }

    private Map<String, Object> solveQuestionAndRelateKnowledgePoints(MathQuestion question, MathExam mathExam, AnalyzeExamKnowledgePointsActivity analyzeExamKnowledgePointsActivity) {
        HashMap<String, Object> result = new HashMap<>();
        analyzeExamKnowledgePointsActivity.solveQuestionViaContentParts(question);

        for (PublisherType publisher : PublisherType.values()) {
            analyzeExamKnowledgePointsActivity.analyzeQuestionKnowledgePoints(question, mathExam.getGrade(), mathExam.getSemester(), publisher);
        }
        result.put("question", question);
        return result;
    }

    private void processQuestionsBatchAsync(List<MathQuestion> questions, MathExam mathExam, AnalyzeExamKnowledgePointsActivity analyzeExamKnowledgePointsActivity) {

        int maxConcurrency = 5; // 最大并发数

        List<List<MathQuestion>> questionBatches = Lists.partition(questions, maxConcurrency);

        for (List<MathQuestion> batch : questionBatches) {
            // 为当前批次创建异步任务
            List<Promise<Map<String, Object>>> promises = batch.stream()
                    .map(question -> Async.function(() -> {
                        try {
                            return solveQuestionAndRelateKnowledgePoints(question, mathExam, analyzeExamKnowledgePointsActivity);
                        } catch (Exception e) {
                            Workflow.getLogger(this.getClass()).error("处理题目失败: questionId={}, 错误={}",
                                    question.getId(), e.getMessage());
                            // 返回空结果，不中断整个流程
                            return null;
                        }
                    }))
                    .toList();

            // 等待当前批次的所有任务完成
            Promise.allOf(promises).get();

            Workflow.getLogger(this.getClass()).info("完成批次处理，批次大小: {}", batch.size());
        }
    }
}
