package com.joinus.knowledge.temporal.workflow.impl;

import com.google.common.collect.Lists;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.temporal.activity.ChatCompletionsActivity;
import com.joinus.knowledge.temporal.activity.FileActivity;
import com.joinus.knowledge.temporal.activity.SaveBookInfoActivity;
import com.joinus.knowledge.temporal.workflow.ImageOcrWorkflow;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
@WorkflowImpl(taskQueues = GlobalConstants.IMAGE_OCR_TASK_QUEUE)
public class ImageOcrWorkflowImpl implements ImageOcrWorkflow {

    ActivityOptions options = ActivityOptions.newBuilder()
            .setStartToCloseTimeout(Duration.ofMinutes(5))
            .setHeartbeatTimeout(Duration.ofMinutes(5))
            .setRetryOptions(RetryOptions.newBuilder()
                    .setMaximumAttempts(2)
                    .build())
            .build();

    @Override
    public String submit(String bookId) {
        FileActivity fileActivity = Workflow.newActivityStub(FileActivity.class, options);

        List<Map<String, Object>> files = fileActivity.getFilesByBookId(bookId);
        files = files.stream().filter(map -> map.get("ocr_html") == null).toList();

        int concurrentSize = 5;

        List<List<Map<String, Object>>> filesList = Lists.partition(files,concurrentSize);

        for (List<Map<String, Object>> list : filesList) {
            List<Promise<String>> promiseList = list.stream().map(data -> Async.function(() -> ocrHtml(data, fileActivity))).toList();
            promiseList.forEach(Promise::get);
        }

        return null;
    }

    protected String ocrHtml(Map<String, Object> map, FileActivity fileActivity) {
        ChatCompletionsActivity chatCompletionsActivity = Workflow.newActivityStub(ChatCompletionsActivity.class, options);
        SaveBookInfoActivity saveBookInfoActivity = Workflow.newActivityStub(SaveBookInfoActivity.class, options);

        String promptText = """
            QwenVL HTML
            """;
        String imageUrl = fileActivity.getPresignedObjectUrl(map.get("oss_url").toString());
        String assistantText = chatCompletionsActivity.chatWithImage(promptText, imageUrl);
        Pattern pattern = Pattern.compile("(?s)```(?:\\w+)?\\s*(.*?)\\s*```");
        Matcher matcher = pattern.matcher(assistantText);
        String htmlContent;
        if (matcher.find()) {
            htmlContent = matcher.group(1);
            log.info(htmlContent);
            saveBookInfoActivity.saveOcrHtml(map.get("id").toString(), htmlContent);
        }
        return "";
    }

}
