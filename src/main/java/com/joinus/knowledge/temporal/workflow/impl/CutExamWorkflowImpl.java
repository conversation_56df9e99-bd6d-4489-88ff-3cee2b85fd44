package com.joinus.knowledge.temporal.workflow.impl;

import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import com.joinus.knowledge.temporal.activity.CutMathExamActivity;
import com.joinus.knowledge.temporal.workflow.CutExamWorkflow;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 切题工作流实现类
 */
@Slf4j
@Component
@WorkflowImpl(taskQueues = GlobalConstants.CUT_EXAM_TASK_QUEUE)
public class CutExamWorkflowImpl implements CutExamWorkflow {
    
    @Value("${temporal.activity.timeout.minutes:10}")
    private int activityTimeoutMinutes;
    
    @Value("${temporal.activity.retry.maxAttempts:2}")
    private int maxRetryAttempts;
    
    @Value("${temporal.batch.size:10}")
    private int batchSize;

    /**
     * 用户主动提交纠错逻辑(耿总的summary接口题号也不正确)
     *
     * @param examParam 试卷参数
     */
    @Override
    public void submit(AnalyzeExamParam examParam) {
        // 创建活动选项，设置超时和重试策略
        ActivityOptions options = ActivityOptions.newBuilder()
                .setStartToCloseTimeout(Duration.ofMinutes(activityTimeoutMinutes))
                .setRetryOptions(RetryOptions.newBuilder()
                        .setMaximumAttempts(maxRetryAttempts)
                        .build())
                .build();
        
        // 创建活动存根
        CutMathExamActivity cutExamActivity = Workflow.newActivityStub(CutMathExamActivity.class, options);

        String logPrefix = "CutExamWorkflow " + examParam.getExamId();

        try {
            // 获取试卷信息
            UUID examId = examParam.getExamId();
            Workflow.getLogger(CutExamWorkflowImpl.class).info("开始处理试卷切题: examId={}", examId);
            
            // 通过活动获取试卷中的所有题目
            List<MathQuestion> questions = cutExamActivity.getExamQuestions(examId);
            Workflow.getLogger(CutExamWorkflowImpl.class).info("获取到题目数量: {}", questions.size());
            if (questions.isEmpty()) {
                Workflow.getLogger(CutExamWorkflowImpl.class).warn("试卷中没有题目: examId={}", examId);
                cutExamActivity.updateExamState(examId, ExamStateEnum.AUTO_RECOGNIZED);
                cutExamActivity.callback(examParam, logPrefix);
                return;
            }
            log.info(logPrefix + "获取到题目 size {} {}", questions.size(), JSONUtil.toJsonStr( questions));
            
            // 更新试卷状态
            Workflow.getLogger(CutExamWorkflowImpl.class).info("切题完成，更新试卷状态: examId={}", examId);
            cutExamActivity.updateExamState(examId, ExamStateEnum.AUTO_RECOGNIZED);
            log.info(logPrefix + "更新试卷状态: examId={} {}", examId, ExamStateEnum.AUTO_RECOGNIZED);

            // 执行回调
            String callbackResult = cutExamActivity.callback(examParam, logPrefix);
            Workflow.getLogger(CutExamWorkflowImpl.class).info("回调执行结果: {}", callbackResult);
            log.info(logPrefix + "回调执行结果: {}", callbackResult);
        } catch (Exception e) {
            Workflow.getLogger(CutExamWorkflowImpl.class).error("切题工作流执行异常: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 批量处理题目
     *
     * @param activity 活动存根
     * @param questions 题目信息列表
     */
    private void processBatchQuestions(CutMathExamActivity activity, List<MathQuestion> questions) {
        int totalQuestions = questions.size();
        int processedQuestions = 0;
        
        // 按批次处理题目
        for (int i = 0; i < questions.size(); i += batchSize) {
            int end = Math.min(i + batchSize, questions.size());
            List<MathQuestion> batch = new ArrayList<>(questions.subList(i, end));
            
            // 记录进度
            processedQuestions += batch.size();
            int progressPercentage = (int) ((processedQuestions * 100.0) / totalQuestions);
            Workflow.getLogger(CutExamWorkflowImpl.class).info("处理进度: {}%, 当前批次: {}/{}", 
                    progressPercentage, processedQuestions, totalQuestions);
            
            // 执行批量切题
            String result = activity.cutQuestionsBatch(batch);
            Workflow.getLogger(CutExamWorkflowImpl.class).info("批量切题结果: {}", result);
            
            // 添加工作流心跳，防止长时间运行的工作流被终止
            Workflow.sleep(Duration.ofMillis(100));
        }
    }
}
