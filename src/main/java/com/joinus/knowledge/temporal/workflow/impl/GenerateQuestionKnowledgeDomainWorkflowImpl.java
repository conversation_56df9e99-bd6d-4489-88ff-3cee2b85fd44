package com.joinus.knowledge.temporal.workflow.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.MathLabelType;
import com.joinus.knowledge.enums.PromptEnum;
import com.joinus.knowledge.model.entity.MathLabel;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.QuestionLabel;
import com.joinus.knowledge.model.vo.QuestionWithLatestAnswerVO;
import com.joinus.knowledge.service.AIChatService;
import com.joinus.knowledge.service.MathLabelService;
import com.joinus.knowledge.service.MathQuestionsService;
import com.joinus.knowledge.service.QuestionLabelService;
import com.joinus.knowledge.temporal.activity.ChatCompletionsActivity;
import com.joinus.knowledge.temporal.workflow.GenerateQuestionKnowledgeDomainWorkflow;
import com.joinus.knowledge.utils.ConverterUtils;
import com.joinus.knowledge.utils.PromptUtils;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Workflow;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Component
@WorkflowImpl(taskQueues = GlobalConstants.GENERATE_QUESTION_KNOWLEDGE_DOMAIN_TASK_QUEUE)
public class GenerateQuestionKnowledgeDomainWorkflowImpl implements GenerateQuestionKnowledgeDomainWorkflow {

    @Resource
    private MathLabelService mathLabelService;

    @Resource
    private MathQuestionsService mathQuestionsService;

    @Resource
    private QuestionLabelService questionLabelService;

    @Resource
    private PromptUtils promptUtils;

    @Resource
    private AIChatService aiChatService;
    ActivityOptions options = ActivityOptions.newBuilder()
            .setStartToCloseTimeout(Duration.ofMinutes(30))
            .setRetryOptions(RetryOptions.newBuilder()
                    .setMaximumAttempts(1)
                    .build())
            .build();

    public GenerateQuestionKnowledgeDomainWorkflowImpl(MathLabelService mathLabelService, MathQuestionsService mathQuestionsService) {
        this.mathLabelService = mathLabelService;
        this.mathQuestionsService = mathQuestionsService;
    }

    @Override
    public void generate() {
        ChatCompletionsActivity chatCompletionsActivity = Workflow.newActivityStub(ChatCompletionsActivity.class, options);
        ChatOptions chatOptions = ChatOptions.builder()
                .temperature(0.1)
                .build();
        // 查询没有打过知识领域标签的数学题，每次查询100条，按照创建时间倒序
        List<QuestionWithLatestAnswerVO> questionList = mathQuestionsService.listNoKnowledgeDomainLabelQuestions(100);

        // 查询知识领域的标签
        List<MathLabel> labelList = mathLabelService.lambdaQuery()
                .eq(MathLabel::getType, MathLabelType.KNOWLEDGE_DOMAIN.toString())
                .list();
        List<String> labels = labelList.stream().map(MathLabel::getName).toList();

        Map<String, UUID> labelMap = labelList.stream().collect(Collectors.toMap(MathLabel::getName, MathLabel::getId));
        // 给这些题打知识领域的标签
        for (QuestionWithLatestAnswerVO question : questionList) {
            // 通过题目内容，调用AI接口，获取知识领域标签
            String questionContent = question.getQuestionContent();
            String promptTemplate = promptUtils.getPromptTemplate(PromptEnum.QUESTION_KNOWLEDGE_DOMAIN_LABEL);
            String promptText = StrUtil.format(promptTemplate, questionContent, question.getAnswer(), question.getAnswerContent());
            String chatResult = chatCompletionsActivity.chatWithOptions(promptText, chatOptions);
            if (StrUtil.isBlank(chatResult)) {
                log.error("模型输出为空");
                continue;
            }
            String labelName = ConverterUtils.convertKnowledgeDomain(chatResult);
            UUID labelId = labelMap.get(labelName);
            if (labelId == null) {
                log.error("知识领域标签不存在，labelName:{}", labelName);
                continue;
            }
            // 保存知识领域标签
            QuestionLabel questionLabel = QuestionLabel.builder()
                    .questionId(question.getQuestionId())
                    .labelId(labelId)
                    .labelType(MathLabelType.KNOWLEDGE_DOMAIN.toString())
                    .build();
            // 判断同类型标签不存在
            List<QuestionLabel> questionLabels = questionLabelService.lambdaQuery()
                    .eq(QuestionLabel::getQuestionId, question.getQuestionId())
                    .eq(QuestionLabel::getLabelType, MathLabelType.KNOWLEDGE_DOMAIN.toString())
                    .list();
            if (CollUtil.isEmpty(questionLabels)) {
                questionLabelService.save(questionLabel);
            }
        }
    }
}
