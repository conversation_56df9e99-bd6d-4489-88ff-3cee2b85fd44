package com.joinus.knowledge.temporal.workflow.impl;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.QuestionFile;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import com.joinus.knowledge.model.vo.KnowledgePointsAndDifficultyVO;
import com.joinus.knowledge.model.vo.MathExamQuestionVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import com.joinus.knowledge.temporal.activity.AnalyzeExamKnowledgePointsActivity;
import com.joinus.knowledge.temporal.activity.CutMathExamActivity;
import com.joinus.knowledge.temporal.activity.MathExamActivity;
import com.joinus.knowledge.temporal.workflow.AnalyzeExamWorkflow;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 考试分析工作流实现
 * 符合Temporal最佳实践：
 * 1. 不在工作流中使用Spring依赖注入
 * 2. 在方法内部定义ActivityOptions
 * 3. 添加完善的错误处理
 * 4. 添加适当的日志记录
 */
@Slf4j
@Component
@WorkflowImpl(taskQueues = GlobalConstants.ANALYZE_EXAM_TASK_QUEUE)
public class AnalyzeExamWorkflowImpl implements AnalyzeExamWorkflow {

    /**
     * 耿总提供的summary获取题目正确，青于蓝app用户选择错题后再次用aliyun ai分析试卷题目，如果一致则接着解题、分析知识点，如果不一致则走自动纠错逻辑
     * @param examParam
     */
    @Override
    public void submit(AnalyzeExamParam examParam) {
        // 在方法内部定义ActivityOptions，符合Temporal最佳实践
        ActivityOptions options = ActivityOptions.newBuilder()
                .setStartToCloseTimeout(Duration.ofMinutes(10))
                .setRetryOptions(RetryOptions.newBuilder()
                        .setMaximumAttempts(2)
                        .setInitialInterval(Duration.ofSeconds(1))
                        .setMaximumInterval(Duration.ofMinutes(1))
                        .setBackoffCoefficient(2.0)
                        .build())
                .build();

        // 创建活动存根
        MathExamActivity mathExamActivity = Workflow.newActivityStub(MathExamActivity.class, options);
        AnalyzeExamKnowledgePointsActivity analyzeActivity = Workflow.newActivityStub(AnalyzeExamKnowledgePointsActivity.class, options);

        UUID examId = examParam.getExamId();
        MathExam exam = null;
        String logPrefix = "AnalyzeExamWorkflow " + examId;
        try {
            // 记录工作流开始
            log.info("{} 开始考试分析工作流", logPrefix);

            // 获取考试信息
            exam = mathExamActivity.getExamById(examId);
            if (exam == null) {
                throw new IllegalArgumentException(logPrefix + "未找到ID为 " + examId + " 的考试");
            }
            if (null == examParam.getPublisher()) {
                throw new IllegalArgumentException(logPrefix + "请指定出版社");
            }

            // 获取数据库中的题目信息
            log.info("{} 获取数据库中的题目信息 {}", logPrefix);
            List<MathExamQuestionVO> dbQuestionVO = mathExamActivity.listDbQuestionsByExamId(examId);
            log.info("{} 获取数据库中的题目信息完成， {}", logPrefix, JSONUtil.toJsonStr(dbQuestionVO));

            // 调用阿里云图像处理，解析题目信息
            log.info("{} 开始调用阿里云图像处理服务", logPrefix);
            List<QuestionDetailVO> aliyunQuestionVO = analyzeActivity.summaryQuestionsByAliyun(exam);
            log.info("{} 阿里云图像处理完成 {}", logPrefix, JSONUtil.toJsonStr(aliyunQuestionVO));

            // 对比阿里云题目和数据库中的题目信息
            boolean consistent = compareQuestions(dbQuestionVO, aliyunQuestionVO, logPrefix);
            log.info("{} 题目比对结果: {}, ", logPrefix, consistent ? "一致" : "不一致");

            // 根据比较结果执行不同的处理逻辑
            if (consistent) {
                // 完全一致则按照阿里云题目内容更新数据库
                log.info("{} 题目一致", logPrefix);
                solveQuestionAndRelateKnowledgePoints(exam, examParam.getPublisher(), mathExamActivity, analyzeActivity);
                exam.setState(ExamStateEnum.DONE);
            } else {
                // 不一致则走人工解析流程
                log.info("{} 题目不一致，转为人工解析", logPrefix);
                exam.setState(ExamStateEnum.AUTO_RECOGNIZED);
            }

            // 更新考试状态
            mathExamActivity.updateById(exam);

            // 设置自动分析结果
            examParam.setAutoAnalyzeSuccess(consistent);

            // 回调通知
            log.info("{} 执行回调 {}", logPrefix, JSONUtil.toJsonStr(examParam));
            analyzeActivity.callbackForAutoAnalyze(examParam, logPrefix);

            log.info("{} 考试分析工作流完成", logPrefix);
        } catch (Exception e) {
            // 异常处理
            log.error("{} 考试分析工作流出错, 错误: {}", logPrefix, e.getMessage(), e);

            // 如果已经获取到考试信息，则更新状态为错误
            if (exam != null) {
                try {
                    log.info("{} 已将考试状态更新为ERROR", logPrefix);
                } catch (Exception updateEx) {
                    log.error("{} 更新考试状态失败，错误: {}", logPrefix, updateEx.getMessage(), updateEx);
                }
            }

            // 重新抛出异常，让Temporal处理
            throw Workflow.wrap(e);
        }
    }

    private void solveQuestionAndRelateKnowledgePoints(MathExam mathExam, PublisherType publisher, MathExamActivity mathExamActivity, AnalyzeExamKnowledgePointsActivity analyzeActivity) {
        // 根据试卷id查询题目列表 - 使用CutMathExamActivity的getExamQuestions方法
        List<MathQuestion> questions = mathExamActivity.getExamQuestions(mathExam.getId());
        Workflow.getLogger(this.getClass()).info("获取到题目数量: {}", questions.size());

        if (questions.isEmpty()) {
            Workflow.getLogger(this.getClass()).warn("试卷中没有题目: examId={}", mathExam.getId());
            mathExamActivity.updateExamState(mathExam.getId(), ExamStateEnum.DONE);
            return;
        }

        List<QuestionFile> questionFiles = mathExamActivity.listQuestionFile(questions.stream().map(MathQuestion::getId).toList());

        Map<UUID, List<UUID>> questionFileList = questionFiles.stream()
                .collect(Collectors.groupingBy(QuestionFile::getQuestionId, Collectors.mapping(QuestionFile::getFileId, Collectors.toList())));

        // 方案一：使用 Promise.allOf() 进行批量异步处理（推荐）
        int maxConcurrency = 8; // 最大并发数
        List<List<MathQuestion>> questionBatches = Lists.partition(questions, maxConcurrency);

        for (List<MathQuestion> batch : questionBatches) {
            // 为当前批次创建异步任务
            List<Promise<Map<String, Object>>> promises = batch.stream()
                    .map(question -> Async.function(() -> {
                        try {
                            return solveSingleQuestionAndRelateKnowledgePoints(question, mathExam, questionFileList, analyzeActivity, publisher);
                        } catch (Exception e) {
                            Workflow.getLogger(this.getClass()).error("处理题目失败: questionId={}, 错误={}",
                                    question.getId(), e.getMessage());
                            // 返回空结果，不中断整个流程
                            return null;
                        }
                    }))
                    .toList();

            // 等待当前批次的所有任务完成
            Promise.allOf(promises).get();

            Workflow.getLogger(this.getClass()).info("完成批次处理，批次大小: {}", batch.size());
        }
    }

    private Map<String, Object> solveSingleQuestionAndRelateKnowledgePoints(MathQuestion question, MathExam mathExam, Map<UUID, List<UUID>> questionFileList, AnalyzeExamKnowledgePointsActivity analyzeActivity, PublisherType publisher) {
        HashMap<String, Object> result = new HashMap<>();
        analyzeActivity.solveQuestion(question, mathExam, questionFileList);
        KnowledgePointsAndDifficultyVO knowledgePointsAndDifficultyVO = analyzeActivity.analyzeQuestionKnowledgePoints(question, mathExam.getGrade(), mathExam.getSemester(), publisher);
        result.put("question", question);
        result.put("knowledgePointsAndDifficultyVO", knowledgePointsAndDifficultyVO);
        return result;
    }

    private boolean compareQuestions(List<MathExamQuestionVO> dBquestionVO, List<QuestionDetailVO> mathExamQuestionVO, String logPrefix) {
        List<QuestionDetailVO> dbQuestionDetailVOs = new ArrayList<>();
        dBquestionVO.stream().forEach(questionVO -> {
                questionVO.getQuestions().stream().forEach(questionDetailVO -> {
                    questionDetailVO.getSortNo();
                    dbQuestionDetailVOs.add(questionDetailVO);
                });
        });

        //题目数量不一致
        if (dbQuestionDetailVOs.size() != mathExamQuestionVO.size()) {
            log.info("{} 题目数量不一致，数据库题目数量: {}, 阿里云题目数量: {}", logPrefix, dbQuestionDetailVOs.size(), mathExamQuestionVO.size());
            return false;
        }

        //存在任一道题的题型不一致
        for (int i = 0; i < dbQuestionDetailVOs.size(); i++) {
            QuestionDetailVO dbQuestionDetailVO = dbQuestionDetailVOs.get(i);
            QuestionDetailVO aliyunQuestionDetailVO = mathExamQuestionVO.get(i);
            if (!dbQuestionDetailVO.getQuestionType().equals(aliyunQuestionDetailVO.getQuestionType())) {
                log.info("{} 题目类型不一致，数据库题目类型: {}, 阿里云题目类型: {}", logPrefix, dbQuestionDetailVO.getQuestionType(), aliyunQuestionDetailVO.getQuestionType());
                return false;
            }
            dbQuestionDetailVO.setContent(aliyunQuestionDetailVO.getContent());
        }

        //题目数量一致，且按题目顺序的每道题的题型也一致
        return true;
    }
}
