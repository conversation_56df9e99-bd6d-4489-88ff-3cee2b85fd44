package com.joinus.knowledge.temporal.workflow.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.temporal.activity.ChatCompletionsActivity;
import com.joinus.knowledge.temporal.activity.FileActivity;
import com.joinus.knowledge.temporal.activity.SaveBookInfoActivity;
import com.joinus.knowledge.temporal.activity.TextbookActivity;
import com.joinus.knowledge.temporal.workflow.ExtractInfoFromImageByLLMWorkflow;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
@WorkflowImpl(taskQueues = GlobalConstants.EXTRACT_INFO_FROM_IMAGE_BY_LLM_TASK_QUEUE)
public class ExtractInfoFromImageByLLMImpl implements ExtractInfoFromImageByLLMWorkflow {

    ActivityOptions options = ActivityOptions.newBuilder()
            .setStartToCloseTimeout(Duration.ofMinutes(5))
            .setHeartbeatTimeout(Duration.ofMinutes(5))
            .setRetryOptions(RetryOptions.newBuilder()
                    .setMaximumAttempts(1)
                    .build())
            .build();

    @Override
    public String submit(String bookId) {
        FileActivity fileActivity = Workflow.newActivityStub(FileActivity.class, options);
        TextbookActivity textbookActivity = Workflow.newActivityStub(TextbookActivity.class, options);

        List<Map<String, Object>> files = fileActivity.getFilesByBookId(bookId);

        Map<Integer, List<String>> pageSectionMap = textbookActivity.getPageSectionMap(bookId);

        int concurrentSize = 5;

        List<List<Map<String, Object>>> filesList = Lists.partition(files,concurrentSize);

        for (List<Map<String, Object>> list : filesList) {
            List<Promise<String>> promiseList = list.stream().map(data -> Async.function(() -> extractInfo(data, pageSectionMap))).toList();
            promiseList.forEach(Promise::get);
        }

        return null;
    }

    protected String extractInfo(Map<String, Object> map, Map<Integer, List<String>> pageSectionMap) {
        ChatCompletionsActivity chatCompletionsActivity = Workflow.newActivityStub(ChatCompletionsActivity.class, options);
        SaveBookInfoActivity saveBookInfoActivity = Workflow.newActivityStub(SaveBookInfoActivity.class, options);
        String ocrHtml = MapUtil.getStr(map, "ocr_html", "");
        if (StrUtil.isBlank(ocrHtml)) {
            return "";
        }
        String promptText = """
                判断当前页面是否包含“知识点”+序号或“题型”+序号、“考点”+序号这样的文本在<h2>标签中,如果有，请将内容输出为以下json数组形式，假如图中有两个知识点，输出为[ {"name": "全等三角形的判定和性质","sortNo": 1, "type":"知识点"}, {"name": "次根式的乘除","sortNo": 2,"type":"考点"},{"name": "次根式在实际生活中的应用","sortNo": 1, "type":"题型","difficulty": 3}]，其中name表示该行的内容，如果内容包含数学公式或者符号，请使用LaTeX表示；sortNo表示文字后边的序号，difficulty表示题型的难度，有几颗星就是几，如果没有星就填null。如果没有找到相关元素，请输出空数组
                """;
        promptText = ocrHtml + "\n" + promptText;
        String assistantText = "";
        try {
            assistantText = chatCompletionsActivity.chat(promptText);
        } catch (Exception e) {
            log.error("chat error:{}", e.getMessage());
        }
        Pattern pattern = Pattern.compile("(?s)```(?:\\w+)?\\s*(.*?)\\s*```");
        Matcher matcher = pattern.matcher(assistantText);
        String codeContent;
        int pageIndex = (int)map.get("page_no");
        JSONArray array;
        if (matcher.find()) {
            codeContent = matcher.group(1);
            log.info(codeContent);
            try {
                array = JSONUtil.parseArray(codeContent);
            } catch (Exception e) {
                log.error("codeContent is not a json array");
                return "";
            }
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                saveBookInfoActivity.saveInfo(pageIndex, pageSectionMap, jsonObject);
            }
        } else {
            if (JSONUtil.isTypeJSON(assistantText)) {
                array = JSONUtil.parseArray(assistantText);
                for (int i = 0; i < array.size(); i++) {
                    JSONObject jsonObject = array.getJSONObject(i);
                    saveBookInfoActivity.saveInfo(pageIndex, pageSectionMap, jsonObject);
                }
            }
        }
        return "";
    }

}
