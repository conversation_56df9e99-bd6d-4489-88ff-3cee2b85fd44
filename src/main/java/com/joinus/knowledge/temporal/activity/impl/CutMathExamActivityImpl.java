package com.joinus.knowledge.temporal.activity.impl;

import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import com.joinus.knowledge.service.AIAbilityService;
import com.joinus.knowledge.service.MathExamQuestionsService;
import com.joinus.knowledge.service.MathExamsService;
import com.joinus.knowledge.service.MathQuestionsService;
import com.joinus.knowledge.temporal.activity.CutMathExamActivity;
import io.temporal.spring.boot.ActivityImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 切题工作流活动实现类
 * 遵循 Temporal 最佳实践，将外部服务调用封装在活动中
 */
@Slf4j
@Component
@ActivityImpl(taskQueues = {
        GlobalConstants.CUT_EXAM_TASK_QUEUE
})
public class CutMathExamActivityImpl implements CutMathExamActivity {

    @Resource
    private AIAbilityService aiAbilityService;
    @Resource
    private MathExamsService mathExamsService;
    @Resource
    private MathExamQuestionsService mathExamQuestionsService;
    @Resource
    private MathQuestionsService mathQuestionsService;
    @Resource
    private WebClient webClient;
    
    @Value("${cut.result.callback.uri:/api/smart-study/callback/ai-cut-questions-result}")
    private String callbackUri;
    @Value("${callback.timeout.seconds:10}")
    private int callbackTimeoutSeconds;

    @Override
    public List<MathQuestion> getExamQuestions(UUID examId) {
        log.info("获取试卷题目: examId={}", examId);
        try {
            List<MathQuestion> questions = mathExamQuestionsService.listQuestionsByExamId(examId);
            log.info("获取到题目数量: {}, examId={}", questions.size(), examId);
            return questions;
        } catch (Exception e) {
            log.error("获取试卷题目失败: examId={}, 错误={}", examId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void updateExamState(UUID examId, ExamStateEnum state) {
        log.info("更新试卷状态: examId={}, state={}", examId, state);
        try {
            MathExam exam = mathExamsService.getById(examId);
            if (exam == null) {
                log.error("试卷不存在: examId={}", examId);
                throw new IllegalArgumentException("试卷不存在: " + examId);
            }
            exam.setState(state);
            mathExamsService.updateById(exam);
            log.info("更新试卷状态成功: examId={}, state={}", examId, state);
        } catch (Exception e) {
            log.error("更新试卷状态失败: examId={}, state={}, 错误={}", examId, state, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String cutQuestionsBatch(List<MathQuestion> questions) {
        int total = questions.size();
        log.info("开始批量切题: 题目数量={}", total);
        
        int successCount = 0;
        int failCount = 0;
        List<String> failedIds = new ArrayList<>();
        
        for (MathQuestion question : questions) {
            try {
                // 直接实现切题逻辑，不再调用cutQuestion活动方法
                log.info("批量处理中的单个题目: 题目ID={}", question.getId());
                
                // 从数据库中获取完整的题目
                // 调用切题服务
                aiAbilityService.cutQuestionsFromExam(question);
                log.info("批量中的单个题目切题成功: 题目ID={}", question.getId());
                
                successCount++;
            } catch (Exception e) {
                failCount++;
                failedIds.add(question.getId().toString());
                log.error("批量中的单个题目切题失败: 题目ID={}, 错误={}", question.getId(), e.getMessage());
                // 单个题目失败不影响整体流程，继续处理下一个
            }
        }
        
        String result = String.format("批量切题完成: 总数=%d, 成功=%d, 失败=%d", total, successCount, failCount);
        log.info(result);
        
        // 如果全部失败，则抛出异常
        if (failCount == total) {
            throw new RuntimeException("批量切题全部失败: " + String.join(", ", failedIds));
        }
        
        return result;
    }


    @Override
    public String callback(AnalyzeExamParam examParam, String logPrefix) {
        log.info(logPrefix + "开始执行回调 params {} url {}", JSONUtil.toJsonStr(examParam), callbackUri);
        try {
            String responseBody = webClient.post()
                    .uri(callbackUri)
                    .bodyValue(examParam)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(callbackTimeoutSeconds))
                    .onErrorResume(e -> {
                        log.error("回调失败: examId={}, 错误={}", examParam.getExamId(), e.getMessage(), e);
                        return Mono.just("回调失败: " + e.getMessage());
                    })
                    .block();
            log.info(logPrefix + "回调成功: examId={}, responseBody={}", examParam.getExamId(), responseBody);
            return responseBody;
        } catch (Exception e) {
            log.error("回调执行异常: examId={}, 错误={}", examParam.getExamId(), e.getMessage(), e);
            return "回调执行异常: " + e.getMessage();
        }
    }

}
