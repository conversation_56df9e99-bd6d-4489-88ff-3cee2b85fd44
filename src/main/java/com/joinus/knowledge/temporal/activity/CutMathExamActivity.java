package com.joinus.knowledge.temporal.activity;

import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.List;
import java.util.UUID;

/**
 * 切题工作流活动接口
 * 遵循 Temporal 最佳实践，将外部服务调用封装在活动中
 */
@ActivityInterface
public interface CutMathExamActivity {

    /**
     * 获取试卷中的题目列表
     * 
     * @param examId 试卷ID
     * @return 题目信息列表
     */
    @ActivityMethod
    List<MathQuestion> getExamQuestions(UUID examId);
    
    /**
     * 更新试卷状态
     * 
     * @param examId 试卷ID
     * @param state 新状态
     */
    @ActivityMethod
    void updateExamState(UUID examId, ExamStateEnum state);
    
    /**
     * 批量处理题目切割
     * 
     * @param questions 题目信息列表
     * @return 处理结果摘要（成功数量/失败数量）
     */
    @ActivityMethod
    String cutQuestionsBatch(List<MathQuestion> questions);

    /**
     * 工作流完成后的回调
     *
     * @param examParam 试卷参数
     * @param logPrefix
     * @return 回调结果
     */
    @ActivityMethod
    String callback(AnalyzeExamParam examParam, String logPrefix);
}
