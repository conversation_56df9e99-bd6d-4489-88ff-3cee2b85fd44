package com.joinus.knowledge.temporal.activity.impl;

import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.service.impl.FilesServiceImpl;
import com.joinus.knowledge.temporal.activity.FileActivity;
import com.joinus.knowledge.utils.MinioUtils;
import io.temporal.spring.boot.ActivityImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
@ActivityImpl(taskQueues = {GlobalConstants.EXTRACT_INFO_FROM_IMAGE_BY_LLM_TASK_QUEUE, GlobalConstants.IMAGE_OCR_TASK_QUEUE})
public class FileActivityImpl implements FileActivity {

    @Resource
    private FilesServiceImpl filesService;


    @Resource
    private MinioUtils minioUtils;

    @Override
    public List<Map<String, Object>> getFilesByBookId(String bookId) {
        return filesService.getFilesByBookId(bookId);
    }

    @Override
    public String getPresignedObjectUrl(String objectName) {
        return minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, objectName);
    }

}
