package com.joinus.knowledge.temporal.activity.impl;

import cn.hutool.core.collection.CollUtil;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.ExamSourceType;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.QuestionType;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.QuestionFile;
import com.joinus.knowledge.model.vo.MathExamQuestionVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import com.joinus.knowledge.service.MathExamQuestionsService;
import com.joinus.knowledge.service.MathExamsService;
import com.joinus.knowledge.service.MathQuestionsService;
import com.joinus.knowledge.service.QuestionFileService;
import com.joinus.knowledge.temporal.activity.MathExamActivity;
import io.temporal.spring.boot.ActivityImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@ActivityImpl(taskQueues = {GlobalConstants.ANALYZE_EXAM_KNOWLEDGE_POINTS_TASK_QUEUE,
        GlobalConstants.ANALYZE_QUESTION_KNOWLEDGE_POINTS_TASK_QUEUE,
        GlobalConstants.ANALYZE_EXAM_TASK_QUEUE,
        GlobalConstants.MATH_EXAM_REANALYZE_KNOWLWDGE_POINT_TASK_QUEUE})
public class MathExamActivityImpl implements MathExamActivity {

    @Resource
    private MathExamsService mathExamsService;
    @Resource
    private WebClient webClient;
    @Value("${analyze.result.callback.uri:/openai/callback}")
    private String callbackUri;
    @Autowired
    private MathQuestionsService mathQuestionsService;
    @Autowired
    private MathExamQuestionsService mathExamQuestionsService;
    @Autowired
    private QuestionFileService questionFileService;



    @Override
    public MathExam getExamById(UUID examId) {
        return mathExamsService.getById(examId);
    }

    @Override
    public List<MathExamQuestionVO> listDbQuestionsByExamId(UUID examId) {
        List<QuestionDetailVO> mathQuestions = mathQuestionsService.listExamQuestionDetailByExamId(examId);
        if (CollUtil.isEmpty(mathQuestions)) {
            return List.of();
        }

        // 按题目类型分组
        Map<QuestionType, List<QuestionDetailVO>> questionTypeMap = mathQuestions.stream()
                .collect(Collectors.groupingBy(QuestionDetailVO::getQuestionType));

        // 定义题型顺序
        List<QuestionType> questionTypeOrder = Arrays.asList(
                QuestionType.MULTIPLE_CHOICE,    // 选择题
                QuestionType.FILL_IN_THE_BLANK,  // 填空题
                QuestionType.CALCULATION,        // 计算题
                QuestionType.PROBLEM_SOLVING,    // 解答题
                QuestionType.PROOF,              // 证明题
                QuestionType.APPLICATION,        // 应用题
                QuestionType.TRUE_FALSE,         // 判断题
                QuestionType.OTHER,              // 其他
                QuestionType.UNKNOWN             // 未知类型
        );

        List<MathExamQuestionVO> resultVO = new ArrayList<>();

        // 遍历题型顺序，确保结果按预定义顺序排列
        for (QuestionType type : questionTypeOrder) {
            // 如果当前题型没有题目，跳过
            if (!questionTypeMap.containsKey(type)) {
                continue;
            }

            List<QuestionDetailVO> questionsOfType = questionTypeMap.get(type);
            if (CollUtil.isEmpty(questionsOfType)) {
                continue;
            }

            // 对每种类型的题目按照sortNo排序（如果有）
            questionsOfType.sort(Comparator.comparing(q -> q.getSortNo() != null ? q.getSortNo() : Integer.MAX_VALUE));

            // 创建并添加分类VO
            MathExamQuestionVO examQuestionVO = MathExamQuestionVO.builder()
                    .type(type)
                    .sortNo(questionTypeOrder.indexOf(type) + 1)
                    .questions(questionsOfType)
                    .build();

            resultVO.add(examQuestionVO);
        }

        return resultVO;
    }




    @Override
    public void updateById(MathExam exam) {
        mathExamsService.updateById(exam);
    }


    @Override
    public List<MathQuestion> getExamQuestions(UUID examId) {
        log.info("获取试卷题目: examId={}", examId);
        try {
            List<MathQuestion> questions = mathExamQuestionsService.listQuestionsByExamId(examId);
            log.info("获取到题目数量: {}, examId={}", questions.size(), examId);
            return questions;
        } catch (Exception e) {
            log.error("获取试卷题目失败: examId={}, 错误={}", examId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<MathQuestion> getExamQuestionDetails(UUID examId) {
        log.info("获取试卷题目: examId={}", examId);
        try {
            List<MathQuestion> questions = mathExamQuestionsService.listQuestionsByExamId(examId);
            questions.forEach(question -> question.setContent(mathQuestionsService.decodeContentV2(question.getContent())));
            log.info("获取到题目数量: {}, examId={}", questions.size(), examId);
            return questions;
        } catch (Exception e) {
            log.error("获取试卷题目失败: examId={}, 错误={}", examId, e.getMessage(), e);
            throw e;
        }
    }


    @Override
    public List<QuestionFile> listQuestionFile(List<UUID> list) {
        List<QuestionFile> questionFiles = questionFileService.lambdaQuery()
                .in(QuestionFile::getQuestionId, list)
                .list();
        return questionFiles;
    }

    @Override
    public void updateExamState(UUID examId, ExamStateEnum state) {
        log.info("更新试卷状态: examId={}, state={}", examId, state);
        try {
            MathExam exam = mathExamsService.getById(examId);
            if (exam == null) {
                log.error("试卷不存在: examId={}", examId);
                throw new IllegalArgumentException("试卷不存在: " + examId);
            }
            exam.setState(state);
            mathExamsService.updateById(exam);
            log.info("更新试卷状态成功: examId={}, state={}", examId, state);
        } catch (Exception e) {
            log.error("更新试卷状态失败: examId={}, state={}, 错误={}", examId, state, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<MathQuestion> getNoneKnowledgePointsExamQuestions() {
        return mathQuestionsService.listNoneKnowledgePointsExamQuestions();
    }

    @Override
    public List<MathExam> listExamByStateAndSources(ExamStateEnum state, List<ExamSourceType> sourceTypes) {
        return mathExamsService.lambdaQuery()
                .eq(MathExam::getState, state)
                .in(MathExam::getSource, sourceTypes)
                .list();
    }
}
