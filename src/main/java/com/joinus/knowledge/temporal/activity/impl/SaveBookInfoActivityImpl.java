package com.joinus.knowledge.temporal.activity.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.entity.MathQuestionType;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.temporal.activity.SaveBookInfoActivity;
import io.temporal.spring.boot.ActivityImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
@ActivityImpl(taskQueues = {GlobalConstants.EXTRACT_INFO_FROM_IMAGE_BY_LLM_TASK_QUEUE,GlobalConstants.IMAGE_OCR_TASK_QUEUE})
public class SaveBookInfoActivityImpl implements SaveBookInfoActivity {


    @Resource
    private MathKnowledgePointsService mathKnowledgePointsService;

    @Resource
    private SectionKnowledgePointsService sectionKnowledgePointsService;

    @Resource
    private MathQuestionTypesService mathQuestionTypesService;

    @Resource
    private SectionQuestionTypesService sectionQuestionTypesService;

    @Resource
    private FilesService filesService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInfo(int pageIndex, Map<Integer, List<String>> pageSectionMap, JSONObject jsonObject) {
        switch (jsonObject.getStr("type")) {
            case "知识点":
                MathKnowledgePoint knowledgePoints = JSONUtil.toBean(jsonObject, MathKnowledgePoint.class);
                saveKnowledgePointsAndRelation(pageIndex, knowledgePoints, pageSectionMap);
                break;
            case "题型":
                MathQuestionType questionTypes = JSONUtil.toBean(jsonObject, MathQuestionType.class);
                saveQuestionTypeAndRelation(pageIndex, questionTypes, pageSectionMap);
                break;
            case "考点":
                MathKnowledgePoint examPoints = JSONUtil.toBean(jsonObject, MathKnowledgePoint.class);
                saveExamPointsAndRelation(pageIndex, examPoints, pageSectionMap);
                break;
            default:
                break;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOcrHtml(String fileId, String html) {
        com.joinus.knowledge.model.entity.File file = new File();
        file.setId(UUID.fromString(fileId));
        file.setOcrHtml(html);
        filesService.updateById(file);
    }

    private void saveKnowledgePointsAndRelation(int pageIndex, MathKnowledgePoint entity, Map<Integer, List<String>> pageSectionMap) {
        List<String> sectionIds = pageSectionMap.get(pageIndex);
        if (sectionIds != null) {

            List<MathKnowledgePoint> list = mathKnowledgePointsService.lambdaQuery().eq(MathKnowledgePoint::getName, entity.getName()).list();
            if (list.isEmpty()) {
                entity.setId(UUID.randomUUID());
                entity.setCreatedAt(new Date());
                entity.setUpdatedAt(new Date());
                mathKnowledgePointsService.save(entity);
            } else {
                entity.setId(list.get(0).getId());
            }

            for (String sectionId : sectionIds) {
                SectionKnowledgePoint relation = new SectionKnowledgePoint();
                relation.setKnowledgePointId(entity.getId());
                relation.setSectionId(UUID.fromString(sectionId));
                relation.setPageIndex(pageIndex);
                if (sectionKnowledgePointsService.lambdaQuery().eq(SectionKnowledgePoint::getSectionId, UUID.fromString(sectionId)).eq(SectionKnowledgePoint::getKnowledgePointId, entity.getId()).list().isEmpty()) {
                    sectionKnowledgePointsService.save(relation);
                }
            }
        }
    }

    private void saveExamPointsAndRelation(int pageIndex, MathKnowledgePoint entity, Map<Integer, List<String>> pageSectionMap) {

        List<String> sectionIds = pageSectionMap.get(pageIndex);
        if (sectionIds != null) {
            List<MathKnowledgePoint> list = mathKnowledgePointsService.lambdaQuery().eq(MathKnowledgePoint::getName, entity.getName()).list();
            if (list.isEmpty()) {
                entity.setId(UUID.randomUUID());
                entity.setCreatedAt(new Date());
                entity.setUpdatedAt(new Date());
                entity.setExamPoint(true);
                mathKnowledgePointsService.save(entity);
            } else {
                entity.setId(list.get(0).getId());
            }

            for (String sectionId : sectionIds) {
                SectionKnowledgePoint relation = new SectionKnowledgePoint();
                relation.setKnowledgePointId(entity.getId());
                relation.setSectionId(UUID.fromString(sectionId));
                relation.setPageIndex(pageIndex);
                if (sectionKnowledgePointsService.lambdaQuery().eq(SectionKnowledgePoint::getSectionId, UUID.fromString(sectionId)).eq(SectionKnowledgePoint::getKnowledgePointId, entity.getId()).list().isEmpty()) {
                    sectionKnowledgePointsService.save(relation);
                }
            }
        }
    }

    private void saveQuestionTypeAndRelation(int pageIndex, MathQuestionType entity, Map<Integer, List<String>> pageSectionMap) {
        List<String> sectionIds = pageSectionMap.get(pageIndex);
        if (sectionIds != null) {
            List<MathQuestionType> list = mathQuestionTypesService.lambdaQuery().eq(MathQuestionType::getName, entity.getName()).list();
            if (list.isEmpty()) {
                entity.setId(UUID.randomUUID());
                entity.setCreatedAt(new Date());
                entity.setUpdatedAt(new Date());
                mathQuestionTypesService.save(entity);
            } else {
                entity.setId(list.get(0).getId());
            }

            for (String sectionId : sectionIds) {
                SectionQuestionType relation = new SectionQuestionType();
                relation.setQuestionTypeId(entity.getId());
                relation.setSectionId(UUID.fromString(sectionId));
                relation.setPageIndex(pageIndex);
                if (sectionQuestionTypesService.lambdaQuery().eq(SectionQuestionType::getSectionId, UUID.fromString(sectionId)).eq(SectionQuestionType::getQuestionTypeId, entity.getId()).list().isEmpty()) {
                    sectionQuestionTypesService.save(relation);
                }
            }
        }
    }
}
