package com.joinus.knowledge.temporal.activity;

import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.QuestionFile;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import com.joinus.knowledge.model.vo.KnowledgePointsAndDifficultyVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@ActivityInterface
public interface AnalyzeExamKnowledgePointsActivity {

    @ActivityMethod
    void solveQuestion(MathQuestion question, MathExam exam, Map<UUID, List<UUID>> questionFileList);

    void solveQuestionViaContentParts(MathQuestion question);

    @ActivityMethod
    String callbackForManualAnalyze(AnalyzeExamParam param, String logPrefix);


    @ActivityMethod
    String callbackForAutoAnalyze(AnalyzeExamParam examParam, String logPrefix);

    @ActivityMethod
    KnowledgePointsAndDifficultyVO analyzeQuestionKnowledgePoints(MathQuestion question, Integer grade, Integer semester, PublisherType publisherType);

    @ActivityMethod
    List<QuestionDetailVO> summaryQuestionsByAliyun(MathExam exam);
}
