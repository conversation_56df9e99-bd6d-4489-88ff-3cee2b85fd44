package com.joinus.knowledge.temporal.activity.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.*;
import com.joinus.knowledge.model.dto.CutExamQuestionDTO;
import com.joinus.knowledge.model.dto.CutQuestionSubjectDTO;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import com.joinus.knowledge.model.param.SolveQuestionParam;
import com.joinus.knowledge.model.po.MathKnowledgePointPO;
import com.joinus.knowledge.model.vo.CutQuestionVO;
import com.joinus.knowledge.model.vo.FileVO;
import com.joinus.knowledge.model.vo.KnowledgePointsAndDifficultyVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.temporal.activity.AnalyzeExamKnowledgePointsActivity;
import io.temporal.activity.Activity;
import io.temporal.spring.boot.ActivityImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
@ActivityImpl(taskQueues = {GlobalConstants.ANALYZE_EXAM_KNOWLEDGE_POINTS_TASK_QUEUE,
        GlobalConstants.ANALYZE_QUESTION_KNOWLEDGE_POINTS_TASK_QUEUE,
        GlobalConstants.ANALYZE_EXAM_TASK_QUEUE,
        GlobalConstants.MATH_EXAM_REANALYZE_KNOWLWDGE_POINT_TASK_QUEUE})
public class AnalyzeExamKnowledgePointsActivityImpl implements AnalyzeExamKnowledgePointsActivity {

    @Resource
    private AIAbilityService aiAbilityService;
    @Resource
    private FilesService filesService;
    @Resource
    private MathQuestionsService mathQuestionsService;
    @Resource
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Resource
    private MathExamsService mathExamsService;
    @Resource
    private ImageRecognizeService imageRecognizeService;
    @Resource
    private WebClient webClient;
    @Value("${analyze.result.callback.uri:/openai/callback}")
    private String callbackAutoUri;
    @Value("${analyze.knowledge-points.result.callback.uri:/api/smart-study/callback/error-correction-analytics-result}")
    private String callbackManualUri;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void solveQuestion(MathQuestion question, MathExam exam, Map<UUID, List<UUID>> questionFileList) {
        Boolean solved = mathQuestionsService.checkSolved(question);
        if (solved) {
            return;
        }
        SolveQuestionParam solveQuestionParam = new SolveQuestionParam();
        solveQuestionParam.setQuestionId(question.getId());
        List<UUID> fileIds = questionFileList.get(question.getId());
        if (CollUtil.isNotEmpty(fileIds)) {
            List<File> files = filesService.lambdaQuery()
                    .in(File::getId, fileIds)
                    .list();
            List<String> objectNames = files.stream()
                    .map(File::getOssUrl)
                    .toList();
            solveQuestionParam.setObjectNames(objectNames);
            solveQuestionParam.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
        }

        if (StrUtil.isBlank(question.getContent())) {
            aiAbilityService.ocrQuestionImageAndSaveContent(solveQuestionParam);
        }
        aiAbilityService.solveMathQuestion(solveQuestionParam);
    }

    @Override
    public void solveQuestionViaContentParts(MathQuestion question) {
        Boolean solved = mathQuestionsService.checkSolved(question);
        if (solved) {
            return;
        }
        SolveQuestionParam solveQuestionParam = new SolveQuestionParam();
        solveQuestionParam.setQuestionId(question.getId());
        solveQuestionParam.setQuestionText(question.getContent());
        aiAbilityService.solveMathQuestionViaContentParts(solveQuestionParam);
    }

    @Override
    public String callbackForManualAnalyze(AnalyzeExamParam examParam, String logPrefix) {
        log.info("{} callback url {} requestParams {}", logPrefix, callbackManualUri, examParam);
        String responseBody = webClient.post()
                .uri(callbackManualUri)
                .bodyValue(examParam)
                .retrieve()
                .bodyToMono(String.class)
                .block();
        log.info("{} callback url {} responseBody {}", logPrefix, callbackManualUri, responseBody);
        return responseBody;
    }


    @Override
    public KnowledgePointsAndDifficultyVO analyzeQuestionKnowledgePoints(MathQuestion question, Integer grade, Integer semester, PublisherType publisher) {
        Boolean related = mathQuestionsService.checkRelatedKnowledgePoints(question, publisher);
        if (related) {
            List<MathKnowledgePointPO> mathKnowledgePointPOS = mathKnowledgePointsService.listByQuestionIdsAndPublisher(CollUtil.toList(question.getId()), publisher);
            return KnowledgePointsAndDifficultyVO.builder()
                    .difficulty(String.valueOf(question.getDifficulty()))
                    .knowledgePoints(mathKnowledgePointPOS.stream().map(MathKnowledgePointPO::convertToVO).toList())
                    .build();
        }
        int attempt = Activity.getExecutionContext().getInfo().getAttempt();
        KnowledgePointsAndDifficultyVO vo = aiAbilityService.getProblemKnowledgePointsViaAI(question, grade, semester, publisher, attempt);
        if (CollUtil.isEmpty(vo.getKnowledgePoints())) {
            throw new RuntimeException("没有找到相关知识点");
        }
        return vo;
    }

    @Override
    public List<QuestionDetailVO> summaryQuestionsByAliyun(MathExam exam) {

        List<FileVO> examFileVOs = mathExamsService.listExamImagesByExamId(exam.getId());

        if (CollUtil.isEmpty(examFileVOs)) {
            return List.of();
        }

        List<QuestionDetailVO> vos = new ArrayList<>();
        for (FileVO fileVO : examFileVOs) {
            CutQuestionVO cutQuestionVO = imageRecognizeService.paperStructedFromAli(fileVO.getOssUrl(), PaperSubjectType.JUNIOR_HIGH_SCHOOL_MATH.getType());
            if (null == cutQuestionVO) {
                continue;
            }
            for (CutQuestionSubjectDTO subject : cutQuestionVO.getSubjects()) {

                CutExamQuestionDTO cutExamQuestionDTO = new CutExamQuestionDTO();
                cutExamQuestionDTO.setObjectName(fileVO.getOssKey());
                cutExamQuestionDTO.setOssEnum(fileVO.getOssEnum());
                cutExamQuestionDTO.setPositionList(subject.getPos_list());

                QuestionDetailVO questionDetailVO = QuestionDetailVO.builder()
                        .questionType(AliQuestionType.fromCode(subject.getType()).getQuestionType())
                        .source(QuestionSourceType.TODO_USER_EXAM)
                        .content(JSONUtil.toJsonStr(cutExamQuestionDTO))
                        .build();
                vos.add(questionDetailVO);
            }
        }
        return vos;
    }

    @Override
    public String callbackForAutoAnalyze(AnalyzeExamParam examParam, String logPrefix) {
        log.info("{} callback url {} requestParams {}", logPrefix, callbackAutoUri, examParam);
        String responseBody = webClient.post()
                .uri(callbackAutoUri)
                .bodyValue(examParam)
                .retrieve()
                .bodyToMono(String.class)
                .block();
        log.info("{} callback url {} responseBody {}", logPrefix, callbackAutoUri, responseBody);
        return responseBody;
    }
}
