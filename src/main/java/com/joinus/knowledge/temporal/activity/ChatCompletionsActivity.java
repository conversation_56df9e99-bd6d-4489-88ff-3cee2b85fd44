package com.joinus.knowledge.temporal.activity;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;
import org.springframework.ai.chat.prompt.ChatOptions;

@ActivityInterface
public interface ChatCompletionsActivity {

    @ActivityMethod
    String chatWithImage(String prompt, String imageUrl);

    @ActivityMethod
    String chat(String prompt);

    @ActivityMethod
    String chatWithOptions(String prompt, ChatOptions chatOptions);
}
