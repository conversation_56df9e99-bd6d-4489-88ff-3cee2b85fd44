package com.joinus.knowledge.temporal.activity;

import cn.hutool.json.JSONObject;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.List;
import java.util.Map;

@ActivityInterface
public interface SaveBookInfoActivity {
    @ActivityMethod
    void saveInfo(int pageIndex, Map<Integer, List<String>> pageSectionMap, JSONObject jsonObject);

    @ActivityMethod
    void saveOcrHtml(String fileId, String html);
}
