package com.joinus.knowledge.temporal.activity.impl;

import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.mapper.MathSectionMapper;
import com.joinus.knowledge.model.entity.MathSection;
import com.joinus.knowledge.service.TextbooksService;
import com.joinus.knowledge.temporal.activity.TextbookActivity;
import io.temporal.spring.boot.ActivityImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
@ActivityImpl(taskQueues = {GlobalConstants.EXTRACT_INFO_FROM_IMAGE_BY_LLM_TASK_QUEUE})
public class TextbookActivityImpl implements TextbookActivity {

    @Resource
    private TextbooksService textbooksService;

    @Resource
    private MathSectionMapper mathSectionMapper;

    @Override
    public Map<Integer, List<String>> getPageSectionMap(String bookId) {
        Map<Integer, List<String>> pageSectionMap = new HashMap<>();
        int offset = textbooksService.getById(UUID.fromString(bookId)).getPageOffset();
        List<MathSection> sections = mathSectionMapper.getByBookId(UUID.fromString(bookId));
        for (MathSection section : sections) {
            for (int i = section.getStartPage(); i <= section.getEndPage(); i++) {
                if (pageSectionMap.containsKey(i + offset)) {
                    log.info(i+offset + " " + section.getId());
                    pageSectionMap.get(i + offset).add(section.getId().toString());
                } else {
                    List<String> list = new ArrayList<>();
                    list.add(section.getId().toString());
                    pageSectionMap.put(i + offset, list);
                }
            }
        }
        return pageSectionMap;
    }
}
