package com.joinus.knowledge.temporal.activity;

import com.joinus.knowledge.enums.ExamSourceType;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.QuestionFile;
import com.joinus.knowledge.model.vo.MathExamQuestionVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.List;
import java.util.UUID;

@ActivityInterface
public interface MathExamActivity {


    @ActivityMethod
    MathExam getExamById(UUID examId);

    @ActivityMethod
    List<MathExamQuestionVO> listDbQuestionsByExamId(UUID examId);



    @ActivityMethod
    void updateById(MathExam exam);

    /**
     * 获取试卷中的题目列表
     *
     * @param examId 试卷ID
     * @return 题目信息列表
     */
    List<MathQuestion> getExamQuestions(UUID examId);

    List<MathQuestion> getExamQuestionDetails(UUID examId);

    List<QuestionFile> listQuestionFile(List<UUID> list);

    /**
     * 更新试卷状态
     *
     * @param examId 试卷ID
     * @param state 新状态
     */
    @ActivityMethod
    void updateExamState(UUID examId, ExamStateEnum state);


    @ActivityMethod
    List<MathQuestion> getNoneKnowledgePointsExamQuestions();

    @ActivityMethod
    List<MathExam> listExamByStateAndSources(ExamStateEnum state, List<ExamSourceType> sourceTypes);
}
