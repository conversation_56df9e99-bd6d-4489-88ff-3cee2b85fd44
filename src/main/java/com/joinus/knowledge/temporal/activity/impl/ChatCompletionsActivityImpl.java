package com.joinus.knowledge.temporal.activity.impl;


import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.AIModelType;
import com.joinus.knowledge.service.AIChatService;
import com.joinus.knowledge.temporal.activity.ChatCompletionsActivity;
import io.temporal.spring.boot.ActivityImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ActivityImpl(taskQueues = {GlobalConstants.EXTRACT_INFO_FROM_IMAGE_BY_LLM_TASK_QUEUE, GlobalConstants.IMAGE_OCR_TASK_QUEUE, GlobalConstants.GENERATE_QUESTION_KNOWLEDGE_DOMAIN_TASK_QUEUE})
public class ChatCompletionsActivityImpl implements ChatCompletionsActivity {

    @Resource
    private AIChatService aiChatService;

    @Override
    public String chatWithImage(String prompt, String imageUrl) {
        return aiChatService.chat(prompt, imageUrl, AIModelType.JYSD_QWEN_VL);
    }

    @Override
    public String chat(String prompt) {
        return aiChatService.chat(prompt, AIModelType.JYSD_DEEPSEEK_R1);
    }

    @Override
    public String chatWithOptions(String prompt, ChatOptions chatOptions) {
        return aiChatService.chat(prompt, AIModelType.JYSD_DEEPSEEK_R1, chatOptions);
    }
}
