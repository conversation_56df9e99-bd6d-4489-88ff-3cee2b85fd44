package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.entity.MathAnswer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.vo.MathAnswerVO;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_answers】的数据库操作Mapper
* @createDate 2025-02-28 14:12:06
* @Entity com.joinus.knowledge.entity.MathQuestionAnswers
*/
public interface MathAnswersMapper extends BaseMapper<MathAnswer> {

    List<MathAnswerVO> listAnswersByQuestionId(UUID id);
    
    /**
     * 自定义逻辑删除方法，解决UUID和时间戳字段的兼容性问题
     * 
     * @param id 要删除的记录ID
     * @return 影响的行数
     */
    int logicalDeleteById(UUID id);
}




