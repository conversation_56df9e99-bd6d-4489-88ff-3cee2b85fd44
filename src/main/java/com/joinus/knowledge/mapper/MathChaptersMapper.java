package com.joinus.knowledge.mapper;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathChapter;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.vo.MathChapterVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【math_chapters】的数据库操作Mapper
* @createDate 2025-02-28 14:12:06
* @Entity com.joinus.knowledge.entity.MathChapters
*/
@Mapper
public interface MathChaptersMapper extends BaseMapper<MathChapter> {

    List<MathChapterVO> list(@Param("name") String name,
                             @Param("grade") Integer grade,
                             @Param("semester") Integer semester,
                             @Param("publisher") PublisherType publisher);

}




