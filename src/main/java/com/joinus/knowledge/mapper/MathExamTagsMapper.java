package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathExamTag;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【math_exam_tags(试卷标签表)】的数据库操作Mapper
* @createDate 2025-07-21 09:19:09
* @Entity generator.domain.MathExamTags
*/
public interface MathExamTagsMapper extends BaseMapper<MathExamTag> {

    MathExam checkExistByAlias(String examName);
}




