package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.entity.MathCatalogNode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_catalog_nodes(数学目录节点表)】的数据库操作Mapper
* @createDate 2025-07-30 17:33:40
* @Entity com.joinus.knowledge.model.entity.MathCatalogNodes
*/
@Mapper
public interface MathCatalogNodesMapper extends BaseMapper<MathCatalogNode> {

    List<MathCatalogNode> listCatalogNodes(UUID nodeId);

}




