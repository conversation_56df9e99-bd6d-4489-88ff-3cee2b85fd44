package com.joinus.knowledge.mapper;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.dto.QuestionKnowledgePointDTO;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.entity.QuestionKnowledgePoint;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_questions】的数据库操作Mapper
* @createDate 2025-03-01 10:22:43
* @Entity com.joinus.knowledge.entity.QuestionKnowledgePoints
*/
public interface QuestionKnowledgePointsMapper extends BaseMapper<QuestionKnowledgePoint> {
    
    /**
     * 插入题目和知识点关联
     * 
     * @param questionId 题目ID
     * @param knowledgePointId 知识点ID
     * @return 影响行数
     */
    int insertQuestionKnowledgePoints(@Param("questionId") UUID questionId, @Param("knowledgePointId") UUID knowledgePointId);
    
    /**
     * 删除题目和知识点关联
     * 
     * @param questionId 题目ID
     * @param knowledgePointId 知识点ID
     * @return 影响行数
     */
    int deleteByQuestionIdAndKnowledgePointId(@Param("questionId") UUID questionId, @Param("knowledgePointId") UUID knowledgePointId);
    
    /**
     * 根据题目ID删除所有关联
     * 
     * @param questionId 题目ID
     * @return 影响行数
     */
    int deleteByQuestionId(@Param("questionId") UUID questionId);
    
    /**
     * 根据知识点ID删除所有关联
     * 
     * @param knowledgePointId 知识点ID
     * @return 影响行数
     */
    int deleteByKnowledgePointId(@Param("knowledgePointId") UUID knowledgePointId);
    
    /**
     * 根据题目ID查询所有关联
     * 
     * @param questionId 题目ID
     * @return 关联列表
     */
    List<QuestionKnowledgePoint> selectByQuestionId(@Param("questionId") UUID questionId);
    
    /**
     * 根据知识点ID查询所有关联
     * 
     * @param knowledgePointId 知识点ID
     * @return 关联列表
     */
    List<QuestionKnowledgePoint> selectByKnowledgePointId(@Param("knowledgePointId") UUID knowledgePointId);


    List<MathKnowledgePoint> listKnowledgePointByQuestionId(@Param("questionId") UUID questionId,
                                                            @Param("grade") Integer grade,
                                                            @Param("semester") Integer semester,
                                                            @Param("publisher") PublisherType publisher);

    UUID getSimilarQuestion(@Param("questionId") UUID questionId);

    List<QuestionKnowledgePointDTO> listKnowledgePointDTOByQuestionId(@Param("questionId") UUID questionId);

}
