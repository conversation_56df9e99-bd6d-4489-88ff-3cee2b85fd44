package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.dto.DerivativeFileDTO;
import com.joinus.knowledge.model.dto.TextbookFileDTO;
import com.joinus.knowledge.model.entity.FileDerivative;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【file_derivatives】的数据库操作Mapper
* @createDate 2025-03-06 22:57:14
* @Entity com.joinus.knowledge.entity.FileDerivative
*/
public interface FileDerivativeMapper extends BaseMapper<FileDerivative> {

    List<DerivativeFileDTO> listByTextbookId(UUID bookId);
}




