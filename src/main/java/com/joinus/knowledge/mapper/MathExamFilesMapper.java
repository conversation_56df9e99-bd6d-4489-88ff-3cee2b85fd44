package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.entity.MathExamFiles;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.vo.FileVO;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_exam_files(试卷与文件关联表)】的数据库操作Mapper
* @createDate 2025-03-20 18:15:37
* @Entity com.joinus.knowledge.model.entity.MathExamFiles
*/
public interface MathExamFilesMapper extends BaseMapper<MathExamFiles> {

    List<FileVO> listFilesByExamId(UUID examId);
}




