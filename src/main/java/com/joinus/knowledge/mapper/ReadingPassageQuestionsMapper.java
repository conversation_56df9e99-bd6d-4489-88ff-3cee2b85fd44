package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.entity.ReadingPassageQuestions;
import com.joinus.knowledge.model.param.ReadingPassageParam;
import com.joinus.knowledge.model.vo.ReadingPassageQuestionsVO;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.UUID;

public interface ReadingPassageQuestionsMapper extends BaseMapper<ReadingPassageQuestions> {
    /**
     * 根据文章获取阅读题
     *
     * @param param
     * @return
     */
    List<ReadingPassageQuestionsVO> getQuestions(@Param("param") ReadingPassageParam param);

    List<ReadingPassageQuestions> getUnsetQuestions();

    HashMap<String, Object> getAiAuditResult(@Param("questionId") UUID questionId);
}