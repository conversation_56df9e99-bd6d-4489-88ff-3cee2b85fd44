package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.dto.QuestionTypesMappingDTO;
import com.joinus.knowledge.model.entity.QuestionTypesMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_type_questions】的数据库操作Mapper
* @createDate 2025-03-01 10:22:43
* @Entity com.joinus.knowledge.entity.QuestionTypesMapping
*/
public interface QuestionTypesMappingMapper extends BaseMapper<QuestionTypesMapping> {
    
    /**
     * 插入题目和题型关联
     * 
     * @param questionId 题目ID
     * @param questionTypeId 题型ID
     * @return 影响行数
     */
    int insertQuestionTypesMapping(@Param("questionId") UUID questionId, @Param("questionTypeId") UUID questionTypeId);
    
    /**
     * 删除题目和题型关联
     * 
     * @param questionId 题目ID
     * @param questionTypeId 题型ID
     * @return 影响行数
     */
    int deleteByQuestionIdAndQuestionTypeId(@Param("questionId") UUID questionId, @Param("questionTypeId") UUID questionTypeId);
    
    /**
     * 根据题目ID删除所有关联
     * 
     * @param questionId 题目ID
     * @return 影响行数
     */
    int deleteByQuestionId(@Param("questionId") UUID questionId);
    
    /**
     * 根据题型ID删除所有关联
     * 
     * @param questionTypeId 题型ID
     * @return 影响行数
     */
    int deleteByQuestionTypeId(@Param("questionTypeId") UUID questionTypeId);
    
    /**
     * 根据题目ID查询所有关联
     * 
     * @param questionId 题目ID
     * @return 关联列表
     */
    List<QuestionTypesMapping> selectByQuestionId(@Param("questionId") UUID questionId);
    
    /**
     * 根据题型ID查询所有关联
     * 
     * @param questionTypeId 题型ID
     * @return 关联列表
     */
    List<QuestionTypesMapping> selectByQuestionTypeId(@Param("questionTypeId") UUID questionTypeId);

    /**
     * 根据题目ID查询所有关联题型
     *
     * @param questionId 题目ID
     * @return 关联列表
     */
    List<QuestionTypesMappingDTO> listQuestionTypesMappingDTOByQuestionId(@Param("questionId") UUID questionId);

}
