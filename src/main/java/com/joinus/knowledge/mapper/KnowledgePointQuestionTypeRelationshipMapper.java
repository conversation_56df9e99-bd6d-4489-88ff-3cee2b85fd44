package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.entity.KnowledgePointQuestionTypeRelationship;
import com.joinus.knowledge.model.entity.SectionKnowledgePoint;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

public interface KnowledgePointQuestionTypeRelationshipMapper extends BaseMapper<KnowledgePointQuestionTypeRelationship> {

    List<KnowledgePointQuestionTypeRelationship> listByKnowledgePointIds(@Param("knowledgePointIds") List<UUID> knowledgePointIds);
}




