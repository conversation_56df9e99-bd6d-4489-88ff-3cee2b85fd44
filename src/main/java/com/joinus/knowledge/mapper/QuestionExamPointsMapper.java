package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.entity.QuestionExamPoints;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【question_exam_points】的数据库操作Mapper
* @createDate 2025-03-01 10:22:43
* @Entity com.joinus.knowledge.entity.QuestionExamPoints
*/
public interface QuestionExamPointsMapper extends BaseMapper<QuestionExamPoints> {
    
    /**
     * 插入题目考点关系
     */
    int insertQuestionExamPoints(@Param("questionId") UUID questionId, @Param("examPointId") UUID examPointId);
    
    /**
     * 根据题目ID和考点ID删除关系
     */
    int deleteByQuestionIdAndExamPointId(@Param("questionId") UUID questionId, @Param("examPointId") UUID examPointId);
    
    /**
     * 根据题目ID删除关系
     */
    int deleteByQuestionId(@Param("questionId") UUID questionId);
    
    /**
     * 根据考点ID删除关系
     */
    int deleteByExamPointId(@Param("examPointId") UUID examPointId);
    
    /**
     * 根据题目ID查询关系
     */
    List<QuestionExamPoints> selectByQuestionId(@Param("questionId") UUID questionId);
    
    /**
     * 根据考点ID查询关系
     */
    List<QuestionExamPoints> selectByExamPointId(@Param("examPointId") UUID examPointId);
}
