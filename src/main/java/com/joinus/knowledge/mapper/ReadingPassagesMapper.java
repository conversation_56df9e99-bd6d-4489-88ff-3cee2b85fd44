package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.entity.ReadingPassages;
import com.joinus.knowledge.model.param.ReadingPassageParam;
import com.joinus.knowledge.model.po.ClonePassageAndQuestionsPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

public interface ReadingPassagesMapper extends BaseMapper<ReadingPassages> {
    /**
     * 根据参数获取阅读文章
     *
     * @param param
     * @return
     */
    ReadingPassages getByUnitId(@Param("param") ReadingPassageParam param);

    List<ClonePassageAndQuestionsPO> getClonePassageAndQuestions();

    void updateProcessStatusByPassageId(@Param("passageId") UUID passageId);
}