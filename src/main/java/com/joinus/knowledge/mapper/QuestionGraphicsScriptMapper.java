package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.entity.QuestionGraphicsScript;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.dto.CandidateQuestion;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_graphics_scripts(问题图案生成脚本)】的数据库操作Mapper
* @createDate 2025-04-17 16:04:14
* @Entity com.joinus.knowledge.model.entity.QuestionGraphicsScript
*/
public interface QuestionGraphicsScriptMapper extends BaseMapper<QuestionGraphicsScript> {

    List<Map<String, Object>> getInitialKeyPointCounts();

    List<CandidateQuestion> getAvailableCandidates(@Param("questionIds") Set<UUID> questionIds);


}




