package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.dto.TextbookFileDTO;
import com.joinus.knowledge.model.entity.File;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【files】的数据库操作Mapper
* @createDate 2025-03-06 16:06:22
* @Entity com.joinus.knowledge.entity.Files
*/
public interface FilesMapper extends BaseMapper<File> {

    List<TextbookFileDTO> listFilesByBookId(UUID bookId);

    List<Map<String,Object>> getFilesByBookId(UUID bookId);

    List<File> listQuestionOriginImages(UUID questionId);

    String getSectionHtml(@Param("textbookId") UUID textbookId,@Param("startPage") Integer startPage,@Param("endPage") Integer endPage);

}




