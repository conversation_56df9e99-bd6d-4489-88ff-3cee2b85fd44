package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.dto.QuestionFileDTO;
import com.joinus.knowledge.model.entity.QuestionFile;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_files】的数据库操作Mapper
* @createDate 2025-03-11 18:31:25
* @Entity generator.domain.QuestionFile
*/
@Mapper
public interface QuestionFileMapper extends BaseMapper<QuestionFile> {

    List<QuestionFileDTO> listByQuestionId(UUID questionId);
}




