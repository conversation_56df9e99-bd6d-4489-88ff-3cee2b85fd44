package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.entity.MathExamQuestion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.entity.MathQuestion;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_exam_questions(试卷与题目关联表)】的数据库操作Mapper
* @createDate 2025-03-20 18:15:37
* @Entity com.joinus.knowledge.model.entity.MathExamQuestions
*/
public interface MathExamQuestionsMapper extends BaseMapper<MathExamQuestion> {

    List<MathQuestion> listQuestionsByExamId(UUID examId);

}




