package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.entity.MathExam;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.po.ExamQuestionPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.model.param.MathExamPageQueryParam;
import com.joinus.knowledge.model.vo.MathExamVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_exams(试卷表)】的数据库操作Mapper
* @createDate 2025-03-20 18:15:37
* @Entity com.joinus.knowledge.model.entity.MathExams
*/
public interface MathExamsMapper extends BaseMapper<MathExam> {

    List<ExamQuestionPO> listQuestionByExamId(UUID examId);

    /**
     * 试卷分页条件查询
     */
    IPage<MathExamVO> pageQuery(Page<MathExamVO> page, @Param("param") MathExamPageQueryParam param);

    List<QuestionDetailVO> listMathExamQuestionsByExamId(UUID id);

    QuestionDetailVO getQuestionDetailById(@Param("examId") UUID examId, @Param("questionId") UUID questionId);
}
