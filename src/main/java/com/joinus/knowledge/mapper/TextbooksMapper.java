package com.joinus.knowledge.mapper;

import com.joinus.knowledge.model.entity.Textbooks;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.vo.SectionKeypointVO;
import com.joinus.knowledge.model.vo.TextbookPointVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_textbooks】的数据库操作Mapper
* @createDate 2025-03-06 16:06:22
* @Entity com.joinus.knowledge.entity.Textbooks
*/
public interface TextbooksMapper extends BaseMapper<Textbooks> {
    List<TextbookPointVO> getKeypointsByBookIdAndPageNo(@Param("bookId") UUID bookId, @Param("pageNo") Integer pageNo);

    TextbookPointVO getQuestionTypeByKeypointId(@Param("keyPointId") UUID keyPointId, @Param("textbookId") UUID textbookId, @Param("pageNo") Integer pageNo);

    TextbookPointVO getKnowledgePointByKeypointId(@Param("keyPointId") UUID keyPointId, @Param("textbookId") UUID textbookId, @Param("pageNo") Integer pageNo);

    List<SectionKeypointVO> listAllKnowledgePointsAndQuestionTypes(@Param("id") UUID id);
}




