package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathSection;
import com.joinus.knowledge.model.entity.QuestionKnowledgePoint;
import com.joinus.knowledge.model.entity.QuestionTypesMapping;
import com.joinus.knowledge.model.po.SectionKeypointCountPO;
import com.joinus.knowledge.model.po.SectionKnowledgePointPO;
import com.joinus.knowledge.model.po.SectionQuestionTypePO;
import com.joinus.knowledge.model.po.SectionVideoPO;
import com.joinus.knowledge.model.vo.MathSectionVO;
import com.joinus.knowledge.model.vo.SectionKeypointVO;
import com.joinus.knowledge.model.vo.SectionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_sections】的数据库操作Mapper
* @createDate 2025-03-06 16:06:22
* @Entity com.joinus.knowledge.entity.MathSections
*/
public interface MathSectionMapper extends BaseMapper<MathSection> {

    List<MathSection> getByBookId(UUID bookId);

    List<MathSection> getByPageNo(@Param("pageNo") Integer pageNo, @Param("textbookId") UUID textbookId, @Param("sectionId") UUID sectionId);

    List<SectionVO> listAllSectionsByBookId(@Param("bookId") UUID bookId);

    List<SectionKeypointVO> listKeypointsById(@Param("sectionId") UUID sectionId);

    List<SectionKnowledgePointPO> listDuplicateKnowledgePoints();

    List<QuestionKnowledgePoint> listBaseQuestionByKnowledgePointIds(@Param("kpIds") List<UUID> kpIds);
    
    List<SectionQuestionTypePO> listDuplicateQuestionTypes();

    List<QuestionTypesMapping> listBaseQuestionByQuestionTypeIds(@Param("qtIds") List<UUID> qtIds);

    List<MathSectionVO> list(@Param("name") String name,
                             @Param("grade") Integer grade,
                             @Param("semester") Integer semester,
                             @Param("publisher") PublisherType publisher,
                             @Param("chapterName") String chapterName,
                             @Param("chapterId") UUID chapterId);

    List<SectionVO> listAllsectionsByPublisher(PublisherType publisher);

    List<SectionVO> listSections(@Param("grade") Integer grade,
                                 @Param("semester") Integer semester,
                                 @Param("publisher") PublisherType publisher);

    List<SectionKeypointCountPO> listKnowledgePointCountAndQuestionTypeCount(@Param("sectionIdList") List<UUID> list);

    List<SectionVideoPO> querySectionVideos(@Param("sectionIds") List<UUID> sectionIds);
}
