package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
public enum PublisherType {
    BEI_SHI_DA("北师大"),
    HUA_SHI_DA("华师大"),
    REN_JIAO("人教");

    @EnumValue
    @Schema(description = "出版社名称", implementation = String.class, example = "北师大")
    private final String value;


    PublisherType(String value) {
        this.value = value;
    }
}
