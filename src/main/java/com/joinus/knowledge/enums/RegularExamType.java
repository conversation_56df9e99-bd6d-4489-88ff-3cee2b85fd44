package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RegularExamType {
    FINAL_EXAM("FINAL_EXAM", "期末"),
    MIDTERM_EXAM("MIDTERM_EXAM", "期中"),
    MONTHLY_EXAM("MONTHLY_EXAM", "月考"),
    OTHER("OTHER", "其他");

    @EnumValue
    private final String value;
    private final String desc;

}
