package com.joinus.knowledge.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ReadingAIAbilityType {
    CORRECT(1,"AI批改",PromptEnum.READING_AI_CORRECT),
    WEAK_KNOWLEDGE_POINT_ANALYSIS(2,"薄弱知识点分析",PromptEnum.READING_WEAK_KNOWLEDGE_POINT_ANALYSIS),
    TRAINING_SUGGESTION(3,"综合训练建议",PromptEnum.READING_TRAINING_SUGGESTION),
    ANSWER_COMPARE(5,"答案比对",PromptEnum.READING_ANSWER_COMPARE),
    SINGLE_REPORT(6,"次报告",PromptEnum.READING_SINGLE_REPORT),
    WEEK_REPORT(7,"周报告",PromptEnum.READING_WEEK_REPORT),
    WEAK_QUESTION_TYPE_ANALYSIS(4,"薄弱题型分析",PromptEnum.READING_WEAK_QUESTION_TYPE_ANALYSIS);


    private final Integer type;
    private final String description;
    private final PromptEnum promptEnum;

    public static ReadingAIAbilityType getByType(Integer readingAIAbilityType) {
        return Arrays.stream(ReadingAIAbilityType.values())
                .filter(t -> t.getType().equals(readingAIAbilityType))
                .findFirst()
                .orElse(null);
    }
}
