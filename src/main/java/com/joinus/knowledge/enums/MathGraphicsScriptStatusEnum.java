package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MathGraphicsScriptStatusEnum {
    PENDING("PENDING", "待完成"),
    SUBMITTED("SUBMITTED", "已完成"),
    VERIFIED("VERIFIED", "已核验"),
    REJECTED("REJECTED", "退回重做"),
    FAILED("FAILED", "做图存在问题"),
    IGNORED("IGNORED", "无需配图");

    @EnumValue
    private final String name;
    private final String description;
}
