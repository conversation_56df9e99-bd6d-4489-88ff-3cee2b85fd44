package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExamTagType {

    ALIAS("ALIAS", "别名"),
    ELITE_SCHOOL_EXAM_PAGERS("ELITE_SCHOOL_EXAM_PAGERS", "名校试卷"),
    REGULAR_EXAM_TYPE("REGULAR_EXAM_TYPE", "常规考试卷类型");

    @EnumValue
    private final String value;

    private String desc;
}
