package com.joinus.knowledge.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SemesterType {
    SEMESTER_1(1, "上学期"),
    SEMESTER_2(2, "下学期");

    private final int code;
    private final String description;

    public static SemesterType of(Integer semester) {
        if (null == semester) {
            return null;
        }
        for (SemesterType semesterType : SemesterType.values()) {
            if (semesterType.getCode() == semester) {
                return semesterType;
            }
        }
        return null;
    }

    public static SemesterType ofDescription(String description) {
        if (null == description) {
            return null;
        }
        for (SemesterType semesterType : SemesterType.values()) {
            if (semesterType.getDescription().equals(description)) {
                return semesterType;
            }
        }
        return null;
    }

}