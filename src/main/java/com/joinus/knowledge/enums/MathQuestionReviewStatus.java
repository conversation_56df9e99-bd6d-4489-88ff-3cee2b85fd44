package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum MathQuestionReviewStatus {
    PENDING_FIRST_REVIEW("PENDING_FIRST_REVIEW", "待初审", null, MathQuestionReviewType.FIRST_REVIEW, 0),
    APPROVED_FIRST_REVIEW("APPROVED_FIRST_REVIEW", "初审通过", true, MathQuestionReviewType.FIRST_REVIEW, 1),
    REJECTED_FIRST_REVIEW("REJECTED_FIRST_REVIEW", "初审不通过", false, MathQuestionReviewType.FIRST_REVIEW, 0),
    PENDING_SECOND_REVIEW("PENDING_SECOND_REVIEW", "待复审", null, MathQuestionReviewType.SECOND_REVIEW, 0),
    APPROVED_SECOND_REVIEW("APPROVED_SECOND_REVIEW", "复审通过", true, MathQuestionReviewType.SECOND_REVIEW, 2),
    REJECTED_SECOND_REVIEW("REJECTED_SECOND_REVIEW", "复审不通过", false, MathQuestionReviewType.SECOND_REVIEW, 0),
    ;

    @EnumValue
    private final String name;
    private final String description;
    private final Boolean verified;
    private final MathQuestionReviewType reviewType;
    private Integer priority;

    public static MathQuestionReviewStatus ofVerifiedAndReviewType(Boolean verified, MathQuestionReviewType reviewType) {
        return Arrays.stream(MathQuestionReviewStatus.values())
                .filter(e -> verified.equals(e.verified) && e.reviewType.equals(reviewType))
                .findFirst()
                .orElse(null);
    }

    public static MathQuestionReviewStatus ofPendingAndReviewType(MathQuestionReviewType reviewType) {
        return Arrays.stream(MathQuestionReviewStatus.values())
                .filter(e -> e.verified == null && e.reviewType.equals(reviewType))
                .findFirst()
                .orElse(null);
    }
}
