package com.joinus.knowledge.enums;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

import static com.joinus.knowledge.enums.MathQuestionDimensionOptionType.*;

@Getter
@AllArgsConstructor
public enum MathQuestionDimensionType {

    PROBLEM_TEXT("题目文本", CollUtil.toList(FULLY_SATISFIED,  BASICALLY_SATISFIED, PARTIALLY_SATISFIED, NOT_SATISFIED, NULL)),
    VISUAL_ELEMENTS("视觉元素", CollUtil.toList(FULLY_SATISFIED,  BASICALLY_SATISFIED, PARTIALLY_SATISFIED, NOT_SATISFIED, NOT_APPLICABLE, NULL)),
    FORMAT_AND_TYPE("格式与题型", CollUtil.toList(FULLY_SATISFIED,  BASICALLY_SATISFIED, PARTIALLY_SATISFIED, NOT_SATISFIED, NULL)),
    CORE_KNOWLEDGE_POINTS("核心知识点", CollUtil.toList(FULLY_SATISFIED,  BASICALLY_SATISFIED, PARTIALLY_SATISFIED, NOT_SATISFIED, NULL)),
    PRIMARY_SOLUTION_METHOD("主要解题方法/策略", CollUtil.toList(FULLY_SATISFIED,  BASICALLY_SATISFIED, PARTIALLY_SATISFIED, NOT_SATISFIED, NULL)),
    SOLUTION_LOGICAL_STRUCTURE("解题步骤逻辑结构", CollUtil.toList(FULLY_SATISFIED,  BASICALLY_SATISFIED, PARTIALLY_SATISFIED, NOT_SATISFIED, NULL)),
    COGNITIVE_LOAD_AND_DIFFICULTY_LEVEL("认知负荷/难度等级", CollUtil.toList(DIFFICULTY_BASICALLY_REASONABLE,  DIFFICULTY_BASICALLY_REASONABLE, DIFFICULTY_WITH_UNCERTAINTY, DIFFICULTY_DIFFICULT_OR_LOW_RELIABILITY, NULL)),
    MATHEMATICAL_RIGOR_AND_CORRECTNESS("数学严谨性和正确性", CollUtil.toList(FULLY_SATISFIED,  BASICALLY_SATISFIED, PARTIALLY_SATISFIED, INSUFFICIENT_INFORMATION, NULL));

    @EnumValue
    private final String description;

    private List<MathQuestionDimensionOptionType> options;
    
    /**
     * 使用单个选项构造枚举
     * @param description 描述
     * @param option 单个选项
     */
    MathQuestionDimensionType(String description, MathQuestionDimensionOptionType option) {
        this.description = description;
        this.options = CollUtil.toList(option);
    }
    
    /**
     * 使用多个选项构造枚举
     * @param description 描述
     * @param options 多个选项
     */
    MathQuestionDimensionType(String description, MathQuestionDimensionOptionType... options) {
        this.description = description;
        this.options = Arrays.asList(options);
    }
}
