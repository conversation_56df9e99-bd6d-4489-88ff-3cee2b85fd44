package com.joinus.knowledge.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum KnowledgeDomainLabel {
    NUMBER_AND_ALGEBRA("数与代数", PromptEnum.GENERATED_QUESTION_NUMBER_AND_ALGEBRA, AIModelType.JYSD_DEEPSEEK_R1, 100),
    GEOMETRY_AND_MEASUREMENT("图形与几何", PromptEnum.GENERATED_QUESTION_GEOMETRY_AND_MEASUREMENT, AIModelType.GEMINI_2_5_PRO, 10),
    STATISTICS_AND_PROBABILITY("统计与概率", PromptEnum.GENERATED_QUESTION_STATISTICS_AND_PROBABILITY, AIModelType.JYSD_DEEPSEEK_R1, 100),
    MATHEMATICAL_THINKING_AND_METHODS("数学思想方法", PromptEnum.GENERATED_QUESTION_MATHEMATICAL_THINKING_AND_METHODS, AIModelType.JYSD_DEEPSEEK_R1, 100),
    MATHEMATICAL_APPLICATIONS("数学应用", PromptEnum.GENERATED_QUESTION_MATHEMATICAL_APPLICATIONS, AIModelType.JYSD_DEEPSEEK_R1, 100);

    private final String label;

    private final PromptEnum promptEnum;

    private final AIModelType aiModelType;

    private final Integer poolSize;

    public static KnowledgeDomainLabel fromLabel(String label) {
        for (KnowledgeDomainLabel domainLabel : values()) {
            if (domainLabel.getLabel().equals(label)) {
                return domainLabel;
            }
        }
        throw new IllegalArgumentException("Unknown label: " + label);
    }
}
