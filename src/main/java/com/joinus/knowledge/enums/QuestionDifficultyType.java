package com.joinus.knowledge.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum QuestionDifficultyType {
    EASY_1(1, "简单"),
    EASY_2(2, "简单"),
    MIDDEL_1(3, "中等"),
    MIDDEL_2(4, "中等"),
    DIFFICULT(5, "困难");

    private final int value;

    private String desc;

    public static String getDesc(Integer difficulty) {
        //根据value获取desc
        for (QuestionDifficultyType questionDifficultyType : QuestionDifficultyType.values()) {
            if (questionDifficultyType.getValue() == difficulty) {
                return questionDifficultyType.getDesc();
            }
        }
        return "未知";
    }
}
