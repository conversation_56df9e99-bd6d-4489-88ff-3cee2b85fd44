package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MathQuestionDimensionOptionType {

    FULLY_SATISFIED("完全满足"),
    BASICALLY_SATISFIED("基本满足"),
    PARTIALLY_SATISFIED("部分满足"),
    NOT_SATISFIED("不满足"),
    NOT_APPLICABLE("不适用"),
    DIFFICULTY_CLEAR_AND_REASONABLE("难度评估明确且合理"),
    DIFFICULTY_BASICALLY_REASONABLE("难度评估基本合理"),
    DIFFICULTY_WITH_UNCERTAINTY("难度评估存在一定不确定性"),
    DIFFICULTY_DIFFICULT_OR_LOW_RELIABILITY("难度评估困难或可靠性较低"),
    INSUFFICIENT_INFORMATION("不适用信息不足"),
    NULL("NULL"),
    UNKNOWN("未知结果类型");

    @EnumValue
    private final String description;

    public static MathQuestionDimensionOptionType ofDescription(String description) {
        for (MathQuestionDimensionOptionType value : MathQuestionDimensionOptionType.values()) {
            if (value.getDescription().equals(description)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
