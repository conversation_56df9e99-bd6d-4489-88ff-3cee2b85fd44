package com.joinus.knowledge.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

@Getter
public enum BaiDuOcrErrorMessage {


    SDK100("SDK100", "图片大小超限"),
    SDK101("SDK101", "图片边长不符合要求"),
    SDK102("SDK102", "读取图片文件错误"),
    SDK108("SDK108", "连接超时或读取数据超时"),
    SDK109("SDK109", "不支持的图片格式"),
    SERVICE4("4", "集群超限额"),
    SERVICE6("6", "无权限访问该用户数据，创建应用时未勾选相关接口，请登录百度云控制台，找到对应的应用，编辑应用，勾选上相关接口，然后重试调用"),
    SERVICE14("14", "IAM鉴权失败，建议用户参照文档自查生成sign的方式是否正确，或换用控制台中ak sk的方式调用"),
    SERVICE17("17", "每天流量超限额"),
    SERVICE18("18", "QPS超限额"),
    SERVICE19("19", "请求总量超限额"),
    SERVICE100("100", "无效参数"),
    SERVICE110("110", "Access Token失效"),
    SERVICE111("111", "Access token过期"),
    SERVICE282000("282000", "服务器内部错误，如果您使用的是高精度接口，报这个错误码的原因可能是您上传的图片中文字过多，识别超时导致的，建议您对图片进行切割后再识别，其他情况请再次请求， 如果持续出现此类错误，请通过QQ群（631977213）或工单联系技术支持团队。"),
    SERVICE216100("216100", "请求中包含非法参数，请检查后重新尝试"),
    SERVICE216101("216101", "缺少必须的参数，请检查参数是否有遗漏"),
    SERVICE216102("216102", "请求了不支持的服务，请检查调用的url"),
    SERVICE216103("216103", "请求中某些参数过长，请检查后重新尝试"),
    SERVICE216110("216110", "appid不存在，请重新核对信息是否为后台应用列表中的appid"),
    SERVICE216200("216200", "图片为空，请检查后重新尝试"),
    SERVICE216201("216201", "上传的图片格式错误，现阶段我们支持的图片格式为：PNG、JPG、JPEG、BMP，请进行转码或更换图片"),
    SERVICE216202("216202", "上传的图片大小错误，现阶段我们支持的图片大小为：base64编码后小于4M，分辨率不高于4096*4096，请重新上传图片"),
    SERVICE216630("216630", "识别错误，请再次请求，如果持续出现此类错误，请通过QQ群（631977213）或工单联系技术支持团队。"),
    SERVICE216631("216631", "识别银行卡错误，出现此问题的原因一般为：您上传的图片非银行卡正面，上传了异形卡的图片或上传的银行卡正品图片不完整"),
    SERVICE216633("216633", "识别身份证错误，出现此问题的原因一般为：您上传了非身份证图片或您上传的身份证图片不完整"),
    SERVICE216634("216634", "检测错误，请再次请求，如果持续出现此类错误，请通过QQ群（631977213）或工单联系技术支持团队。"),
    SERVICE282003("282003", "请求参数缺失"),
    SERVICE282005("282005", "处理批量任务时发生部分或全部错误，请根据具体错误码排查"),
    SERVICE282006("282006", "批量任务处理数量超出限制，请将任务数量减少到10或10以下"),
    SERVICE282110("282110", "URL参数不存在，请核对URL后再次提交"),
    SERVICE282111("282111", "URL格式非法，请检查url格式是否符合相应接口的入参要求"),
    SERVICE282112("282112", "url下载超时，请检查url对应的图床/图片无法下载或链路状况不好，您可以重新尝试以下，如果多次尝试后仍不行，建议更换图片地址"),
    SERVICE282113("282113", "URL返回无效参数"),
    SERVICE282114("282114", "URL长度超过1024字节或为0"),
    SERVICE282808("282808", "request id xxxxx 不存在"),
    SERVICE282809("282809", "返回结果请求错误（不属于excel或json）"),
    SERVICE282810("282810", "图像识别错误");

    private final String code;
    private final String msg;

    BaiDuOcrErrorMessage(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    // 根据code获取枚举
    public static BaiDuOcrErrorMessage getByCode(String code) {
        if (StrUtil.isBlank(code)) return null;
        for (BaiDuOcrErrorMessage value : values()) {
            if (StrUtil.equals(value.code, code)) {
                return value;
            }
        }
        return null;
    }

}
