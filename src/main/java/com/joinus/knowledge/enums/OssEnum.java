package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicReference;

@Getter
@AllArgsConstructor
public enum OssEnum {

    MINIO_EDU_KNOWLEDGE_HUB("minio", "education-knowledge-hub"),
    ALIYUN_EDU_KNOWLEDGE_HUB("aliyun", "edu-knowledge-hub");

    @Schema(description = "oss平台类型", implementation = String.class, example = "aliyun")
    private final String type;

    @Schema(description = "oss平台bucket", implementation = String.class, example = "edu-knowledge-hub")
    private final String bucket;


    public static OssEnum ofTypeAndBucket(String type, String bucket) {
        // 找到第一个匹配项即可停止遍历
        return Arrays.stream(OssEnum.values())
                .filter(e -> e.getType().equals(type) && e.getBucket().equals(bucket))
                .findFirst()
                .orElse(MINIO_EDU_KNOWLEDGE_HUB);
    }
}
