package com.joinus.knowledge.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum Genre {
    //叙事阅读
    STORY("叙事阅读"),
    //写人阅读
    PERSON("写人阅读"),
    //写景状物阅读
    SCENERY("写景状物阅读"),
    //说明文阅读
    EXPLANATORY("说明文阅读"),
    //童话寓言阅读
    FAIRY_TALE("童话寓言阅读"),
    //名著阅读
    CLASSIC("名著阅读"),
    //非连续性文本阅读
    NON_CONTINUOUS("非连续性文本阅读"),
    //散文阅读
    ESSAY("散文阅读"),
    //应用文阅读
    APPLICATION("应用文阅读"),
    //议论文阅读
    ARGUMENTATIVE("议论文阅读"),
    //跨学科阅读
    CROSS_DISCIPLINARY("跨学科阅读"),
    //现代诗歌阅读
    MODERN_POETRY("现代诗歌阅读"),
    //整本书阅读
    WHOLE_BOOK("整本书阅读"),
    //古诗古文阅读
    CLASSIC_POETRY("古诗古文阅读");

    private final String genreName;

    public static Genre getByName(String name) {
        return Arrays.stream(values())
                .filter(t -> t.name().equals(name))
                .findFirst()
                .orElse(null);
    }
    public static Genre getByGenreName(String genreName) {
        return Arrays.stream(values())
                .filter(t -> t.getGenreName().equals(genreName))
                .findFirst()
                .orElse(null);
    }
}