package com.joinus.knowledge.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum GradeType {
    GRADE_7(7, "七年级"),
    GRADE_8(8, "八年级"),
    GRADE_9(9, "九年级");

    private final int code;
    private final String description;


    public static GradeType of(Integer grade) {
        if (null == grade) {
            return null;
        }
        for (GradeType gradeType : GradeType.values()) {
            if (gradeType.getCode() == grade) {
                return gradeType;
            }
        }
        return null;
    }

    public static GradeType ofDescription(String description) {
        if (null == description) {
            return null;
        }
        for (GradeType gradeType : GradeType.values()) {
            if (gradeType.getDescription().equals(description)) {
                return gradeType;
            }
        }
        return null;
    }
}