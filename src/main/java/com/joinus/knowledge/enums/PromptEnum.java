package com.joinus.knowledge.enums;

public enum PromptEnum {
    ANALYZE_K<PERSON><PERSON>LEDGE_POINTS,
    ANALY<PERSON>E_QUESTION,
    CHECK_IMAGE_EXISTS,
    SOLVE_MATH_PROBLEM,
    SOLVE_MATH_PROBLEM_WITH_IMAGE,
    GENERATE_MATH_QUESTION_FROM_QUESTION_AND_KNOWLEDGE_POINTS,
    <PERSON>UD<PERSON>_QUESTION_TYPE,
    RECOGNIZE_EXAM_PAPER_TITLE,
    QUESTION_KNOWLEDGE_DOMAIN_LABEL,
    IMAGE_OCR,
    MATH_IMAGE_OCR,
    GENERATED_QUESTION_JSON_FORMAT,
    GENERATED_QUESTION_NUMBER_AND_ALGEBRA,
    GENERATED_QUESTION_GEOMETRY_AND_MEASUREMENT,
    GENERATED_QUESTION_STATISTICS_AND_PROBABILITY,
    GENERATED_QUESTION_MATHEMATICAL_THINKING_AND_METHODS,
    GENERATED_QUESTION_MATHEMATICAL_APPLICATIONS,
    READING_AI_CORRECT,
    READING_WEAK_KNOWLEDGE_POINT_ANALYSIS,
    READING_WEAK_KNOWLEDGE_POINT_ANALYSIS_PLACEHOLDER,
    READING_TRAINING_SUGGESTION,
    READING_WEAK_QUESTION_TYPE_ANALYSIS,
    READING_SINGLE_REPORT,
    READING_WEEK_REPORT,
    READING_ANSWER_COMPARE,
    SOLVE_MATH_PROBLEM_WITH_JSON_SCHEMA
}
