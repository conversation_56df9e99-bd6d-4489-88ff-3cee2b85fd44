package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExamSourceType {
    USER_UPLOAD("用户上传", QuestionSourceType.USER_EXAM),
    SPECIAL_TRAINING("专项训练", QuestionSourceType.AI),
    PAST_EXAM_PAPER("中考真题", QuestionSourceType.ZHONG_KAO_EXAM),
    EXAM_BLIND_SPOTS_TRAINING("考点盲区训练", QuestionSourceType.AI),
    HOLIDAY_TRAINING("暑期训练", QuestionSourceType.AI),
    REGULAR_EXAM_PAPER("常规考试卷", QuestionSourceType.REGULAR_EXAM);

    @EnumValue
    private final String name;

    private final QuestionSourceType questionSourceType;
}
