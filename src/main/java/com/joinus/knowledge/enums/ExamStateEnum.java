package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
public enum ExamStateEnum {
    TODO("TODO", "待分析") {
        @Override
        public ExamStateEnum handle(ExamHandleEvent event) {
            return this;
        }
    },
    IN_PROGRESS("IN_PROGRESS", "处理中") {
        @Override
        public ExamStateEnum handle(ExamHandleEvent event) {
            return this;
        }
    },
    DONE("DONE", "分析完成") {
        @Override
        public ExamStateEnum handle(ExamHandleEvent event) {
            return switch (event) {
                case REVIEW_PASSED -> RECOMMEND_DISABLED;
                case REVIEW_FAILED -> REVIEW_REJECTED;
                default -> throw new IllegalStateException(String.format("试卷当前状态[%s]不允许进行[%s]操作", this.getDesc(), event.getDesc()));
            };
        }
    },
    UPLOADED("UPLOADED", "用户已上传") {
        @Override
        public ExamStateEnum handle(ExamHandleEvent event) {
            return this;
        }
    },
    AUTO_RECOGNIZED("AUTO_RECOGNIZED", "自动识别完成") {
        @Override
        public ExamStateEnum handle(ExamHandleEvent event) {
            return this;
        }
    },
    HUMAN_RECOGNIZED("HUMAN_RECOGNIZED", "人工纠错完成") {
        @Override
        public ExamStateEnum handle(ExamHandleEvent event) {
            return this;
        }
    },
    REVIEW_REJECTED("REVIEW_REJECTED", "审核不通过") {
        @Override
        public ExamStateEnum handle(ExamHandleEvent event) {
            return this;
        }
    },
    RECOMMEND_DISABLED("RECOMMEND_DISABLED", "不启用推荐") {
        @Override
        public ExamStateEnum handle(ExamHandleEvent event) {
            return this;
        }
    },
    RECOMMEND_ENABLED("RECOMMEND_ENABLED", "启用推荐") {
        @Override
        public ExamStateEnum handle(ExamHandleEvent event) {
            return this;
        }
    };

    @EnumValue
    @Schema(description = "试卷状态", implementation = String.class, example = "TODO")
    private final String value;

    private String desc;


    ExamStateEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public abstract ExamStateEnum handle(ExamHandleEvent event);
}
