package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AIModelType {

    JYSD_QWEN_VL("jysd-qwen2.5-vl-72b", ModelProviderType.HTTP, "jysdQwenVLChatClient", PgAIModelType.OTHER, "qwenVlWebClient"),
    JYSD_DEEPSEEK_R1("jysd-deepseekr1", ModelProviderType.HTTP, "jysdDeepSeekR1ChatClient", PgAIModelType.DEEPSEEK_R1, "jysdDeepseekR1WebClient"),
    DOUBAO_DEEPSEEK_R1("ep-20250217000346-2wfvt", ModelProviderType.VOLCEGINE_SDK ,"ep-20250217000346-2wfvt", PgAIModelType.DEEPSEEK_R1, ""),
    DOUBAO_VISION_PRO("ep-20250625180014-gtqkz", ModelProviderType.VOLCEGINE_SDK, "ep-20250625180014-gtqkz", PgAIModelType.DOUBAO_VISION_1_5_PRO, ""),
    DOUBAO_LITE("doubao-1-5-lite-32k-250115", ModelProviderType.VOLCEGINE_SDK,"doubao-1-5-lite-32k-250115", PgAIModelType.OTHER, ""),
    GEMINI_2_5_PRO("models/gemini-2.5-pro-preview-03-25", ModelProviderType.HTTP, "", PgAIModelType.GEMINI_2_5_PRO, "geminiWebClient"),
    GEMINI_2_5_FLASH("models/gemini-2.5-flash-preview-04-17", ModelProviderType.HTTP, "", PgAIModelType.GEMINI_2_5_PRO, "geminiWebClient"),
    JYSD_QWEN_3("jysd-qwen3-235b", ModelProviderType.HTTP, "", PgAIModelType.OTHER, "qwen3WebClient"),
    DOUBAO_SEED_1_6("ep-20250708172952-w6vj6", ModelProviderType.VOLCEGINE_SDK, "ep-20250708172952-w6vj6", PgAIModelType.OTHER, "")
    ;

    @EnumValue
    private final String modelName;

    private final ModelProviderType providerType;

    private final String clientModel;

    private final PgAIModelType pgAiModelType;

    private final String webClientName;
}
