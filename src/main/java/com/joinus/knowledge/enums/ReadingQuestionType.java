package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ReadingQuestionType {
    CHOICE("选择"),
    FILL_IN_THE_BLANK("填空"),
    JUDGMENT("判断"),
    QA("问答"),
    EXPLANATION("解释词句"),
    SENTENCE_REWRITE("仿写/改写句子"),
    KEY_SENTENCE("找中心句/关键句"),
    CHART("图表"),
    MATCHING("连线"),
    MIND_MAP("思维导图"),
    SORTING("排序"),
    INSERT_SENTENCE("插入句子"),
    PICTURE_CORRESPONDENCE("图片对应"),
    DETERMINE_TITLE("拟定标题"),
    WORD_FILLING("词语填空"),
    SENTENCE_APPRECIATION("句子赏析题"),
    COMPARE_ANALYSIS("对比分析"),
    CHARACTER_EVALUATION("人物评价"),
    LIVING_SUGGESTION("生活建议题"),
    SIMPLE_CALCULATION("简单计算题"),
    DISCRIMINATION("辨析题"),
    EMOTION_THEME("情感主旨"),
    SCENE_DEPICTION("场景描绘"),
    MODIFICATION("修改题"),
    OPINION_DISCUSSION("观点讨论"),
    SUMMARY("归纳总结"),
    ARGUMENTATION_METHOD_ANALYSIS("论证方法分析"),
    POEM_TRANSLATION("古诗今译"),
    BREAK_SENTENCE("断句/划分节奏"),
    MICRO_WRITING("微写作"),
    OTHER("其它");

    @EnumValue
    private final String type;

    public static List<Map<String, String>> list() {
        return Arrays.stream(ReadingQuestionType.values())
                .map(type -> Map.of(
                        "key", type.name(),
                        "value", type.getType()
                ))
                .collect(Collectors.toList());
    }

    public static ReadingQuestionType getByType(String type) {
        return Arrays.stream(values())
                .filter(t -> t.getType().equals(type))
                .findFirst()
                .orElse(OTHER);
    }

    public static ReadingQuestionType getByName(String name) {
        return Arrays.stream(values())
                .filter(t -> t.name().equals(name))
                .findFirst()
                .orElse(OTHER);
    }
}