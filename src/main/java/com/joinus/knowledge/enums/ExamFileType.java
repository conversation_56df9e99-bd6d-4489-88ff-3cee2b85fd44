package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExamFileType {
    ORIGINAL_PAPER("试卷题目原文件", "ORIGINAL_PAPER"),
    ANSWER_AND_ANALYSIS("试卷答案(解析)文件", "ANSWER_AND_ANALYSIS"),
    HANDWRITING_REMOVED_PAPER("试卷题目抹除笔迹后文件", "HANDWRITING_REMOVED_PAPER");


    private final String name;

    @EnumValue
    private String value;

}
