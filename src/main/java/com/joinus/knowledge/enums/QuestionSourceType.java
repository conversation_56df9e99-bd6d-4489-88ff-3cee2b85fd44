package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
public enum QuestionSourceType {
    BOOK("BO<PERSON>", "书籍录入"),
    USER("USER", "用户上传"),
    AI("AI", "AI生成"),
    TODO_USER_EXAM("TODO_USER_EXAM", "用户试卷上传未分析"),
    USER_EXAM("USER_EXAM", "用户试卷上传已分析"),
    ZHONG_KAO_EXAM("ZHONG_KAO_EXAM", "中考真题"),
    REGULAR_EXAM("REGULAR_EXAM", "常规考试题");

    @EnumValue
    private final String name;

    @Schema(description = "描述", implementation = String.class, example = "书籍录入")
    private final String description;

    QuestionSourceType(String name, String description) {
        this.name = name;
        this.description = description;
    }

}
