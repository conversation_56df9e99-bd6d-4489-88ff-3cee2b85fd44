package com.joinus.knowledge.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AliQuestionType {
    CHOICE(0, "选择题", QuestionType.MULTIPLE_CHOICE),
    FILL(1, "填空题", QuestionType.FILL_IN_THE_BLANK),
    READING_COMPREHENSION(2, "阅读理解（阅读+问答选择）", QuestionType.OTHER),
    CLOZE_TEST(3, "完型填空（阅读+选择）", QuestionType.FILL_IN_THE_BLANK),
    READING_FILL(4, "阅读填空（阅读+填空）", QuestionType.FILL_IN_THE_BLANK),
    QANDA(5, "问答题", QuestionType.PROBLEM_SOLVING),
    MULTIPLE_SELECTION(6, "选择题，多选", QuestionType.MULTIPLE_CHOICE),
    MIXED_FILL_CHOICE(7, "填空、选择题混合", QuestionType.OTHER),
    APPLICATION(8, "应用题", QuestionType.APPLICATION),
    TRUE_FALSE(9, "判断题", QuestionType.TRUE_FALSE),
    DRAWING(10, "作图题", QuestionType.OTHER),
    MATERIAL(11, "材料题", QuestionType.OTHER),
    CALCULATION(12, "计算题", QuestionType.CALCULATION),
    MATCHING(13, "连线题", QuestionType.OTHER),
    COMPOSITION(14, "作文题", QuestionType.OTHER),
    SOLUTION(15, "解答题", QuestionType.PROBLEM_SOLVING),
    OTHER(16, "其他", QuestionType.OTHER),
    DIAGRAM(17, "图", QuestionType.OTHER),
    TABLE(18, "表格", QuestionType.OTHER);

    private final int code;
    private final String description;
    private final QuestionType questionType;


    public static AliQuestionType fromCode(int code) {
        for (AliQuestionType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }
}