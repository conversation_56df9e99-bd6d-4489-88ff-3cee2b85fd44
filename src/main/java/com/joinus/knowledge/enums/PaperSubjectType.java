package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PaperSubjectType {

    DEFAULT("default"),
    MATH("Math"),
    PRIMARY_SCHOOL_MATH("PrimarySchool_Math"),
    JUNIOR_HIGH_SCHOOL_MATH("JHighSchool_Math"),
    CHINES<PERSON>("Chinese"),
    PRIMARY_SCHOOL_CHINESE("PrimarySchool_Chinese"),
    JUNIOR_HIGH_SCHOOL_CHINESE("JHighSchool_Chinese"),
    ENGLISH("English"),
    PRIMARY_SCHOOL_ENGLISH("PrimarySchool_English"),
    JUNIOR_HIGH_SCHOOL_ENGLISH("JHighSchool_English");


    @EnumValue
    private final String type;
}
