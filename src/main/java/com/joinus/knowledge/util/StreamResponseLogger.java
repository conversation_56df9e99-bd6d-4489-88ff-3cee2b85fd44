package com.joinus.knowledge.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 流式响应日志记录工具
 * 可用于记录SSE、Flux等流式响应的内容
 */
@Slf4j
public class StreamResponseLogger {

    /**
     * 记录普通Flux流的内容
     * @param originalFlux 原始流
     * @param apiName API名称
     * @param <T> 流中的数据类型
     * @return 装饰后的流
     */
    public static <T> Flux<T> logFlux(Flux<T> originalFlux, String apiName) {
        final long startTime = System.currentTimeMillis();
        final AtomicLong messageCount = new AtomicLong(0);
        final StringBuilder sampleContent = new StringBuilder();
        final int maxSampleLength = 1000; // 样本内容最大长度

        log.info("===== 流式响应开始 - {} =====", apiName);
        
        return originalFlux
                .doOnNext(item -> {
                    long count = messageCount.incrementAndGet();
                    // 记录前10个消息，然后每100个记录一次
                    if (count <= 10 || count % 100 == 0) {
                        log.info("流式响应 - {} - 消息 #{}: {}", apiName, count, item);
                    }
                    
                    // 收集样本内容（限制长度）
                    if (sampleContent.length() < maxSampleLength) {
                        String itemStr = String.valueOf(item);
                        int availableSpace = maxSampleLength - sampleContent.length();
                        if (itemStr.length() <= availableSpace) {
                            sampleContent.append(itemStr);
                        } else {
                            sampleContent.append(itemStr, 0, availableSpace);
                            sampleContent.append("...");
                        }
                    }
                })
                .doOnComplete(() -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.info("===== 流式响应完成 - {} =====", apiName);
                    log.info("总消息数: {}, 耗时: {}ms", messageCount.get(), duration);
                    log.info("响应内容样本: {}", sampleContent.toString());
                })
                .doOnError(error -> {
                    log.error("流式响应发生错误 - " + apiName, error);
                });
    }
    
    /**
     * 记录ServerSentEvent流的内容
     * @param originalFlux 原始SSE流
     * @param apiName API名称
     * @param <T> 流中的数据类型
     * @return 装饰后的流
     */
    public static <T> Flux<ServerSentEvent<T>> logSseFlux(Flux<ServerSentEvent<T>> originalFlux, String apiName) {
        final long startTime = System.currentTimeMillis();
        final AtomicLong messageCount = new AtomicLong(0);
        final StringBuilder sampleContent = new StringBuilder();
        final int maxSampleLength = 1000; // 样本内容最大长度

        log.info("===== SSE流式响应开始 - {} =====", apiName);
        
        return originalFlux
                .doOnNext(event -> {
                    long count = messageCount.incrementAndGet();
                    // 记录前10个消息，然后每100个记录一次
                    if (count <= 10 || count % 100 == 0) {
                        log.info("SSE流式响应 - {} - 消息 #{}: id={}, event={}, data={}", 
                                apiName, count, event.id(), event.event(), event.data());
                    }
                    
                    // 收集样本内容（限制长度）
                    if (sampleContent.length() < maxSampleLength && event.data() != null) {
                        String itemStr = String.valueOf(event.data());
                        int availableSpace = maxSampleLength - sampleContent.length();
                        if (itemStr.length() <= availableSpace) {
                            sampleContent.append(itemStr);
                        } else {
                            sampleContent.append(itemStr, 0, availableSpace);
                            sampleContent.append("...");
                        }
                    }
                })
                .doOnComplete(() -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.info("===== SSE流式响应完成 - {} =====", apiName);
                    log.info("总消息数: {}, 耗时: {}ms", messageCount.get(), duration);
                    log.info("响应内容样本: {}", sampleContent.toString());
                })
                .doOnError(error -> {
                    log.error("SSE流式响应发生错误 - " + apiName, error);
                });
    }
    
    /**
     * SseEmitter装饰器，用于记录SseEmitter发送的数据
     */
    public static class LoggingSseEmitter extends SseEmitter {
        private final String apiName;
        private final AtomicLong messageCount = new AtomicLong(0);
        private final long startTime;
        private final StringBuilder sampleContent = new StringBuilder();
        private final int maxSampleLength = 1000;
        
        public LoggingSseEmitter(String apiName) {
            super();
            this.apiName = apiName;
            this.startTime = System.currentTimeMillis();
            log.info("===== SseEmitter流式响应开始 - {} =====", apiName);
            
            this.onCompletion(() -> {
                long duration = System.currentTimeMillis() - startTime;
                log.info("===== SseEmitter流式响应完成 - {} =====", apiName);
                log.info("总消息数: {}, 耗时: {}ms", messageCount.get(), duration);
                log.info("响应内容样本: {}", sampleContent.toString());
            });
            
            this.onError(error -> {
                log.error("SseEmitter流式响应发生错误 - " + apiName, error);
            });
        }
        
        @Override
        public void send(Object data) throws java.io.IOException {
            logMessage(data);
            super.send(data);
        }
        
        @Override
        public void send(Object data, MediaType mediaType) throws java.io.IOException {
            logMessage(data);
            super.send(data, mediaType);
        }
        
        private void logMessage(Object data) {
            long count = messageCount.incrementAndGet();
            if (count <= 10 || count % 100 == 0) {
                log.info("SseEmitter流式响应 - {} - 消息 #{}: {}", apiName, count, data);
            }
            
            // 收集样本内容（限制长度）
            if (sampleContent.length() < maxSampleLength && data != null) {
                String itemStr = String.valueOf(data);
                int availableSpace = maxSampleLength - sampleContent.length();
                if (itemStr.length() <= availableSpace) {
                    sampleContent.append(itemStr);
                } else {
                    sampleContent.append(itemStr, 0, availableSpace);
                    sampleContent.append("...");
                }
            }
        }
    }
}
