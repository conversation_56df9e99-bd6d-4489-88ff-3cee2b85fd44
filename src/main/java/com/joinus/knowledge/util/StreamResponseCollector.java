package com.joinus.knowledge.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.model.response.StreamResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 流式响应收集器
 * 用于在流式响应的同时收集完整内容，并在流结束时执行回调操作（如存入数据库）
 */
@Slf4j
public class StreamResponseCollector {

    /**
     * 收集普通Flux流的内容，并在流结束时执行回调
     * @param originalFlux 原始流
     * @param apiName API名称（用于日志记录）
     * @param completionCallback 流结束时的回调函数，接收完整收集的内容
     * @param <T> 流中的数据类型
     * @return 装饰后的流
     */
    public static <T> Flux<T> collectFlux(Flux<T> originalFlux, String apiName, Consumer<String> completionCallback) {
        final long startTime = System.currentTimeMillis();
        final AtomicLong messageCount = new AtomicLong(0);
        final StringBuilder fullContent = new StringBuilder();

        log.info("===== 流式响应开始 - {} =====", apiName);
        
        return originalFlux
                .doOnNext(item -> {
                    messageCount.incrementAndGet();
                    // 收集完整内容
                    fullContent.append(item);
                })
                .doOnComplete(() -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.info("===== 流式响应完成 - {} =====", apiName);
                    log.info("总消息数: {}, 耗时: {}ms", messageCount.get(), duration);
                    
                    // 执行回调，传入完整收集的内容
                    try {
                        completionCallback.accept(fullContent.toString());
                        log.info("流式响应内容已成功处理 - {}", apiName);
                    } catch (Exception e) {
                        log.error("处理流式响应内容时发生错误 - " + apiName, e);
                    }
                })
                .doOnError(error -> {
                    log.error("流式响应发生错误 - " + apiName, error);
                });
    }
    
    /**
     * 收集ServerSentEvent流的内容，并在流结束时执行回调
     * @param originalFlux 原始SSE流
     * @param apiName API名称（用于日志记录）
     * @param completionCallback 流结束时的回调函数，接收完整收集的内容
     * @param <T> 流中的数据类型
     * @return 装饰后的流
     */
    public static <T> Flux<ServerSentEvent<T>> collectSseFlux(
            Flux<ServerSentEvent<T>> originalFlux, 
            String apiName, 
            Consumer<String> completionCallback) {
        
        final long startTime = System.currentTimeMillis();
        final AtomicLong messageCount = new AtomicLong(0);
        final StringBuilder fullContent = new StringBuilder();

        log.info("===== SSE流式响应开始 - {} =====", apiName);
        
        return originalFlux
                .doOnNext(event -> {
                    messageCount.incrementAndGet();
                    // 收集完整内容
                    if (event.data() != null) {
                        fullContent.append(event.data());
                    }
                })
                .doOnComplete(() -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.info("===== SSE流式响应完成 - {} =====", apiName);
                    log.info("总消息数: {}, 耗时: {}ms", messageCount.get(), duration);
                    
                    // 执行回调，传入完整收集的内容
                    try {
                        completionCallback.accept(fullContent.toString());
                        log.info("SSE流式响应内容已成功处理 - {}", apiName);
                    } catch (Exception e) {
                        log.error("处理SSE流式响应内容时发生错误 - " + apiName, e);
                    }
                })
                .doOnError(error -> {
                    log.error("SSE流式响应发生错误 - " + apiName, error);
                });
    }
    
    /**
     * 收集普通Flux流的内容，并在流结束时执行异步处理
     * @param originalFlux 原始流
     * @param apiName API名称（用于日志记录）
     * @param asyncProcessor 流结束时的异步处理函数，接收完整收集的内容并返回Mono
     * @param <T> 流中的数据类型
     * @param <R> 异步处理返回的类型
     * @return 装饰后的流
     */
    public static <T, R> Flux<T> collectFluxAsync(
            Flux<T> originalFlux, 
            String apiName, 
            Function<String, Mono<R>> asyncProcessor) {
        
        final long startTime = System.currentTimeMillis();
        final AtomicLong messageCount = new AtomicLong(0);
        final StringBuilder fullContent = new StringBuilder();

        log.info("===== 流式响应开始 - {} =====", apiName);
        
        return originalFlux
                .doOnNext(item -> {
                    messageCount.incrementAndGet();
                    // 收集完整内容
                    fullContent.append(item);
                })
                .doOnComplete(() -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.info("===== 流式响应完成 - {} =====", apiName);
                    log.info("总消息数: {}, 耗时: {}ms", messageCount.get(), duration);
                    
                    // 执行异步处理，传入完整收集的内容
                    asyncProcessor.apply(fullContent.toString())
                        .subscribe(
                            result -> log.info("流式响应内容已成功异步处理 - {}", apiName),
                            error -> log.error("异步处理流式响应内容时发生错误 - " + apiName, error)
                        );
                })
                .doOnError(error -> {
                    log.error("流式响应发生错误 - " + apiName, error);
                });
    }
    
    /**
     * 非阻塞地收集普通Flux流的内容，同时立即返回原始流
     * 此方法不会阻塞原始流的传输，而是在后台异步收集内容并在流结束时执行处理
     * 
     * @param originalFlux 原始流
     * @param apiName API名称（用于日志记录）
     * @param asyncProcessor 流结束时的异步处理函数，接收完整收集的内容并返回Mono
     * @param <T> 流中的数据类型
     * @param <R> 异步处理返回的类型
     * @return 原始流（不会被阻塞）
     */
    public static <T, R> Flux<T> collectFluxNonBlocking(
            Flux<T> originalFlux, 
            String apiName, 
            Function<String, Mono<R>> asyncProcessor) {
        
        final long startTime = System.currentTimeMillis();
        final AtomicLong messageCount = new AtomicLong(0);
        final StringBuilder fullContent = new StringBuilder();

        log.info("===== 非阻塞流式响应开始 - {} =====", apiName);
        
        // 创建一个副本流用于内容收集，不影响原始流的传输
        Flux<T> collectorFlux = originalFlux
            .doOnNext(item -> {
                messageCount.incrementAndGet();
                // 收集完整内容
                fullContent.append(item);
            })
            .doOnComplete(() -> {
                long duration = System.currentTimeMillis() - startTime;
                log.info("===== 非阻塞流式响应完成 - {} =====", apiName);
                log.info("总消息数: {}, 耗时: {}ms", messageCount.get(), duration);
                
                // 在单独的线程中执行异步处理，确保不阻塞主流程
                Mono.fromCallable(() -> fullContent.toString())
                    .flatMap(asyncProcessor)
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe(
                        result -> log.info("流式响应内容已成功异步处理 - {}", apiName),
                        error -> log.error("异步处理流式响应内容时发生错误 - " + apiName, error)
                    );
            })
            .doOnError(error -> {
                log.error("非阻塞流式响应发生错误 - " + apiName, error);
            });
        
        // 订阅副本流进行内容收集，但返回原始流
        collectorFlux.subscribe();
        return originalFlux;
    }

    public static <T, R> Flux<T> collectFluxNonBlockingNew(
            Flux<T> originalFlux,
            String apiName,
            Function<String, Mono<R>> asyncProcessor) {

        final long startTime = System.currentTimeMillis();
        final AtomicLong messageCount = new AtomicLong(0);
        final StringBuilder fullContent = new StringBuilder();

        log.info("===== 非阻塞流式响应开始 - {} =====", apiName);

        // 创建一个副本流用于内容收集，不影响原始流的传输
        Flux<T> collectorFlux = originalFlux
                .doOnNext(item -> {
                    messageCount.incrementAndGet();
                    // 收集完整内容
                    if (!"[DONE]".equals(item)) {
                        StreamResponse response = JSONUtil.toBean(item.toString(), StreamResponse.class);
                        if (StrUtil.isNotBlank(response.getReasoningContent())) {
                            fullContent.append(response.getReasoningContent());
                        } else {
                            fullContent.append(response.getContent());
                        }
                    }
                })
                .doOnComplete(() -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.info("===== 非阻塞流式响应完成 - {} =====", apiName);
                    log.info("总消息数: {}, 耗时: {}ms", messageCount.get(), duration);

                    // 在单独的线程中执行异步处理，确保不阻塞主流程
                    Mono.fromCallable(() -> fullContent.toString())
                            .flatMap(asyncProcessor)
                            .subscribeOn(Schedulers.boundedElastic())
                            .subscribe(
                                    result -> log.info("流式响应内容已成功异步处理 - {}", apiName),
                                    error -> log.error("异步处理流式响应内容时发生错误 - " + apiName, error)
                            );
                })
                .doOnError(error -> {
                    log.error("非阻塞流式响应发生错误 - " + apiName, error);
                });

        // 订阅副本流进行内容收集，但返回原始流
        collectorFlux.subscribe();
        return originalFlux;
    }

    public static <T, R> Flux<T> collectFluxNonBlockingNoReasoningContent(
            Flux<T> originalFlux,
            String apiName,
            Function<String, Mono<R>> asyncProcessor) {

        final long startTime = System.currentTimeMillis();
        final AtomicLong messageCount = new AtomicLong(0);
        final StringBuilder fullContent = new StringBuilder();

        log.info("===== 非阻塞流式响应开始 - {} =====", apiName);

        // 创建一个副本流用于内容收集，不影响原始流的传输
        Flux<T> collectorFlux = originalFlux
                .doOnNext(item -> {
                    messageCount.incrementAndGet();
                    // 收集完整内容
                    if (!"[DONE]".equals(item)) {
                        StreamResponse response = JSONUtil.toBean(item.toString(), StreamResponse.class);
                        if (StrUtil.isNotBlank(response.getContent())) {
                            fullContent.append(response.getContent());
                        }
                    }
                })
                .doOnComplete(() -> {
                    long duration = System.currentTimeMillis() - startTime;
                    log.info("===== 非阻塞流式响应完成 - {} =====", apiName);
                    log.info("总消息数: {}, 耗时: {}ms", messageCount.get(), duration);

                    // 在单独的线程中执行异步处理，确保不阻塞主流程
                    Mono.fromCallable(() -> fullContent.toString())
                            .flatMap(asyncProcessor)
                            .subscribeOn(Schedulers.boundedElastic())
                            .subscribe(
                                    result -> log.info("流式响应内容已成功异步处理 - {}", apiName),
                                    error -> log.error("异步处理流式响应内容时发生错误 - " + apiName, error)
                            );
                })
                .doOnError(error -> {
                    log.error("非阻塞流式响应发生错误 - " + apiName, error);
                });

        // 订阅副本流进行内容收集，但返回原始流
        collectorFlux.subscribe();
        return originalFlux;
    }
}
