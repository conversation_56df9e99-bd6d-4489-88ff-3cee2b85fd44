<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学专项训练</title>
    <style>
        body { 
            font-family: 'WenQuanYi Micro Hei', 'Noto Sans CJK SC';
            line-height: 1.6; 
            padding: 20px; 
            max-width: 1000px;
            margin: 0 auto;
        }
        .exam-title { 
            text-align: center; 
            font-size: 24px; 
            margin-bottom: 30px; 
            font-weight: bold;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin: 30px 0 15px 0;
            padding: 8px;
            background-color: #f0f0f0;
            border-left: 4px solid #3498db;
        }
        .question-container { 
            margin-bottom: 30px; 
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        .question-header { 
            font-weight: bold; 
            margin-bottom: 10px; 
            background-color: #f5f5f5;
            padding: 8px;
            border-radius: 4px;
        }
        .question-content { 
            margin-left: 20px; 
            margin-bottom: 15px; 
        }
        .answer-section {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px dashed #ccc;
        }
        .answer-result {
            color: #d35400;
        }
        img { 
            max-width: 100%; 
            vertical-align: middle; 
        }
        /* LaTeX公式样式，使其自动适应中文文字大小 */
        .latex-formula {
            height: 1.2em !important; /* 强制使用em单位，与中文字体大小关联 */
            vertical-align: middle !important;
            display: inline-block !important;
            margin: 0 0.15em !important;
        }
        /* 行内LaTeX公式的处理 */
        p .latex-formula, span .latex-formula {
            font-size: 1em; /* 适应当前文字大小 */
        }
        /* 标题中的LaTeX公式处理 */
        h1 .latex-formula, h2 .latex-formula, h3 .latex-formula, h4 .latex-formula {
            font-size: inherit; /* 继承标题的字体大小 */
        }
        /* 打印样式 */
        @media print {
            .question-container {
                page-break-inside: avoid;
            }
            /* 移除强制分页设置，改为更智能的分页控制 */
            .section-title {
                page-break-inside: avoid; /* 避免章节标题内部分页 */
            }
            /* 只有后续章节才考虑分页 */
            .section-title:not(:first-of-type) {
                page-break-before: auto; /* 允许自动分页决策，而不是强制分页 */
            }
            .exam-title {
                page-break-after: avoid; /* 确保标题和第一个章节标题在同一页 */
            }
            body {
                font-size: 12pt;
            }
        }
    </style>
</head>
<body>
    <div class="exam-title"><!-- EXAM_TITLE --></div>
    
    <!-- QUESTIONS_CONTENT -->
    
</body>
</html>
