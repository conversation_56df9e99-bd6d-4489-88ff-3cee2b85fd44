<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学专项训练演示</title>
    <style>
        body {
            font-family: 'WenQuanYi Micro Hei', 'Noto Sans CJK SC';
            line-height: 1.6;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button, select {
            padding: 8px;
            font-size: 16px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            padding: 10px 15px;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 4px;
            min-height: 200px;
        }
        .preview-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin-top: 20px;
            border-radius: 4px;
        }
        .preview-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
        }
        .print-btn {
            background-color: #2196F3;
            margin-top: 10px;
        }
        .options-container {
            display: flex;
            gap: 15px;
            align-items: center;
        }
    </style>
</head>
<body>
    <h1>数学专项训练 HTML 生成器</h1>
    
    <div class="container">
        <div class="form-group">
            <label for="examId">试卷ID：</label>
            <input type="text" id="examId" placeholder="输入试卷ID">
            
            <div class="options-container">
                <label for="htmlType">选择版本：</label>
                <select id="htmlType">
                    <option value="questions">只有题目</option>
                    <option value="question-answers">题目带答案</option>
                    <option value="question-answers/separate">题目和答案分离</option>
                    <option value="answers">只有答案</option>
                </select>
                <button id="generateBtn">生成 HTML</button>
            </div>
        </div>
        
        <div id="result">
            <p>请输入试卷ID并点击"生成 HTML"按钮</p>
        </div>
        
        <div class="preview-container" style="display: none;" id="previewContainer">
            <div class="preview-title">预览：</div>
            <iframe id="previewFrame"></iframe>
            <button class="print-btn" id="printBtn">打印预览</button>
        </div>
    </div>

    <script>
        document.getElementById('generateBtn').addEventListener('click', function() {
            const examId = document.getElementById('examId').value.trim();
            const htmlType = document.getElementById('htmlType').value;
            
            if (!examId) {
                alert('请输入有效的试卷ID');
                return;
            }
            
            // 显示加载中
            document.getElementById('result').innerHTML = '<p>正在生成 HTML...</p>';
            
            // 发送请求
            fetch(`/api/edu-knowledge-hub/api/math-training/html/${htmlType}?examId=${examId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.text();
                })
                .then(html => {
                    // 显示 HTML 代码
                    document.getElementById('result').innerHTML = '<pre>' + escapeHtml(html) + '</pre>';
                    
                    // 显示预览
                    const previewContainer = document.getElementById('previewContainer');
                    previewContainer.style.display = 'block';
                    
                    // 将 HTML 内容加载到 iframe 中
                    const previewFrame = document.getElementById('previewFrame');
                    const frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
                    frameDoc.open();
                    frameDoc.write(html);
                    frameDoc.close();
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = '<p>错误: ' + error.message + '</p>';
                });
        });
        
        document.getElementById('printBtn').addEventListener('click', function() {
            const previewFrame = document.getElementById('previewFrame');
            previewFrame.contentWindow.print();
        });
        
        // 转义 HTML 以便在 <pre> 标签中显示
        function escapeHtml(html) {
            return html
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }
    </script>
</body>
</html>
