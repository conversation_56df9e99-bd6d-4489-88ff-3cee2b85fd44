<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学专项训练</title>
    <style>
        body {
            font-family: 'WenQuanYi Micro Hei', 'Noto Sans CJK SC';
            margin: 40px;
            line-height: 1.6;
            text-align: left; /* 左对齐 */
        }
        .exam-title {
            text-align: center; /* 保留标题居中 */
            font-size: 24px;
            margin-bottom: 30px;
            font-weight: bold;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-top: 30px;
            margin-bottom: 20px;
            padding-bottom: 5px;
            text-align: left; /* 左对齐 */
        }
        .question-container {
            margin-bottom: 30px; 
            padding-bottom: 20px;
            text-align: left; /* 左对齐 */
        }
        .question-header { 
            font-weight: bold; 
            margin-bottom: 10px; 
            background-color: #f5f5f5;
            padding: 8px;
            border-radius: 4px;
            text-align: left; /* 左对齐 */
            display: flex; /* 使用Flex布局 */
            align-items: flex-start; /* 顶部对齐 */
        }
        /* 题号部分 */
        .question-number {
            display: inline-block;
            min-width: 40px; /* 固定最小宽度 */
            text-align: right; /* 数字右对齐 */
            margin-right: 10px; /* 与题目内容间距 */
            flex-shrink: 0; /* 防止收缩 */
        }
        .question-content { 
            margin-left: 0; /* 移除左边距 */
            margin-bottom: 15px; 
            text-align: left; /* 左对齐 */
            flex: 1; /* 占据剩余空间 */
        }
        .answers-page {
            margin-top: 80px; /* 增加更多空间 */
            padding-top: 40px;
            border-top: 3px dashed #aaa;
            page-break-before: always !important; /* 确保答案始终在新页开始 */
            text-align: left; /* 左对齐 */
        }
        .answers-title {
            text-align: center; /* 保留标题居中 */
            font-size: 24px;
            margin-bottom: 30px;
            font-weight: bold;
        }
        .answer-item {
            margin-bottom: 15px;
            text-align: left; /* 左对齐 */
        }
        .answer-number {
            font-weight: bold;
            margin-right: 10px;
            display: inline-block;
            min-width: 40px; /* 为题号提供一致的宽度 */
            vertical-align: top;
            text-align: left; /* 左对齐 */
        }
        .answer-content {
            display: inline-block;
            vertical-align: top;
            text-align: left; /* 左对齐 */
        }
        img { 
            max-width: 100%; 
            height: auto; 
        }
        .latex-formula {
            height: 1.2em !important; /* 强制使用em单位，与中文字体大小关联 */
            vertical-align: middle !important;
            display: inline-block !important;
            margin: 0 0.15em !important;
        }
        p .latex-formula, span .latex-formula {
            font-size: 1em; /* 适应当前文字大小 */
        }
        h1 .latex-formula, h2 .latex-formula, h3 .latex-formula, h4 .latex-formula {
            font-size: inherit; /* 继承标题的字体大小 */
        }
        .question {
            margin-bottom: 20px;
            text-align: left; /* 左对齐 */
        }
        .question-number {
            font-weight: bold;
            margin-bottom: 5px;
            text-align: left; /* 左对齐 */
        }
        .answer {
            margin-bottom: 20px;
            text-align: left; /* 左对齐 */
        }
        .answer-result {
            margin-bottom: 5px;
            text-align: left; /* 左对齐 */
        }
        .answer-explanation {
            color: #666;
            font-size: 0.95em;
            text-align: left; /* 左对齐 */
        }
        .question-images {
            text-align: left; /* 左对齐 */
            margin: 10px 0; /* 修改为上下边距 */
        }
        .question-image-container {
            display: inline-block;
            margin: 5px 0; /* 修改为上下边距 */
            text-align: left; /* 左对齐 */
        }
        /* 打印样式 */
        @media print {
            .question-container {
                page-break-inside: avoid;
            }
            /* 移除强制分页设置 */
            .section-title {
                page-break-inside: avoid; /* 避免章节标题内部分页 */
            }
            .section-title:not(:first-of-type) {
                page-break-before: auto; /* 允许自动分页决策 */
            }
            .exam-title {
                page-break-after: avoid; /* 确保标题和第一个章节标题在同一页 */
            }
            /* 答案部分强制新页开始 */
            .answers-page {
                page-break-before: always !important; /* 强制答案部分总是新页开始 */
                page-break-inside: avoid; /* 尽量避免答案内部被分页 */
            }
            .answers-title {
                page-break-after: avoid; /* 确保答案标题和第一个答案在同一页 */
            }
            body {
                font-size: 12pt;
            }
        }
    </style>
</head>
<body>
    <div class="exam-title"><!-- EXAM_TITLE --></div>
    
    <!-- QUESTIONS_CONTENT -->
    
    <div class="answers-page">
        <div class="answers-title">参考答案</div>
        <!-- ANSWERS_CONTENT -->
    </div>
</body>
</html>
