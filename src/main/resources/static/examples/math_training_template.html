<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学专项训练</title>
    <style>
        body { 
            font-family: 'WenQuanYi Micro Hei', 'Noto Sans CJK SC';
            line-height: 1.6; 
            padding: 20px; 
            max-width: 1000px;
            margin: 0 auto;
        }
        .exam-title { 
            text-align: center; 
            font-size: 24px; 
            margin-bottom: 30px; 
            font-weight: bold;
        }
        .question-container, .answer-item { 
            margin-bottom: 30px; 
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
            clear: both; /* 添加清除浮动 */
            overflow: hidden; /* 确保内容不会溢出 */
            display: block; /* 确保为块级元素 */
            position: relative; /* 添加相对定位 */
        }
        .question-header { 
            font-weight: bold; 
            margin-bottom: 10px; 
            background-color: #f5f5f5;
            padding: 8px;
            border-radius: 4px;
            display: flex; /* 使用Flex布局 */
            align-items: flex-start; /* 顶部对齐 */
            text-align: left; /* 确保左对齐 */
        }
        /* 题号部分 */
        .question-number {
            display: inline-block;
            min-width: 60px; /* 增加固定最小宽度，确保所有题号都有相同的空间 */
            width: 60px; /* 添加固定宽度 */
            text-align: left; /* 改为左对齐，确保所有题号位置一致 */
            margin-right: 10px; /* 与题目内容间距 */
            flex-shrink: 0; /* 防止收缩 */
            position: relative; /* 添加相对定位 */
        }
        /* 题型标签样式 */
        .question-type-label {
            display: inline-block;
            margin-right: 10px;
            flex-shrink: 0;
        }
        .question-content { 
            margin-bottom: 15px; 
            margin-left: 0; /* 移除左边距 */
            flex: 1; /* 占据剩余空间 */
            text-align: left !important; /* 确保左对齐 */
            padding-left: 0; /* 确保没有额外的左内边距 */
        }
        .question-content, .answer-content {
            font-size: 16px; 
            line-height: 1.8;
            margin-bottom: 15px;
            text-align: left !important; /* 确保左对齐 */
            padding-left: 0; /* 确保没有额外的左内边距 */
        }
        img { 
            max-width: 100%; 
            vertical-align: middle; 
        }
        svg {
            vertical-align: middle; /* 垂直对齐方式 */
            margin: 0 2px; /* 左右边距 */
        }
        /* 包含LaTeX公式的段落样式 */
        p:has(svg), div:has(svg) {
            line-height: 2.2 !important; /* 使用更大的行高以容纳公式 */
            margin-bottom: 10px;
        }
        /* 答案部分样式 */
        .answer-section {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px dashed #ddd;
        }
        .answer-result, .answer-explanation {
            margin-bottom: 10px;
            text-align: left !important;
        }
        /* 不可见的分隔元素，用于确保题目之间的布局正确 */
        .question-separator {
            clear: both;
            display: block;
            height: 15px;
            width: 100%;
            margin-top: 10px;
            visibility: hidden;
        }
        /* 标准化题目和答案内容的字体大小 */
        .question-content, .answer-content {
            font-size: 16px;
            line-height: 1.8;
            text-align: left !important;
            padding-left: 0;
        }
        /* 确保每个题目都有明确的边界 */
        .question-container:after, .answer-item:after {
            content: "";
            display: table;
            clear: both;
            margin-top: 15px;
            border-bottom: 1px solid transparent;
        }
        /* 题目分隔元素 - 不可见但强制清除浮动 */
        .question-separator {
            display: block;
            clear: both;
            height: 20px; /* 提供足够的空间分隔 */
            width: 100%;
            visibility: hidden; /* 使其不可见 */
            overflow: hidden;
            margin: 0;
            padding: 0;
            border: none;
        }
        /* 保证内容依然左对齐 */
        .question-content p,
        .answer-content p,
        .answer-result p,
        .answer-explanation p {
            text-align: left !important; /* 强制段落左对齐 */
            margin-left: 0 !important; /* 移除左边距 */
        }
        /* 打印样式 */
        @media print {
            .question-container, .answer-item {
                page-break-inside: avoid;
            }
            body {
                font-size: 12pt;
            }
        }
    </style>
</head>
<body>
    <div class="exam-title"><!-- EXAM_TITLE --></div>
    
    <!-- QUESTIONS_CONTENT -->
    
</body>
</html>
