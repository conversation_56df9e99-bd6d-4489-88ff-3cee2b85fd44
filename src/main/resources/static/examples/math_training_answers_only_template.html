<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学专项训练 - 答案</title>
    <style>
        body {
            font-family: 'WenQuanYi Micro Hei', 'Noto Sans CJK SC';
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }
        .exam-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            margin-top: 20px;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        .answer-item {
            margin-bottom: 10px;
            display: block;
            clear: both; /* 添加清除浮动 */
            overflow: hidden; /* 确保内容不会溢出 */
            position: relative; /* 添加相对定位 */
        }
        .question-header { 
            font-weight: bold; 
            margin-bottom: 10px; 
            background-color: #f5f5f5;
            padding: 8px;
            border-radius: 4px;
            display: flex; /* 使用Flex布局 */
            align-items: flex-start; /* 顶部对齐 */
            text-align: left; /* 确保左对齐 */
        }
        .question-number {
            display: inline-block;
            min-width: 60px; /* 与问题模板一致 */
            width: 60px; /* 添加固定宽度 */
            text-align: left; /* 与问题模板一致，改为左对齐 */
            margin-right: 10px;
            flex-shrink: 0; /* 防止收缩 */
            position: relative; /* 添加相对定位 */
        }
        .question-content {
            flex: 1;
            text-align: left !important;
            margin-left: 0 !important;
            padding-left: 0; /* 确保没有额外的左内边距 */
        }
        /* 答案部分样式 */
        .answer-result, .answer-explanation {
            margin-bottom: 10px;
            text-align: left !important;
        }
        /* 保证内容依然左对齐 */
        .question-content p,
        .answer-content p,
        .answer-result p,
        .answer-explanation p {
            text-align: left !important; /* 强制段落左对齐 */
            margin-left: 0 !important; /* 移除左边距 */
        }
        /* LaTeX公式样式 */
        .question-content img,
        .question-content svg,
        .question-content [class*="latex-formula"] {
            display: block; /* 块级显示 */
            margin: 1em auto; /* 上下边距与水平居中 */
            max-width: 100%; /* 最大宽度 */
            clear: both; /* 添加清除浮动 */
        }
        /* 确保每个题目都有明确的边界 */
        .answer-item:after {
            content: "";
            display: table;
            clear: both;
            margin-top: 15px;
            border-bottom: 1px solid transparent;
        }
        
        /* 题目分隔元素 - 不可见但强制清除浮动 */
        .question-separator {
            display: block;
            clear: both;
            height: 20px; /* 提供足够的空间分隔 */
            width: 100%;
            visibility: hidden; /* 使其不可见 */
            overflow: hidden;
            margin: 0;
            padding: 0;
            border: none;
        }
        @media print {
            /* 移除所有强制分页 */
            body {
                font-size: 12pt;
            }
            .section-title {
                page-break-inside: avoid; /* 避免章节标题内部分页 */
            }
            .answer-item {
                page-break-inside: avoid; /* 避免答案项内部分页 */
            }
        }
    </style>
</head>
<body>
    <div class="exam-title"><!-- EXAM_TITLE --></div>
    
    <!-- ANSWERS_CONTENT -->
    
</body>
</html>
