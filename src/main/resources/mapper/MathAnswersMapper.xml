<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathAnswersMapper">

    <resultMap id="mathAnswersVO" type="com.joinus.knowledge.model.vo.MathAnswerVO">
        <id property="id" column="id"/>
        <result property="content" column="content"/>
        <result property="answer" column="answer"/>
        <result property="createdAt" column="created_at"/>
        <collection property="files" ofType="com.joinus.knowledge.model.vo.FileVO">
            <id property="id" column="file_id"/>
            <result property="ossBucket" column="oss_bucket"/>
            <result property="ossType" column="oss_type"/>
            <result property="ossKey" column="oss_url"/>
            <result property="type" column="type"/>
            <result property="sortNo" column="sort_no"/>
        </collection>
    </resultMap>

    <select id="listAnswersByQuestionId" resultMap="mathAnswersVO">
        select mqa.*,
               maf.type,
               maf.sort_no,
               f.id as file_id,
               f.oss_bucket,
               f.oss_type,
               f.oss_url
        from math_question_answers t
                 inner join math_answers mqa on t.answer_id = mqa.id
                 left join math_answer_files maf on mqa.id = maf.answer_id
                 left join files f on maf.file_id = f.id and f.deleted_at is null
        where mqa.deleted_at is null
          and t.question_id = #{id}
          and (maf.file_id is null or f.id is not null)
    </select>
    
    <!-- 自定义逻辑删除方法，明确设置updated_at为当前时间 -->
    <update id="logicalDeleteById">
        UPDATE math_answers 
        SET updated_at = now(), deleted_at = now() 
        WHERE id = #{id} AND deleted_at IS NULL
    </update>
</mapper>
