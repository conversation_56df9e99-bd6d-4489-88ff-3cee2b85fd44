<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathLabelMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathLabel">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="type" column="type" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,type
    </sql>
</mapper>
