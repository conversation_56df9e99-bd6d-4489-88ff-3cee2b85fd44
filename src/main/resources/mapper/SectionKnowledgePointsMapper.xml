<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.SectionKnowledgePointsMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.SectionKnowledgePoint">
            <id property="sectionId" column="section_id" />
            <id property="knowledgePointId" column="knowledge_point_id" />
    </resultMap>

    <sql id="Base_Column_List">
        section_id,knowledge_point_id
    </sql>
</mapper>
