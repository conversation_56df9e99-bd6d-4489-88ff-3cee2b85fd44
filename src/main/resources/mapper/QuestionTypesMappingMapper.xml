<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.QuestionTypesMappingMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.QuestionTypesMapping">
            <id property="questionId" column="question_id" />
            <id property="questionTypeId" column="question_type_id" />
    </resultMap>

    <sql id="Base_Column_List">
        question_id,question_type_id
    </sql>
    
    <!-- 插入题目和题型关联 -->
    <insert id="insertQuestionTypesMapping">
        INSERT INTO math_question_type_questions (question_id, question_type_id)
        VALUES (#{questionId}, #{questionTypeId})
    </insert>
    
    <!-- 删除题目和题型关联 -->
    <delete id="deleteByQuestionIdAndQuestionTypeId">
        DELETE FROM math_question_type_questions
        WHERE question_id = #{questionId} AND question_type_id = #{questionTypeId}
    </delete>
    
    <!-- 根据题目ID删除所有关联 -->
    <delete id="deleteByQuestionId">
        DELETE FROM math_question_type_questions
        WHERE question_id = #{questionId}
    </delete>
    
    <!-- 根据题型ID删除所有关联 -->
    <delete id="deleteByQuestionTypeId">
        DELETE FROM math_question_type_questions
        WHERE question_type_id = #{questionTypeId}
    </delete>
    
    <!-- 根据题目ID查询所有关联 -->
    <select id="selectByQuestionId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM math_question_type_questions
        WHERE question_id = #{questionId}
    </select>
    
    <!-- 根据题型ID查询所有关联 -->
    <select id="selectByQuestionTypeId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM math_question_type_questions qtm
        inner join math_questions mq on qtm.question_id = mq.id
        WHERE qtm.question_type_id = #{questionTypeId}
    </select>
    <select id="listQuestionTypesMappingDTOByQuestionId"
            resultType="com.joinus.knowledge.model.dto.QuestionTypesMappingDTO">
        select mq.id as question_id,vmqt.textbook_name, vmqt.textbook_id,vmqt.question_type_id,vmqt.section_id,vmqt.question_type_name,vms.start_page,vms.end_page,vms.page_offset from math_questions mq
        inner join math_question_type_questions  qtm on mq.id = qtm.question_id
        inner join view_math_question_types vmqt on qtm.question_type_id = vmqt.question_type_id
        inner join view_math_sections vms on vmqt.section_id = vms.section_id
        where mq.id = #{questionId}
    </select>

</mapper>
