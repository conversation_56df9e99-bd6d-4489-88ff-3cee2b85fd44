<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathExamsMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathExam">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="semester" column="semester" />
            <result property="grade" column="grade" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,semester,grade,created_at,updated_at,
        deleted_at
    </sql>

    <resultMap id="QuestionDetailVOMap" type="com.joinus.knowledge.model.vo.QuestionDetailVO">
        <id property="id" column="question_id"/>
        <result property="content" column="content"/>
        <result property="questionType" column="question_type"/>
        <result property="difficulty" column="difficulty"/>

        <result property="source" column="source"/>
        <result property="sortNo" column="question_sort_no"/>
        <!-- 答案集合 -->
        <collection property="answers" ofType="com.joinus.knowledge.model.vo.QuestionAnswerDetailVO">
            <id property="id" column="answer_id"/>
            <result property="answer" column="answer"/>
            <result property="content" column="answer_content"/>
        </collection>
        <!-- 文件集合 -->
        <collection property="files" ofType="com.joinus.knowledge.model.vo.FileVO">
            <id property="id" column="file_id"/>
            <result property="ossKey" column="oss_url"/>
            <result property="sortNo" column="file_sort_no"/>
            <result property="type" column="file_type"/>
            <result property="ossType" column="oss_type"/>
            <result property="ossBucket" column="oss_bucket"/>
        </collection>
    </resultMap>

    <select id="listQuestionByExamId" resultType="com.joinus.knowledge.model.po.ExamQuestionPO">
            WITH ranked_answers AS (
                SELECT
                    t.id AS question_id,
                    t.content,
                    t.question_type,
                    t.difficulty,
                    mqa.id AS answer_id,
                    mqa.answer,
                    mqa.content AS answer_content,
                    f.id AS file_id,
                    f.oss_url,
                    f.oss_type,
                    f.oss_bucket,
                    ROW_NUMBER() OVER (
          PARTITION BY t.id
          ORDER BY mqa.created_at DESC
        ) AS row_num
                FROM math_questions t
                         INNER JOIN math_question_files qf ON t.id = qf.question_id
                         INNER JOIN files f ON qf.file_id = f.id
                         INNER JOIN math_question_answers qar ON t.id = qar.question_id
                         INNER JOIN math_answers mqa ON qar.answer_id = mqa.id
                WHERE
            )

        SELECT
            question_id,
            content,
            question_type,
            difficulty,
            answer_id,
            answer,
            answer_content,
            file_id,
            oss_url,
            oss_type,
            oss_bucket
        FROM ranked_answers
        WHERE row_num = 1
        ORDER BY created_at DESC;
--         select meq.sort_no,
--                mq.content,
--                mq.question_type
--         from math_exams t
--         inner join math_exam_questions meq on t.id = meq.exam_id
--         inner join math_questions mq on meq.question_id = mq.id
--         where t.id = #{examId}
    </select>

    <!-- 试卷分页条件查询 -->
    <select id="pageQuery" resultType="com.joinus.knowledge.model.vo.MathExamVO">
        SELECT
            e.id,
            e.name,
            e.state,
            e.publisher,
            e.grade,
            e.semester,
            e.created_at AS createdAt,
            e.updated_at AS updatedAt,
            e.year,
            e.source,
            e.region_path,
            e.region
        FROM math_exams e
        WHERE e.deleted_at IS NULL
        <if test="param.name != null and param.name != ''">
            AND (
            e.name LIKE CONCAT('%', #{param.name}, '%')
                or exists(
                select 1 from math_exam_tags met
                where met.exam_id = e.id
                and met.deleted_at is null
                and met.type = 'ALIAS'
                and met.value like CONCAT('%', #{param.name}, '%')
                )
            )
        </if>
        <if test="param.state != null">
            AND e.state = #{param.state}
        </if>
        <if test="param.publisher != null">
            AND e.publisher = #{param.publisher}
        </if>
        <if test="param.grade != null">
            AND e.grade = #{param.grade}
        </if>
        <if test="param.semester != null">
            AND e.semester = #{param.semester}
        </if>
        <if test="param.year != null">
            AND e.year = #{param.year}
        </if>
        <if test="param.source != null">
            AND e.source = #{param.source}
        </if>
        <if test="param.regionPath != null and param.regionPath != ''">
            AND e.region_path ~ ('*.' || #{param.regionPath} || '.*')::lquery
        </if>
        <if test="param.id != null">
            AND e.id = #{param.id}
        </if>
        <if test="param.regularExamType != null">
            AND exists(
            select 1 from math_exam_tags met
            where met.exam_id = e.id
            and met.deleted_at is null
            and met.type = 'REGULAR_EXAM_TYPE'
            and met.value = #{param.regularExamType.value}
            )
        </if>
        <if test="param.examIds != null and param.examIds.size() > 0">
            AND e.id in
            <foreach item="item" collection="param.examIds" separator="," close=")" index="index" open="(">
                #{item}
            </foreach>
        </if>
        ORDER BY e.updated_at DESC
    </select>

    <!-- 根据试卷ID查询试题详情 -->
    <select id="listMathExamQuestionsByExamId" resultMap="QuestionDetailVOMap">
        SELECT 
            mq.id AS question_id,
            mq.content,
            mq.question_type,
            mq.difficulty,
            mq.source,
            meq.sort_no as question_sort_no,
            mqa.id AS answer_id,
            mqa.answer,
            mqa.content AS answer_content,
            f.id AS file_id,
            f.oss_url,
            f.oss_type,
            f.oss_bucket,
            qf.sort_no as file_sort_no,
            qf.type as file_type
        FROM math_exams me
        INNER JOIN math_exam_questions meq ON me.id = meq.exam_id
        INNER JOIN math_questions mq ON meq.question_id = mq.id and mq.deleted_at IS NULL
        LEFT JOIN math_question_answers qar ON mq.id = qar.question_id
        LEFT JOIN math_answers mqa ON qar.answer_id = mqa.id and mqa.deleted_at IS NULL
        LEFT JOIN math_question_files qf ON mq.id = qf.question_id
        LEFT JOIN files f ON qf.file_id = f.id and f.deleted_at IS NULL
        WHERE me.id = #{id}
        ORDER BY meq.sort_no, qf.sort_no
    </select>

    <select id="getQuestionDetailById" resultMap="QuestionDetailVOMap">
        SELECT
             mq.id AS question_id,
            mq.content,
            mq.question_type,
            mq.difficulty,
            mq.source,
            mqa.id AS answer_id,
            mqa.answer,
            mqa.content AS answer_content,
            meq.sort_no as question_sort_no,
            f.id AS file_id,
            f.oss_url,
            f.oss_type,
            f.oss_bucket,
            qf.sort_no as file_sort_no,
            qf.type as file_type
        from math_questions mq
            inner join math_exam_questions meq ON mq.id = meq.question_id
            LEFT JOIN math_question_answers qar ON mq.id = qar.question_id
            LEFT JOIN math_answers mqa ON qar.answer_id = mqa.id
            LEFT JOIN math_question_files qf ON mq.id = qf.question_id
            LEFT JOIN files f ON qf.file_id = f.id
        WHERE mq.id = #{questionId}
          and meq.exam_id = #{examId}
        ORDER BY qf.sort_no
    </select>
</mapper>
