<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathExamQuestionsMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathExamQuestion">
            <id property="examId" column="exam_id" />
            <id property="questionId" column="question_id" />
            <result property="sortNo" column="sort_no" />
    </resultMap>

    <sql id="Base_Column_List">
        exam_id,question_id,sort_no
    </sql>
    <select id="listQuestionsByExamId" resultType="com.joinus.knowledge.model.entity.MathQuestion"
            parameterType="java.util.UUID">
        select mq.* from math_questions mq, math_exam_questions meq, math_exams me
        where mq.id = meq.question_id
          and meq.exam_id = me.id
          and meq.exam_id = #{examId}
          and mq.question_type != '未知类型'
          order by mq.created_at
    </select>
</mapper>
