<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.AiChatLogMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.AiChatLog">
            <id property="id" column="id" />
            <result property="params" column="params" />
            <result property="result" column="result" />
            <result property="createdAt" column="created_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,params,result,created_at
    </sql>
</mapper>
