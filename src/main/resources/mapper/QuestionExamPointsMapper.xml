<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.QuestionExamPointsMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.QuestionExamPoints">
            <id property="questionId" column="question_id" />
            <id property="examPointId" column="exam_point_id" />
    </resultMap>

    <sql id="Base_Column_List">
        question_id,exam_point_id
    </sql>
    
    <insert id="insertQuestionExamPoints">
        INSERT INTO question_exam_points (question_id, exam_point_id) 
        VALUES (#{questionId}, #{examPointId})
    </insert>
    
    <delete id="deleteByQuestionIdAndExamPointId">
        DELETE FROM question_exam_points
        WHERE question_id = #{questionId} AND exam_point_id = #{examPointId}
    </delete>
    
    <delete id="deleteByQuestionId">
        DELETE FROM question_exam_points
        WHERE question_id = #{questionId}
    </delete>
    
    <delete id="deleteByExamPointId">
        DELETE FROM question_exam_points
        WHERE exam_point_id = #{examPointId}
    </delete>
    
    <select id="selectByQuestionId" resultType="com.joinus.knowledge.model.entity.QuestionExamPoints">
        SELECT question_id, exam_point_id
        FROM question_exam_points
        WHERE question_id = #{questionId}
    </select>
    
    <select id="selectByExamPointId" resultType="com.joinus.knowledge.model.entity.QuestionExamPoints">
        SELECT question_id, exam_point_id
        FROM question_exam_points
        WHERE exam_point_id = #{examPointId}
    </select>
</mapper>
