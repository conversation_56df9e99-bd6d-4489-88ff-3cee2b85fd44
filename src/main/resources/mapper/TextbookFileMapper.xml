<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.TextbookFileMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.TextbookFile">
            <id property="textbookId" column="textbook_id" />
            <id property="pageNo" column="page_no" />
            <result property="fileId" column="file_id" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
    </resultMap>

    <sql id="Base_Column_List">
        textbook_id,file_id,page_no,created_at,updated_at,deleted_at
    </sql>
</mapper>
