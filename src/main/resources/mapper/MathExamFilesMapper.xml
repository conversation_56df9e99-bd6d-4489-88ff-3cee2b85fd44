<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathExamFilesMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathExamFiles">
            <id property="examId" column="exam_id" />
            <id property="fileId" column="file_id" />
            <result property="sortNo" column="sort_no" />
    </resultMap>

    <sql id="Base_Column_List">
        exam_id,file_id,sort_no
    </sql>

    <select id="listFilesByExamId" resultType="com.joinus.knowledge.model.vo.FileVO">
        select t.id,
               t.oss_url as oss_key,
               t.oss_bucket,
               t.oss_type,
               t.ocr_html,
               mef.sort_no,
               mef.type as exam_file_type
        from files t
             inner join math_exam_files mef on t.id = mef.file_id
        where t.deleted_at is null
          and mef.exam_id = #{examId}
          order by mef.sort_no
    </select>
</mapper>
