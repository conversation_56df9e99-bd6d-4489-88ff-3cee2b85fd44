<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.KnowledgePointQuestionTypeRelationshipMapper">


    <select id="listByKnowledgePointIds"
            resultType="com.joinus.knowledge.model.entity.KnowledgePointQuestionTypeRelationship">
        select t.*
        from math_knowledge_point_question_types t
        inner join math_question_types mqt on mqt.id = t.question_type_id
        where mqt.deleted_at is null
            and t.knowledge_point_id in
        <foreach collection="knowledgePointIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
