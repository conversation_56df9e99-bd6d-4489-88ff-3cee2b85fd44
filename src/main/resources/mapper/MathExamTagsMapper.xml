<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathExamTagsMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathExamTag">
            <result property="examId" column="exam_id" />
            <result property="type" column="type" />
            <result property="value" column="value" />
            <result property="properties" column="properties" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
    </resultMap>

    <sql id="Base_Column_List">
        exam_id,type,value,properties,created_at,updated_at,
        deleted_at
    </sql>

    <select id="checkExistByAlias" resultType="com.joinus.knowledge.model.entity.MathExam">
        select me.* from math_exam_tags met
                          inner join math_exams me on met.exam_id = me.id
        where me.deleted_at is null
          and met.deleted_at is null
          and met.type = 'ALIAS'
          and met.value = #{examName}
          and me.source in ('用户上传', '中考真题', '常规考试卷')
        order by me.created_at asc
        limit 1
    </select>
</mapper>
