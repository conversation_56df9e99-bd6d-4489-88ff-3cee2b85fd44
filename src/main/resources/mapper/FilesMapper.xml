<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.FilesMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.File">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="type" column="type" />
            <result property="mimeType" column="mime_type" />
            <result property="ossUrl" column="oss_url" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,type,mime_type,oss_url,created_at,
        updated_at,deleted_at
    </sql>

    <select id="getFilesByBookId" resultType="map" >
        select f.id,tf.page_no,f.oss_url,f.ocr_html from math_textbooks t, math_textbook_files tf, files f
        where t.id = tf.textbook_id and tf.file_id = f.id and t.id = #{bookId}
        order by tf.page_no
    </select>

    <select id="listFilesByBookId" resultType="com.joinus.knowledge.model.dto.TextbookFileDTO" >

        select f.id as fileId,
               f.oss_url as pngOssKey,
               tf.page_no,
               f.ocr_html
        from math_textbooks t
                 inner join math_textbook_files tf on t.id = tf.textbook_id
                 inner join files f on tf.file_id = f.id
        where t.id = #{bookId}
    </select>

    <select id="listQuestionOriginImages" resultType="com.joinus.knowledge.model.entity.File">
        select f.* from math_question_files qf, files f
        where qf.file_id = f.id
          and qf.type = 1
          and qf.question_id = #{questionId}
        order by sort_no
    </select>
    <select id="getSectionHtml" resultType="java.lang.String">
        select string_agg(f.ocr_html , e'\n' order by tf.page_no) from math_textbooks t
        inner join math_textbook_files tf on t.id = tf.textbook_id
        inner join files f on tf.file_id = f.id
        where t.id = #{textbookId}
          and tf.page_no between #{startPage} and #{endPage}
    </select>
</mapper>
