<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.QuestionLabelMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.QuestionLabel">
            <id property="questionId" column="question_id" />
            <id property="labelId" column="label_id" />
            <result property="labelType" column="label_type" />
    </resultMap>

    <sql id="Base_Column_List">
        question_id,label_id,label_type
    </sql>
</mapper>
