<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.SectionQuestionTypesMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.SectionQuestionType">
            <id property="sectionId" column="section_id" />
            <id property="questionTypeId" column="question_type_id" />
    </resultMap>

    <sql id="Base_Column_List">
        section_id,question_type_id
    </sql>
</mapper>
