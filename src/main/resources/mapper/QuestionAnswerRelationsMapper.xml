<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.QuestionAnswerRelationsMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.QuestionAnswerRelation">
            <id property="questionId" column="question_id" />
            <id property="answerId" column="answer_id" />
    </resultMap>

    <sql id="Base_Column_List">
        question_id,answer_id
    </sql>
</mapper>
