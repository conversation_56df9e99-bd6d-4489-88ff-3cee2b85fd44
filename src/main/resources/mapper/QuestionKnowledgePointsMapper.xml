<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.QuestionKnowledgePointsMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.QuestionKnowledgePoint">
            <id property="questionId" column="question_id" />
            <id property="knowledgePointId" column="knowledge_point_id" />
    </resultMap>

    <sql id="Base_Column_List">
        question_id,knowledge_point_id
    </sql>
    
    <!-- 插入题目和知识点关联 -->
    <insert id="insertQuestionKnowledgePoints">
        INSERT INTO math_knowledge_point_questions (question_id, knowledge_point_id)
        VALUES (#{questionId}, #{knowledgePointId})
    </insert>
    
    <!-- 删除题目和知识点关联 -->
    <delete id="deleteByQuestionIdAndKnowledgePointId">
        DELETE FROM math_knowledge_point_questions
        WHERE question_id = #{questionId} AND knowledge_point_id = #{knowledgePointId}
    </delete>
    
    <!-- 根据题目ID删除所有关联 -->
    <delete id="deleteByQuestionId">
        DELETE FROM math_knowledge_point_questions
        WHERE question_id = #{questionId}
    </delete>
    
    <!-- 根据知识点ID删除所有关联 -->
    <delete id="deleteByKnowledgePointId">
        DELETE FROM math_knowledge_point_questions
        WHERE knowledge_point_id = #{knowledgePointId}
    </delete>
    
    <!-- 根据题目ID查询所有关联 -->
    <select id="selectByQuestionId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM math_knowledge_point_questions
        WHERE question_id = #{questionId}
    </select>
    
    <!-- 根据知识点ID查询所有关联 -->
    <select id="selectByKnowledgePointId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM math_knowledge_point_questions qkp
        inner join math_questions mq on mq.id = qkp.question_id and mq.deleted_at is null
        WHERE qkp.knowledge_point_id = #{knowledgePointId}
    </select>

    <select id="listKnowledgePointByQuestionId" resultType="com.joinus.knowledge.model.entity.MathKnowledgePoint">
        select distinct mkp.*
        from math_knowledge_points mkp
           inner join math_knowledge_point_questions qkp on mkp.id = qkp.knowledge_point_id
           inner join math_section_knowledge_points skp on mkp.id = skp.knowledge_point_id
           inner join math_sections ms on skp.section_id = ms.id and ms.deleted_at is null
           inner join math_chapters mc on mc.id = ms.chapter_id and mc.deleted_at is null
           inner join math_textbooks te on mc.textbook_id = te.id and te.deleted_at is null
        where mkp.id = qkp.knowledge_point_id
            and qkp.question_id = #{questionId}
          <if test="null != publisher">
              and te.publisher = #{publisher}
          </if>
          <if test="null != grade">
              and te.grade = #{grade}
          </if>
          <if test="null != semester">
              and te.semester = #{semester}
          </if>
    </select>

    <select id="getSimilarQuestion" resultType="java.util.UUID" parameterType="java.util.UUID">
        select
            qkp.question_id,
            case
                when mq.review_status = 'APPROVED_SECOND_REVIEW' then 2
                when mq.review_status = 'APPROVED_FIRST_REVIEW' then 1
                else 0 end as sortNo,
            count(*) as common_count
        from math_knowledge_point_questions qkp
                 inner join math_knowledge_point_questions qkp2
                            on qkp.knowledge_point_id = qkp2.knowledge_point_id
                 inner join math_questions mq
                            on qkp.question_id = mq.id
                 inner join math_questions mq2
                            on qkp2.question_id = mq2.id
        where qkp.question_id != #{questionId}
              and qkp2.question_id = #{questionId}
              and mq.source = 'AI'
              and mq.enabled = true
              and mq.review_status in ('APPROVED_SECOND_REVIEW', 'APPROVED_FIRST_REVIEW')
        group by
            qkp.question_id,
            case
            when mq.review_status = 'APPROVED_SECOND_REVIEW' then 2
            when mq.review_status = 'APPROVED_FIRST_REVIEW' then 1
            else 0 end
        order by
            sortNo desc,
            common_count desc,
            qkp.question_id
        limit 1
    </select>
    <select id="listKnowledgePointDTOByQuestionId" resultType="com.joinus.knowledge.model.dto.QuestionKnowledgePointDTO">
        select mq.id as question_id,vmkp.textbook_id,vmkp.textbook_name,vmkp.knowledge_point_id,vmkp.knowledge_point_name,vmkp.section_id,vms.start_page,vms.end_page,vms.page_offset from math_questions mq
        inner join math_knowledge_point_questions qkp on mq.id = qkp.question_id
        inner join view_math_knowledge_points vmkp on qkp.knowledge_point_id = vmkp.knowledge_point_id
        inner join view_math_sections vms on vmkp.section_id = vms.section_id
        where mq.id = #{questionId}
    </select>

</mapper>
