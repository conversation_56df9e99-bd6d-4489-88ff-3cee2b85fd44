<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.FileDerivativeMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.FileDerivative">
            <id property="id" column="id" />
            <result property="fileId" column="file_id" />
            <result property="derivativeType" column="derivative_type" />
            <result property="storagePath" column="storage_path" />
            <result property="format" column="format" />
            <result property="width" column="width" />
            <result property="height" column="height" />
            <result property="fileSize" column="file_size" />
            <result property="textContent" column="text_content" />
    </resultMap>

    <sql id="Base_Column_List">
        id,file_id,derivative_type,storage_path,format,width,
        height,file_size,text_content
    </sql>

    <select id="listByTextbookId" resultType="com.joinus.knowledge.model.dto.DerivativeFileDTO">
        select f.id as fileId,
               fd.derivative_type,
               fd.storage_path as ossKey,
               fd.width,
               fd.height
        from math_textbooks t
                 inner join math_textbook_files tf on t.id = tf.textbook_id
                 inner join files f on tf.file_id = f.id
                 inner join file_derivatives fd on f.id = fd.file_id
        where t.id = #{bookId}
    </select>
</mapper>
