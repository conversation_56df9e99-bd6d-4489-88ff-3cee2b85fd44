<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathKnowledgePointsMapper">

    <resultMap id="knowledgePointAndQuestionTypes" type="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
    </resultMap>

    <select id="listByGradeAndSemester" resultType="com.joinus.knowledge.model.entity.MathKnowledgePoint">
        select vmkp.knowledge_point_id as id, vmkp.knowledge_point_name as name
        from view_math_knowledge_points vmkp
        where vmkp.exam_point = false
        <if test="grade != null and grade != ''">
            and vmkp.grade &lt;= #{grade}
        </if>
        <if test="publisher != null">
            and vmkp.publisher = #{publisher.value}
        </if>
    </select>
    <select id="listByQuestionId" resultType="com.joinus.knowledge.model.entity.MathKnowledgePoint">
        select mkp.* from math_knowledge_points mkp, math_knowledge_point_questions qkp
        where mkp.id = qkp.knowledge_point_id
          and qkp.question_id = #{questionId}
          and mkp.deleted_at is null
          and mkp.exam_point = false
    </select>
    <select id="listByPublisher" resultType="com.joinus.knowledge.model.entity.MathKnowledgePoint">
        select distinct mkp.* from math_knowledge_points mkp
                                       inner join view_math_knowledge_points v on mkp.id = v.knowledge_point_id
        where v.publisher = #{publisher}
          and te.subject= '数学'
    </select>

    <select id="listQuestionTypeByKnowledgePointIds"
            resultMap="knowledgePointAndQuestionTypes">
        select mkp.id,
               mkp.name,
               mqt.id as question_type_id,
               mqt.name as question_type_name
        from
            math_knowledge_points mkp
                left join math_knowledge_point_question_types kqtr on mkp.id = kqtr.knowledge_point_id
                left join math_question_types mqt on mqt.id = kqtr.question_type_id and mqt.deleted_at is null
        where mkp.deleted_at is null
        and mkp.exam_point = false
        and mkp.id in
        <foreach collection="knowledgePointIds" item="knowledgePointId" open="(" separator="," close=")">
            #{knowledgePointId}
        </foreach>
    </select>

    <select id="listSectionKnowledgePointByKnowledgeIds"
            resultType="com.joinus.knowledge.model.vo.SectionKnowledgePointVO">
        select v.chapter_id as chapterId,
            v.chapter_name as chapterName,
            v.chapter_sort_no as chapterSortNo,
            v.section_id as sectionId,
            v.section_name as sectionName,
            v.section_sort_no as sectionSortNo,
            t.id as knowledgePointId,
            t.name as knowledgePointName,
            t.id as textbookId,
            v.grade,
            v.semester,
            v.publisher
        from math_knowledge_points t
        inner join view_math_knowledge_points v on t.id = v.knowledge_point_id
        where t.deleted_at is null
          and t.exam_point = false
          and t.id in
        <foreach collection="knowledgePointIds" item="knowledgePointId" open="(" separator="," close=")">
            #{knowledgePointId}
        </foreach>
        <if test="grade != null">
            and v.grade = #{grade}
        </if>
        <if test="semester != null">
            and v.semester = #{semester}
        </if>
        <if test="publisher != null">
            and v.publisher = #{publisher.value}
        </if>
    </select>

    <select id="list" resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select distinct mkp.id,
                        mkp.name,
                        v.grade,
                        v.semester,
                        v.publisher
        from math_knowledge_points mkp
        inner join view_math_knowledge_points v on mkp.id = v.knowledge_point_id
        where mkp.deleted_at is null
            <if test="name != null and name != ''">
                and mkp.name like concat('%', #{name}, '%')
            </if>
            <if test="grade != null">
                and v.grade = #{grade}
            </if>
            <if test="semester != null">
                and v.semester = #{semester}
            </if>
            <if test="publisher != null">
              and v.publisher = #{publisher}
            </if>
            <if test="chapterId != null">
                and v.chapter_id = #{chapterId}
            </if>
            <if test="chapterName != null and chapterName != ''">
                and v.chapter_name = #{chapterName}
            </if>
            <if test="sectionId != null">
                and v.section_id = #{sectionId}
            </if>
            <if test="sectionName != null and sectionName != ''">
                and v.section_name = #{sectionName}
            </if>
    </select>

    <select id="listByTextbookId" resultType="com.joinus.knowledge.model.entity.MathKnowledgePoint">
        select mkp.* from math_knowledge_points mkp
          inner join view_math_knowledge_points v on mkp.id = v.knowledge_point_id
        where v.textbook_id = #{textbookId}
    </select>

    <select id="listByQuestionIds" resultType="com.joinus.knowledge.model.po.MathKnowledgePointPO">
        select t.knowledge_point_id as id,
               vmkp.knowledge_point_name as name,
               vmkp.grade,
               vmkp.semester,
               vmkp.publisher,
               vmkp.chapter_id,
               vmkp.chapter_name,
               vmkp.chapter_sort_no,
               vmkp.section_id,
               vmkp.section_name,
               vmkp.section_sort_no,
               vmkp.exam_point,
               t.question_id
        from math_knowledge_point_questions t
            inner join view_math_knowledge_points vmkp on t.knowledge_point_id = vmkp.knowledge_point_id
            inner join math_knowledge_points mkp on t.knowledge_point_id = mkp.id
        where t.question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </select>

    <select id="listByQuestionIdsAndPublisher" resultType="com.joinus.knowledge.model.po.MathKnowledgePointPO">
        select t.knowledge_point_id as id,
        vmkp.knowledge_point_name as name,
        vmkp.grade,
        vmkp.semester,
        vmkp.publisher,
        vmkp.chapter_id,
        vmkp.chapter_name,
        vmkp.chapter_sort_no,
        vmkp.section_id,
        vmkp.section_name,
        vmkp.section_sort_no,
        t.question_id
        from math_knowledge_point_questions t
        inner join view_math_knowledge_points vmkp on t.knowledge_point_id = vmkp.knowledge_point_id
        where vmkp.exam_point = false
        and t.question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        <if test="null != publisher">
            and vmkp.publisher = #{publisher.value}
        </if>
    </select>

    <!-- 历史的题目有关联考点的（通过题型与考点关联），为了避免查询历史试卷缺题（考点不显示，对应的题目也就不显示），所以兼容查询考点 -->
    <select id="listByQuestionIdsAndPublisherAndExamPoint" resultType="com.joinus.knowledge.model.po.MathKnowledgePointPO">
        select t.knowledge_point_id as id,
        vmkp.knowledge_point_name as name,
        vmkp.grade,
        vmkp.semester,
        vmkp.publisher,
        vmkp.chapter_id,
        vmkp.chapter_name,
        vmkp.chapter_sort_no,
        vmkp.section_id,
        vmkp.section_name,
        vmkp.section_sort_no,
        t.question_id
        from math_knowledge_point_questions t
        inner join view_math_knowledge_points vmkp on t.knowledge_point_id = vmkp.knowledge_point_id
        where t.question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        <if test="null != publisher">
            and vmkp.publisher = #{publisher.value}
        </if>
    </select>

    <select id="listBySectionIds" resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select v.knowledge_point_id as id,
            v.knowledge_point_name as name,
            v.textbook_id as textbookId,
            v.publisher,
            v.grade,
            v.semester,
            v.chapter_id as chapterId,
            v.chapter_name as chapterName,
            v.chapter_sort_no as chapterSortNo,
            v.section_id as sectionId,
            v.section_name as sectionName,
            v.section_sort_no as  sectionSortNo,
            skp.page_index
        from view_math_knowledge_points v
        inner join math_section_knowledge_points skp on v.section_id = skp.section_id and v.knowledge_point_id = skp.knowledge_point_id
        where v.exam_point = false
            and v.section_id in
            <foreach collection="sectionIds" item="sectionId" open="(" separator="," close=")">
                #{sectionId}
            </foreach>
        order by v.knowledge_point_id, v.chapter_sort_no, v.section_sort_no, skp.page_index
    </select>

    <select id="listByIds" resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select v.knowledge_point_id as id,
            v.knowledge_point_name as name,
            v.textbook_id as textbookId,
            v.publisher,
            v.grade,
            v.semester,
            v.chapter_id as chapterId,
            v.chapter_name as chapterName,
            v.chapter_sort_no as chapterSortNo,
            v.section_id as sectionId,
            v.section_name as sectionName,
            v.section_sort_no as  sectionSortNo,
            skp.page_index
        from view_math_knowledge_points v
        inner join math_section_knowledge_points skp on v.section_id = skp.section_id and v.knowledge_point_id = skp.knowledge_point_id
        where v.exam_point = false
            and v.knowledge_point_id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        order by v.knowledge_point_id, v.chapter_sort_no, v.section_sort_no, skp.page_index
    </select>

    <select id="listKnowledgePointByQuestionId"
            resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select mkp.*,
            vmkp.grade,
            vmkp.semester,
            vmkp.publisher
               from math_questions t
                              inner join math_knowledge_point_questions kpq on t.id = kpq.question_id
                              inner join math_knowledge_points mkp on kpq.knowledge_point_id = mkp.id
                              inner join view_math_knowledge_points vmkp on mkp.id = vmkp.knowledge_point_id
        where t.deleted_at is null
          and t.id = #{questionId}
          and mkp.exam_point = false
          <if test="grade != null">
            and vmkp.grade = #{grade}
          </if>
          <if test="semester != null">
              and vmkp.semester = #{semester}
          </if>
          <if test="publisher != null">
              and vmkp.publisher = #{publisher.value}
          </if>
        union
        select mkp.*,
            vmkp.grade,
            vmkp.semester,
            vmkp.publisher
        from math_questions t
                 inner join math_question_type_questions  qtq on t.id = qtq.question_id
                 inner join math_knowledge_point_question_types kpqt on qtq.question_type_id = kpqt.question_type_id
                 inner join math_knowledge_points mkp on kpqt.knowledge_point_id = mkp.id and mkp.deleted_at is null
                 inner join view_math_knowledge_points vmkp on mkp.id  = vmkp.knowledge_point_id
        where t.deleted_at is null
          and t.id = #{questionId}
          and mkp.exam_point = false
            <if test="grade != null">
                and vmkp.grade = #{grade}
            </if>
            <if test="semester != null">
                and vmkp.semester = #{semester}
            </if>
            <if test="publisher != null">
                and vmkp.publisher = #{publisher.value}
            </if>
    </select>

    <select id="listByIdsAndPublisher" resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select v.knowledge_point_id as id,
        v.knowledge_point_name as name,
        v.textbook_id as textbookId,
        v.publisher,
        v.grade,
        v.semester,
        v.chapter_id as chapterId,
        v.chapter_name as chapterName,
        v.chapter_sort_no as chapterSortNo,
        v.section_id as sectionId,
        v.section_name as sectionName,
        v.section_sort_no as  sectionSortNo,
        skp.page_index
        from view_math_knowledge_points v
        inner join math_section_knowledge_points skp on v.section_id = skp.section_id and v.knowledge_point_id = skp.knowledge_point_id
        where v.exam_point = false
            and v.knowledge_point_id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="publisher != null">
                and v.publisher = #{publisher.value}
            </if>
            <if test="grade != null">
                and v.grade = #{grade}
            </if>
            <if test="semester != null">
                and v.semester = #{semester}
            </if>
        order by v.knowledge_point_id, v.chapter_sort_no, v.section_sort_no, skp.page_index
    </select>

    <select id="listEnableAiQuestionCountByKnowledgePointIds"
            resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select mkp.id,
               mkp.name,
               count(distinct mq.id) as enableAiQuestionCount
        from
            math_knowledge_points mkp
                left join math_knowledge_point_questions  kpq on mkp.id = kpq.knowledge_point_id
                left join math_questions mq on kpq.question_id = mq.id and mq.deleted_at is null and mq.enabled = true and mq.source = 'AI'
        where mkp.deleted_at is null
            and mkp.id in
        <foreach collection="knowledgePointIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by mkp.id, mkp.name
    </select>




</mapper>
