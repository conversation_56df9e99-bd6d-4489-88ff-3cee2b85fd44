<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.QuestionFileMapper">

    <select id="listByQuestionId" resultType="com.joinus.knowledge.model.dto.QuestionFileDTO">
        select f.id as fileId,
               f.oss_url
        from math_question_files qf
                 inner join files f on qf.file_id = f.id
        where qf.question_id = #{questionId}
    </select>
</mapper>
