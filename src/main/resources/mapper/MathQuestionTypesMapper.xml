<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathQuestionTypesMapper">
    <select id="listByTextbookId" resultType="com.joinus.knowledge.model.entity.MathQuestionType">
        select mqt.* from math_question_types mqt,math_section_question_types sqt,math_sections ms, math_chapters mc
        where mqt.id = sqt.question_type_id
          and sqt.section_id = ms.id
          and ms.chapter_id = mc.id
          and mc.textbook_id = #{textbookId}
    </select>

    <select id="listByQuestionId" resultType="com.joinus.knowledge.model.entity.MathQuestionType">
        select t.* from math_question_types t
                            inner join math_question_type_questions qtm on t.id = qtm.question_type_id
        where  t.deleted_at is null
          and qtm.question_id = #{id}
    </select>
    
    <select id="listByQuestionIds" resultType="com.joinus.knowledge.model.po.MathQuestionTypePO">

        select t.question_type_id      as id,
            vmqt.question_type_name as name,
            vmqt.grade,
            vmqt.semester,
            vmqt.publisher,
            vmqt.chapter_id,
            vmqt.chapter_name,
            vmqt.chapter_sort_no,
            vmqt.section_id,
            vmqt.section_name,
            vmqt.section_sort_no,
            t.question_id
        from math_question_type_questions t
        inner join view_math_question_types vmqt on t.question_type_id = vmqt.question_type_id
        where t.question_id in
              <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
                #{questionId}
              </foreach>
        union
        select mqt.id,
            mqt.name,
            null as grade,
            null as semester,
            null as publisher,
            null as chapter_id,
            null as chapter_name,
            null as chapter_sort_no,
            null as section_id,
            null as section_name,
            null as section_sort_no,
            t.question_id
        from math_question_type_questions t
            inner join math_question_types mqt on t.question_type_id = mqt.id
        where mqt.deleted_at is null
            and not exists (select 1 from math_section_question_types sqt
                inner join math_sections ms on sqt.section_id = ms.id and ms.deleted_at is null
                inner join math_chapters mc on ms.chapter_id = mc.id and mc.deleted_at is null
                inner join math_textbooks te on mc.textbook_id = te.id and te.deleted_at is null
                where sqt.question_type_id = mqt.id)
            and t.question_id in
            <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
                #{questionId}
            </foreach>
    </select>

    <select id="listByQuestionIdsAndPublisher" resultType="com.joinus.knowledge.model.po.MathQuestionTypePO">
        select t.question_type_id      as id,
        vmqt.question_type_name as name,
        vmqt.grade,
        vmqt.semester,
        vmqt.publisher,
        vmqt.chapter_id,
        vmqt.chapter_name,
        vmqt.chapter_sort_no,
        vmqt.section_id,
        vmqt.section_name,
        vmqt.section_sort_no,
        t.question_id
        from math_question_type_questions t
        inner join view_math_question_types vmqt on t.question_type_id = vmqt.question_type_id
        where t.question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        <if test="null != publisher">
            and vmqt.publisher = #{publisher.value}
        </if>
    </select>

    <select id="list" resultType="com.joinus.knowledge.model.vo.MathQuestionTypeVO">
        select distinct mqt.id,
            mqt.name,
            te.grade,
            te.semester,
            te.publisher
        from math_question_types mqt
        inner join math_section_question_types sqt on mqt.id = sqt.question_type_id
        inner join math_sections ms on sqt.section_id = ms.id and ms.deleted_at is null
        inner join math_chapters mc on mc.id = ms.chapter_id and mc.deleted_at is null
        inner join math_textbooks te on mc.textbook_id = te.id and te.deleted_at is null
        where mqt.deleted_at is null
        <if test="name != null and name != ''">
            and mqt.name like concat('%', #{name}, '%')
        </if>
        <if test="grade != null">
            and te.grade = #{grade}
        </if>
        <if test="semester != null">
            and te.semester = #{semester}
        </if>
        <if test="publisher != null">
            and te.publisher = #{publisher}
        </if>
        <if test="chapterId != null">
            and mc.id = #{chapterId}
        </if>
        <if test="chapterName != null and chapterName != ''">
            and mc.name = #{chapterName}
        </if>
        <if test="sectionId != null">
            and ms.id = #{sectionId}
        </if>
        <if test="sectionName != null and sectionName != ''">
            and ms.name = #{sectionName}
        </if>
    </select>

    <select id="listByIds" resultType="com.joinus.knowledge.model.vo.MathQuestionTypeVO">
        select mqt.id as id,
        mqt.name as name,
        te.id as textbookId,
        te.publisher,
        te.grade,
        te.semester,
        mc.id as chapterId,
        mc.name as chapterName,
        mc.sort_no as chapterSortNo,
        ms.id as sectionId,
        ms.name as sectionName,
        ms.sort_no as  sectionSortNo,
        sqt.page_index
        from math_question_types mqt
        inner join math_section_question_types sqt on mqt.id = sqt.question_type_id
        inner join math_sections ms on sqt.section_id = ms.id and ms.deleted_at is null
        inner join math_chapters mc on mc.id = ms.chapter_id and mc.deleted_at is null
        inner join math_textbooks te on mc.textbook_id = te.id and te.deleted_at is null
        where mqt.deleted_at is null
             and mqt.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        order by te.id, mc.sort_no, ms.sort_no, sqt.page_index
    </select>

    <select id="listByIdsAndPublisher" resultType="com.joinus.knowledge.model.vo.MathQuestionTypeVO">
        select mqt.id as id,
        mqt.name as name,
        te.id as textbookId,
        te.publisher,
        te.grade,
        te.semester,
        mc.id as chapterId,
        mc.name as chapterName,
        mc.sort_no as chapterSortNo,
        ms.id as sectionId,
        ms.name as sectionName,
        ms.sort_no as  sectionSortNo,
        sqt.page_index
        from math_question_types mqt
        inner join math_section_question_types sqt on mqt.id = sqt.question_type_id
        inner join math_sections ms on sqt.section_id = ms.id and ms.deleted_at is null
        inner join math_chapters mc on mc.id = ms.chapter_id and mc.deleted_at is null
        inner join math_textbooks te on mc.textbook_id = te.id and te.deleted_at is null
        where mqt.deleted_at is null
        and mqt.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="publisher != null">
            and te.publisher = #{publisher.value}
        </if>
        <if test="grade != null">
            and te.grade = #{grade}
        </if>
        <if test="semester != null">
            and te.semester = #{semester}
        </if>
        order by te.id, mc.sort_no, ms.sort_no, sqt.page_index
    </select>

    <select id="listEnableAiQuestionCountByKnowledgePointIds"
            resultType="com.joinus.knowledge.model.vo.MathQuestionTypeVO">
        select mqt.id,
               mqt.name,
               count(distinct mq.id) as enableAiQuestionCount
        from
            math_knowledge_points mkp
                inner join math_knowledge_point_question_types kpqt on mkp.id = kpqt.knowledge_point_id
                inner join math_question_types mqt on kpqt.question_type_id = mqt.id and mqt.deleted_at is null
                left join math_question_type_questions qtq on kpqt.question_type_id = qtq.question_type_id
                left join math_questions mq on qtq.question_id = mq.id and mq.deleted_at is null and mq.enabled = true and mq.source = 'AI'
        where mkp.deleted_at is null
        and mkp.id in
        <foreach collection="knowledgePointIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by mqt.id, mqt.name
    </select>

    <select id="listBySectionIds" resultType="com.joinus.knowledge.model.vo.MathQuestionTypeVO">
        select mqt.id as id,
        mqt.name as name,
        t.id as textbookId,
        t.publisher,
        t.grade,
        t.semester,
        mc.id as chapterId,
        mc.name as chapterName,
        mc.sort_no as chapterSortNo,
        ms.id as sectionId,
        ms.name as sectionName,
        ms.sort_no as  sectionSortNo,
        sqt.page_index
        from math_textbooks t
        inner join math_chapters mc on t.id = mc.textbook_id and mc.deleted_at is null
        inner join math_sections ms on mc.id = ms.chapter_id and ms.deleted_at is null
        inner join math_section_question_types sqt on ms.id = sqt.section_id
        inner join math_question_types mqt on sqt.question_type_id = mqt.id and mqt.deleted_at is null
        where ms.id in
        <foreach collection="sectionIds" item="sectionId" open="(" separator="," close=")">
            #{sectionId}
        </foreach>
        order by t.id, mc.sort_no, ms.sort_no, sqt.page_index
    </select>
</mapper>
