<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathSectionMapper">

    <select id="getByBookId" resultType="com.joinus.knowledge.model.entity.MathSection">
        select * from math_sections s, math_chapters c
        where s.chapter_id = c.id
          and c.textbook_id = #{bookId}
    </select>

    <select id="getByPageNo" resultType="com.joinus.knowledge.model.entity.MathSection">
        select ms.*
        from math_textbooks t
        inner join math_chapters mc on t.id = mc.textbook_id
        inner join math_sections ms on ms.chapter_id = mc.id
        where t.id = #{textbookId}
          <if test="sectionId != null">
              and ms.id = #{sectionId}
          </if>
        and ms.start_page &lt;= #{pageNo} - t.page_offset
        and ms.end_page >= #{pageNo} - t.page_offset
    </select>

    <select id="listAllSectionsByBookId" resultType="com.joinus.knowledge.model.vo.SectionVO">
        select t.id,
               t.name,
               mc.id as chapter_id,
               mc.name as chapter_name,
               ms.id as section_id,
               ms.name as section_name,
               ms.start_page,
               ms.end_page
        from math_textbooks t
                 inner join math_chapters mc on t.id = mc.textbook_id and mc.deleted_at is null
                 inner join math_sections ms on ms.chapter_id = mc.id and ms.deleted_at is null
        where t.deleted_at is null
          and t.id = #{bookId}
        order by t.id,mc.sort_no, ms.sort_no
    </select>

    <select id="listKeypointsById" resultType="com.joinus.knowledge.model.vo.SectionKeypointVO">
        select *
        from (
                 select
                        ms.id            as section_id,
                        ms.name          as section_name,
                        'knowledgePoint' as type,
                        mkp.id           as key_point_id,
                        mkp.name         as key_point_name,
                        skp.page_index   as page_index,
                        COALESCE(count(distinct mq.id), 0) as question_count
                 from  math_sections ms
                          inner join math_section_knowledge_points skp on ms.id = skp.section_id
                          inner join math_knowledge_points mkp on skp.knowledge_point_id = mkp.id
                          left join math_knowledge_point_questions qkp on mkp.id = qkp.knowledge_point_id
                          left join math_questions mq on qkp.question_id = mq.id
                 where ms.id = #{sectionId}
                   and ms.deleted_at is null
                   and mkp.deleted_at is null
                   and mq.deleted_at is null
                 group by ms.id,
                          mkp.id,
                          skp.page_index
                 union
                 select
                        ms.id          as section_id,
                        ms.name        as section_name,
                        'questionType' as type,
                        mqt.id         as key_point_id,
                        mqt.name       as key_point_name,
                        skp.page_index as page_index,
                        COALESCE(count(distinct mq.id),0) as question_count
                 from  math_sections ms
                          inner join math_section_question_types skp on ms.id = skp.section_id
                          inner join math_question_types mqt on skp.question_type_id = mqt.id
                          left join math_question_type_questions qkp on mqt.id = qkp.question_type_id
                          left join math_questions mq on qkp.question_id = mq.id
                 where ms.id = #{sectionId}
                   and ms.deleted_at is null
                   and mqt.deleted_at is null
                   and mq.deleted_at is null
                 group by ms.id,
                          mqt.id,
                          skp.page_index
             )
        order by page_index,key_point_id
    </select>

    <select id="listDuplicateKnowledgePoints"
            resultType="com.joinus.knowledge.model.po.SectionKnowledgePointPO">
        with knowledge_base as (
            select t.knowledge_point_id from math_section_knowledge_points t
                                                 inner join math_sections ms on t.section_id = ms.id
                                                 inner join math_knowledge_points mkp on t.knowledge_point_id = mkp.id
            where ms.deleted_at is null and mkp.deleted_at is null and t.remain = true
            group by t.knowledge_point_id
            having count(t.section_id) > 1
        )

        select mkp.*,
               t.publisher,
               ms.id as sectionId,
               skp.page_index
        from math_textbooks t
                 inner join math_chapters mc on t.id = mc.textbook_id and mc.deleted_at is null
                 inner join math_sections ms on mc.id = ms.chapter_id and ms.deleted_at is null
                 inner join math_section_knowledge_points skp on ms.id = skp.section_id
                 inner join math_knowledge_points mkp on skp.knowledge_point_id = mkp.id
        where mkp.id in (select knowledge_point_id from knowledge_base)

    </select>

    <select id="listBaseQuestionByKnowledgePointIds"
            resultType="com.joinus.knowledge.model.entity.QuestionKnowledgePoint">
        select qkp.* from math_knowledge_points t
                              inner join math_knowledge_point_questions qkp on t.id = qkp.knowledge_point_id
                              inner join math_questions q on qkp.question_id = q.id
        where t.deleted_at is null and q.deleted_at is null and q.source = 'BOOK'
          and t.id in
        <foreach item="item" index="index" collection="kpIds" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="listDuplicateQuestionTypes"
            resultType="com.joinus.knowledge.model.po.SectionQuestionTypePO">
        with question_type_base as (
            select t.question_type_id from math_section_question_types t
                                                 inner join math_sections ms on t.section_id = ms.id
                                                 inner join math_question_types mqt on t.question_type_id = mqt.id
            where ms.deleted_at is null and mqt.deleted_at is null and t.remain = true
            group by t.question_type_id
            having count(t.section_id) > 1
        )

        select mqt.*,
               t.publisher,
               ms.id as sectionId,
               sqt.page_index
        from math_textbooks t
                 inner join math_chapters mc on t.id = mc.textbook_id and mc.deleted_at is null
                 inner join math_sections ms on mc.id = ms.chapter_id and ms.deleted_at is null
                 inner join math_section_question_types sqt on ms.id = sqt.section_id
                 inner join math_question_types mqt on sqt.question_type_id = mqt.id
        where mqt.id in (select question_type_id from question_type_base)
    </select>

    <select id="listBaseQuestionByQuestionTypeIds"
            resultType="com.joinus.knowledge.model.entity.QuestionTypesMapping">
        select qtm.* from math_question_types t
                              inner join math_question_type_questions qtm on t.id = qtm.question_type_id
                              inner join math_questions q on qtm.question_id = q.id
        where t.deleted_at is null and q.deleted_at is null and q.source = 'BOOK'
          and t.id in
        <foreach item="item" index="index" collection="qtIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="list" resultType="com.joinus.knowledge.model.vo.MathSectionVO">
        select ms.id,
            ms.name
        from math_sections ms
        inner join math_chapters mc on ms.chapter_id = mc.id and mc.deleted_at is null
        inner join math_textbooks te on mc.textbook_id = te.id and te.deleted_at is null
        where mc.deleted_at is null
        <if test="name != null and name != ''">
            and mc.name like concat('%', #{name}, '%')
        </if>
        <if test="grade != null">
            and te.grade = #{grade}
        </if>
        <if test="semester != null">
            and te.semester = #{semester}
        </if>
        <if test="publisher != null">
            and te.publisher = #{publisher.value}
        </if>
        <if test="chapterName != null and chapterName != ''">
            and mc.name = #{chapterName}
        </if>
        <if test="chapterId != null">
            and mc.id = #{chapterId}
        </if>
    </select>

    <select id="listAllsectionsByPublisher" resultType="com.joinus.knowledge.model.vo.SectionVO">
        select t.id as textbookId,
               t.publisher,
               t.grade,
               t.semester,
               mc.id as chapterId,
               mc.name as chapterName,
               mc.sort_no as chapterSortNo,
               ms.id as sectionId,
               ms.name as sectionName,
               ms.sort_no as  sectionSortNo
        from math_textbooks t
                 inner join math_chapters mc on t.id = mc.textbook_id and mc.deleted_at is null
                 inner join math_sections ms on mc.id = ms.chapter_id and ms.deleted_at is null
        where t.subject = '数学' and t.publisher = #{publisher.value}
        order by t.id, mc.sort_no, ms.sort_no
    </select>

    <select id="listSections" resultType="com.joinus.knowledge.model.vo.SectionVO">
        select t.id as textbookId,
               t.publisher,
               t.grade,
               t.semester,
               mc.id as chapterId,
               mc.name as chapterName,
               mc.sort_no as chapterSortNo,
               ms.id as sectionId,
               ms.name as sectionName,
               ms.sort_no as  sectionSortNo
        from math_textbooks t
                 inner join math_chapters mc on t.id = mc.textbook_id and mc.deleted_at is null
                 inner join math_sections ms on mc.id = ms.chapter_id and ms.deleted_at is null

        where t.subject = '数学' and
              t.publisher = #{publisher.value}
                and t.grade = #{grade}
                and t.semester = #{semester}
        order by t.id, mc.sort_no, ms.sort_no
    </select>

    <select id="listKnowledgePointCountAndQuestionTypeCount"
            resultType="com.joinus.knowledge.model.po.SectionKeypointCountPO">
        select ms.id as sectionId,
               count(distinct vmkp.knowledge_point_id) as knowledgePointCount,
               count(distinct vmqt.question_type_id) as questionTypeCount
        from  math_sections ms
                  left join view_math_knowledge_points vmkp on ms.id = vmkp.section_id
                  left join view_math_question_types vmqt on ms.id = vmqt.section_id
        where ms.id in
        <foreach item="item" index="index" collection="sectionIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by ms.id
    </select>

    <select id="querySectionVideos" resultType="com.joinus.knowledge.model.po.SectionVideoPO">
        select t.id as section_id,
               msf.file_id  as file_id,
               msf.sort_no,
               f.name as file_name,
               f.oss_bucket,
               f.oss_type,
               f.oss_url as ossKey
        from math_sections t
                 inner join math_section_files msf on t.id = msf.section_id
                 inner join files f on msf.file_id = f.id
        where t.id in
        <foreach item="item" index="index" collection="sectionIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
