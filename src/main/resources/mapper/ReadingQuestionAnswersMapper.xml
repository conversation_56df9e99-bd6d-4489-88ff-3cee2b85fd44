<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.ReadingQuestionAnswersMapper">
    <select id="getFillInTheBlankQuestionAnswers"
            resultType="com.joinus.knowledge.model.entity.ReadingQuestionAnswers">
        select rqa.*
        from reading_question_answers rqa
                 inner join reading_passage_questions rpq on rpq.id = rqa.question_id
        where rpq.question_type = 'FILL_IN_THE_BLANK'
--           and rqa.id ='5a0ddc6d-1233-48db-9723-ec63c26867c5'
    </select>
</mapper>