<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.ReadingPassagesMapper">

    <select id="getByUnitId" resultType="com.joinus.knowledge.model.entity.ReadingPassages">
        WITH filtered_passages AS (SELECT rp.id
                                   FROM reading_passages rp
                                            JOIN reading_passage_questions rpq
                                                 ON rpq.passage_id = rp.id
                                                     AND rpq.is_enabled = 1
                                                     AND rpq.deleted_at is null
                                                     and rpq.is_audit = 1
                                   WHERE rp.unit_id = #{param.unitId}
                                     AND rp.deleted_at IS NULL
                                     AND rp.is_enabled = 1
                                     AND rp.is_audit = 1
                                <if test="param.questionIds != null and param.questionIds.size() > 0">
                                    and rpq.id not in
                                    <foreach collection="param.questionIds" item="questionId" open="(" separator="," close=")">
                                        #{questionId}
                                    </foreach>
                                </if>
                                )
        SELECT rp.*
        FROM reading_passages rp
        WHERE rp.id = (SELECT id
                       FROM filtered_passages
                       ORDER BY RANDOM()
            LIMIT 1)
    </select>
    <select id="getClonePassageAndQuestions" resultType="com.joinus.knowledge.model.po.ClonePassageAndQuestionsPO">
        select distinct on (caaq.base_passage_id) *
        from copy_article_and_questions caaq where base_passage_id is not null and is_processed = false
    </select>
    <update id="updateProcessStatusByPassageId">
        update copy_article_and_questions set is_processed = true where id = #{passageId}
    </update>
</mapper>