<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.ReadingPassageQuestionsMapper">
    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.vo.ReadingPassageQuestionsVO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="question_type" jdbcType="VARCHAR" property="questionType"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="optionStr" jdbcType="VARCHAR" property="optionStr"/>
        <collection property="answers" ofType="com.joinus.knowledge.model.vo.ReadingQuestionAnswersVO">
            <result column="answer_id" jdbcType="VARCHAR" property="id"/>
            <result column="answer" jdbcType="VARCHAR" property="answer"/>
            <result column="answer_content" jdbcType="VARCHAR" property="content"/>
        </collection>
    </resultMap>

    <select id="getQuestions" resultMap="BaseResultMap">
        select t1.id,
        t1.passage_id,
        t1.question_type,
        t1.content ->> '问题' as content,
        t1.content ->> '选项' as optionStr,
        t1.order_no,
        t2.id                 as answer_id,
        t2.content            as answer_content,
        t2.answer ->> '答案'  as answer
        from reading_passage_questions t1
                 left join reading_question_answers t2
                           on t1.id = t2.question_id
        where t1.passage_id = #{param.passageId}
              and t1.is_enabled = 1
              and t1.deleted_at is null
              and t1.is_audit = 1
          <if test="param.questionIds != null and param.questionIds.size() > 0">
              and t1.id not in
              <foreach collection="param.questionIds" item="questionId" open="(" separator="," close=")">
                  #{questionId}
              </foreach>
          </if>
        <if test="param.knowledgePoints != null and param.knowledgePoints.size() > 0">
            and t1.id in (select rqkp.question_id
            from reading_question_knowledge_points rqkp
            where rqkp.knowledge_point_id in
            <foreach collection="param.knowledgePoints" item="knowledgePoint" open="(" separator="," close=")">
                #{knowledgePoint}
            </foreach>
            )
        </if>
        ORDER BY RANDOM()
        limit #{param.count}
    </select>
    <select id="getUnsetQuestions" resultType="com.joinus.knowledge.model.entity.ReadingPassageQuestions">
        select rpq.*
        from reading_passage_questions rpq
                 left join reading_passage_question_set_entries rpqse on rpqse.question_id = rpq.id
        where rpqse.set_id is null
    </select>
    <select id="getAiAuditResult" resultType="java.util.HashMap">
        select * from chinese_question_dimensions where reading_passage_question_id = #{questionId}
    </select>
</mapper>