<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathQuestionRelationshipsMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathQuestionRelationships">
            <id property="id" column="id" />
            <result property="baseQuestionId" column="base_question_id" />
            <result property="derivedQuestionId" column="derived_question_id" />
            <result property="aiModel" column="ai_model" />
            <result property="generationParameters" column="generation_parameters" />
            <result property="createdAt" column="created_at" />
            <result property="hierarchyLevel" column="hierarchy_level" />
    </resultMap>

    <sql id="Base_Column_List">
        id,base_question_id,derived_question_id,ai_model,generation_parameters,created_at,
        hierarchy_level
    </sql>
</mapper>
