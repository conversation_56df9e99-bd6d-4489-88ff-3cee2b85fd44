<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.ReadingUnitGenreKnowledgePointQuestionTypesMapper">
    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.ReadingUnitGenreKnowledgePointQuestionTypes">
        <result column="unit_id" property="unitId" />
        <result column="genre" property="genre" />
        <result column="knowledge_point_id" property="knowledgePointId" />
        <result column="question_type" property="questionType" />
        <result column="weight" property="weight" />
    </resultMap>

    <sql id="Base_Column_List">
        unit_id, genre, knowledge_point_id, question_type, weight
    </sql>
</mapper>