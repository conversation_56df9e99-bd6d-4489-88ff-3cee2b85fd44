<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.QuestionGraphicsScriptMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.QuestionGraphicsScript">
            <result property="questionId" column="question_id" />
            <result property="aiScript" column="ai_script" />
            <result property="fileId" column="file_id" />
            <result property="humanScript" column="human_script" />
            <result property="completedAt" column="completed_at" />
            <result property="username" column="username" />
    </resultMap>

    <resultMap id="candidateQuestion" type="com.joinus.knowledge.model.dto.CandidateQuestion">
        <id property="questionId" column="question_id" javaType="java.util.UUID" jdbcType="OTHER"/>
        <collection property="keyPointIds" ofType="java.util.UUID" javaType="java.util.ArrayList">
            <constructor>
                <idArg column="key_point_id" javaType="java.util.UUID" jdbcType="OTHER"/>
            </constructor>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        question_id,ai_script,file_id,human_script,completed_at,username
    </sql>
    <select id="getInitialKeyPointCounts" resultType="java.util.Map">
        select mkpq.knowledge_point_id as "keyPointId",count(mkpq.knowledge_point_id) as count from math_question_graphics_scripts mqgs
            inner join math_knowledge_point_questions mkpq on mqgs.question_id = mkpq.question_id
            inner join math_questions mq on mqgs.question_id = mq.id
        where mq.enabled = true
          and mqgs.username is not null
        group by mkpq.knowledge_point_id
        union
        select mqtq.question_type_id as "keyPointId",count(mqtq.question_type_id) as count from math_question_graphics_scripts mqgs
            inner join math_question_type_questions mqtq on mqgs.question_id = mqtq.question_id
            inner join math_questions mq on mqgs.question_id = mq.id
        where mq.enabled = true
          and mqgs.username is not null
        group by mqtq.question_type_id
    </select>
    <select id="getAvailableCandidates" resultMap="candidateQuestion">
        select mqgs.question_id, mkpq.knowledge_point_id as key_point_id from math_question_graphics_scripts mqgs
               inner join math_knowledge_point_questions mkpq on mqgs.question_id = mkpq.question_id
               inner join math_questions mq on mqgs.question_id = mq.id
               inner join math_question_dimensions mqd on mq.id = mqd.question_id
        where mqd.validation_success = true
            and mq.deleted_at is null
            and mq.source = 'AI'
            and mqgs.username is null
            and exists (select 1 from math_question_labels mql
                        inner join math_labels ml on mql.label_id = ml.id
                        where mql.question_id = mq.id
                        and ml.type = 'LATEX_SYNTAX'
                        and ml.name = 'LaTeX语法正确')
        <if test="questionIds != null and questionIds.size() > 0">
            and mqgs.question_id not in
            <foreach item="questionId" collection="questionIds" open="(" separator="," close=")">
                #{questionId}
            </foreach>
        </if>
        union
        select mqgs.question_id, mqtq.question_type_id as key_point_id from math_question_graphics_scripts mqgs
                inner join math_question_type_questions mqtq on mqgs.question_id = mqtq.question_id
                inner join math_questions mq on mqgs.question_id = mq.id
                inner join math_question_dimensions mqd on mq.id = mqd.question_id
        where mqd.validation_success = true
            and mq.deleted_at is null
            and mq.source = 'AI'
            and mqgs.username is null
            and exists (select 1 from math_question_labels mql
                        inner join math_labels ml on mql.label_id = ml.id
                        where mql.question_id = mq.id
                        and ml.type = 'LATEX_SYNTAX'
                        and ml.name = 'LaTeX语法正确')
        <if test="questionIds != null and questionIds.size() > 0">
            and mqgs.question_id not in
            <foreach item="questionId" collection="questionIds" open="(" separator="," close=")">
                #{questionId}
            </foreach>
        </if>
    </select>
</mapper>
