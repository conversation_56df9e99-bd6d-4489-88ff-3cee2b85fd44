<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.ReadingQuestionKnowledgePointsMapper">
    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.ReadingQuestionKnowledgePoints">
        <result column="question_id" property="questionId" />
        <result column="knowledge_point_id" property="knowledgePointId" />
    </resultMap>

    <sql id="Base_Column_List">
        question_id, knowledge_point_id
    </sql>
</mapper>