app.id=edu-knowledge-hub
apollo.bootstrap.enabled=true
apollo.bootstrap.eagerLoad.enabled=true
spring.temporal.start-workers=false
ali.oss.region=cn-beijing
#ali.oss.endpoint=https://oss-cn-beijing.aliyuncs.com
ali.oss.endpoint=https://oss-edu-knowledge-hub.qingyulan.net
ali.oss.accessKeyId=LTAI5tH52JJv1MG3sH8eGWE2
ali.oss.accessKeySecret=******************************
ali.oss.bucketName=edu-knowledge-hub
ali.oss.baseDir=uat/
ali.oss.sts.role-arn=acs:ram::1127886446663093:role/edu-knowledge-hub-role

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Redis配置
spring.data.redis.host=************
spring.data.redis.port=6379
spring.data.redis.database=0
spring.data.redis.password=ijx967111
# Redis连接超时时间（毫秒）
spring.data.redis.timeout=3000
# 连接池最大连接数
spring.data.redis.lettuce.pool.max-active=8
# 连接池最大阻塞等待时间（负数表示没有限制）
spring.data.redis.lettuce.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.data.redis.lettuce.pool.max-idle=8
# 连接池中的最小空闲连接
spring.data.redis.lettuce.pool.min-idle=0