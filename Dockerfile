FROM registry.cn-beijing.aliyuncs.com/ijx-public/opentelemetry-javaagent:1.26.0 as opentelemetry
FROM harbor.ijx.icu/ijx-public/openjdk:21-slim-bookworm

ENV TZ=Asia/Shanghai
# 添加Java字体缓存目录环境变量
ENV JAVA_FONTS=/usr/share/fonts/
# 添加QT环境变量解决 QStandardPaths 问题
ENV QT_QPA_PLATFORM=offscreen
ENV XDG_RUNTIME_DIR=/tmp

# 安装多种中文字体、wkhtmltopdf和字体工具以及必要的依赖
RUN apt-get update && apt-get install -y tzdata fonts-noto-cjk fonts-wqy-zenhei fonts-wqy-microhei \
    fontconfig locales wkhtmltopdf xvfb libxrender1 libfontconfig1 \
    && ln -sf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone \
    && dpkg-reconfigure -f noninteractive tzdata \
    # 配置中文locale
    && sed -i -e 's/# zh_CN.UTF-8 UTF-8/zh_CN.UTF-8 UTF-8/' /etc/locale.gen \
    && locale-gen \
    # 更新字体缓存
    && fc-cache -fv \
    # 创建wrapper脚本以确保wkhtmltopdf使用xvfb
    && echo '#!/bin/bash\nxvfb-run -a --server-args="-screen 0, 1024x768x24" /usr/bin/wkhtmltopdf "$@"' > /usr/local/bin/wkhtmltopdf-wrapper \
    && chmod +x /usr/local/bin/wkhtmltopdf-wrapper \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置中文locale
ENV LANG=zh_CN.UTF-8
ENV LANGUAGE=zh_CN:zh
ENV LC_ALL=zh_CN.UTF-8

COPY --from=opentelemetry / /opentelemetry
COPY target/*.jar app.jar

CMD ["java","-Djava.security.egd=file:/dev/./urandom","-Djava.awt.headless=true","-Dfile.encoding=UTF-8","-Dsun.jnu.encoding=UTF-8","-Djava.awt.fonts=${JAVA_FONTS}","-jar" ,"/app.jar"]