# Markdown 转 HTML 和 LaTeX 转 SVG 功能说明

## 功能概述

本功能实现了将 Markdown 文本转换为 HTML，并将其中的 LaTeX 公式转换为 SVG 格式的功能。主要包括：

1. Markdown 转 HTML：使用 commonmark 库将 Markdown 文本转换为 HTML
2. LaTeX 转 SVG：使用 JLaTeXMath 和 Apache Batik 将 LaTeX 公式转换为 SVG 格式
3. 支持两种 SVG 输出模式：
   - 直接输出 SVG 标签
   - 将 SVG 转换为 base64 编码并嵌入到 img 标签中

## 使用方法

### 在 AIAbilityServiceImpl 中使用

修改 `SolveQuestionParam` 参数中的 `extraParams` 字段：

```java
SolveQuestionParam param = new SolveQuestionParam();
// 设置其他参数...

// 创建 extraParams
Map<String, Object> extraParams = new HashMap<>();
extraParams.put("convertToHtml", true);  // 启用 Markdown 转 HTML
extraParams.put("useImgTag", true);      // 使用 img 标签（可选，默认为 true）
param.setExtraParams(extraParams);

// 调用 solveMathQuestionViaStream 方法
Flux<String> result = aiAbilityService.solveMathQuestionViaStream(param);
```

### 在 AIAbilityController 中使用

直接调用 `/ai/ability/math/problem/solve/stream` 接口，并在请求参数中设置 `extraParams`：

```json
{
  "questionId": "d0a0d0a0-d0a0-d0a0-d0a0-d0a0d0a0d0a0",
  "questionText": "求解方程：$x^2 + 2x + 1 = 0$",
  "grade": 1,
  "semester": 1,
  "extraParams": {
    "convertToHtml": true,
    "useImgTag": true
  }
}
```

### 在其他地方直接使用工具类

#### 1. Markdown 转 HTML

```java
import com.joinus.knowledge.utils.MarkdownUtils;

String markdown = "# 标题\n\n这是**粗体**和*斜体*。";
String html = MarkdownUtils.toHtml(markdown);
```

#### 2. LaTeX 转 SVG

```java
import com.joinus.knowledge.utils.LaTeXToSVGConverter;

// 直接输出 SVG
String latex = "E=mc^2";
String svg = LaTeXToSVGConverter.convertToSVG(latex);

// 输出 img 标签
String imgTag = LaTeXToSVGConverter.convertToImgTag(latex);
```

#### 3. Markdown 中的 LaTeX 公式转换

```java
import com.joinus.knowledge.utils.MarkdownLaTeXProcessor;

String markdown = "# 标题\n\n行内公式: $E=mc^2$\n\n块级公式:\n\n$$\\sum_{i=1}^n i = \\frac{n(n+1)}{2}$$";

// 使用 img 标签
String htmlWithImg = MarkdownLaTeXProcessor.processToHtml(markdown, true);

// 直接使用 SVG
String htmlWithSvg = MarkdownLaTeXProcessor.processToHtml(markdown, false);
```

## 缓存机制

为了提高性能，特别是对于复杂的 LaTeX 公式，我们实现了缓存机制：

1. 使用 `ConcurrentHashMap` 作为缓存容器，确保线程安全
2. 分别为 SVG 和 img 标签维护独立的缓存
3. 设置了最大缓存容量 (1000)，防止内存泄漏
4. 提供了缓存管理 API，可以查看缓存状态和清空缓存

### 缓存管理 API

1. 获取缓存状态：`GET /api/v1/latex/cache/status`
2. 清空缓存：`POST /api/v1/latex/cache/clear`

### 缓存策略

- 缓存键由 LaTeX 公式和字体大小组成，确保不同大小的相同公式也能正确缓存
- 当缓存达到最大容量时，新的公式将不再被缓存，直到缓存被清空
- 建议在系统负载较低时定期清空缓存，以释放内存

### 代码示例

```java
// 获取缓存大小
int cacheSize = LaTeXToSVGConverter.getCacheSize();

// 清空缓存
LaTeXToSVGConverter.clearCache();
```

## 演示页面

访问 `/api/v1/demo/markdown-latex/demo` 可以查看演示页面，在页面上可以输入 Markdown 文本，并查看转换后的 HTML 和渲染结果。

## 依赖说明

本功能依赖以下库：

1. commonmark：用于 Markdown 转 HTML
   ```xml
   <dependency>
       <groupId>org.commonmark</groupId>
       <artifactId>commonmark</artifactId>
       <version>0.21.0</version>
   </dependency>
   ```

2. JLaTeXMath：用于 LaTeX 转 SVG
   ```xml
   <dependency>
       <groupId>org.scilab.forge</groupId>
       <artifactId>jlatexmath</artifactId>
       <version>1.0.7</version>
   </dependency>
   ```

3. Apache Batik：用于 SVG 生成
   ```xml
   <dependency>
       <groupId>org.apache.xmlgraphics</groupId>
       <artifactId>batik-svggen</artifactId>
       <version>1.17</version>
   </dependency>
   <dependency>
       <groupId>org.apache.xmlgraphics</groupId>
       <artifactId>batik-dom</artifactId>
       <version>1.17</version>
   </dependency>
   <dependency>
       <groupId>org.apache.xmlgraphics</groupId>
       <artifactId>batik-util</artifactId>
       <version>1.17</version>
   </dependency>
   ```
