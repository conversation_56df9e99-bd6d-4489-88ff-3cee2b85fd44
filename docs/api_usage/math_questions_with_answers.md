# 题目与答案API文档

## 创建API

### API 端点

```
POST /api/math/questions/with-answers
```

### 功能描述

此API允许用户同时添加题目及其对应的答案，并自动建立题目与答案之间的关联关系。

### 请求格式

请求体是一个JSON数组，数组中每个元素包含一个题目对象和一个答案对象：

```json
[
  {
    "question": {
      "content": "题目内容",
      "questionType": "题目类型",
      "difficulty": 难度级别
    },
    "questionAnswer": {
      "answer": "答案简要内容",
      "content": "答案详细解析"
    }
  },
  // 更多题目和答案...
]
```

#### 字段说明

##### question 对象：
- `content` (String): 题目内容
- `questionType` (String): 题目类型，例如"选择题"、"证明题"等
- `difficulty` (Integer): 难度级别，通常为1-5的整数
- `fullQuestionImageId` (UUID, 可选): 题目完整图片的ID
- `embeddedFigureImageId` (UUID, 可选): 题目中嵌入图形/图表的ID

##### questionAnswer 对象：
- `answer` (String): 简要答案内容，例如选择题的"A"、"B"、"C"、"D"或"见解析"等
- `content` (String): 答案的详细解析内容
- `fullAnswerImageId` (UUID, 可选): 答案完整图片的ID
- `embeddedFigureImageId` (UUID, 可选): 答案中嵌入图形/图表的ID

### 请求示例

```json
[
  {
    "question": {
      "content": "如图1.1-1，在△ABC中，点D在边BC上，CD=AB，DE∥AB，∠DCE=∠A. 求证：DE=BC.",
      "questionType": "证明题",
      "difficulty": 1
    },
    "questionAnswer": {
      "answer": "见解析",
      "content": "证明：∵ DE∥AB，∴ ∠EDC=∠B.\n在△CDE和△ABC中，$\\left\\{\\begin{array}{l}\\angle EDC=\\angle B, \\\\ CD=AB, \\\\ \\angle DCE=\\angle A,\\end{array}\\right.$\n∴ △CDE≌△ABC(ASA)，\n∴ DE=BC."
    }
  },
  {
    "question": {
      "content": "(湖州中考)如图1.1-3，AD，CE分别是△ABC的中线和角平分线。若AB=AC，∠CAD=20°，则∠ACE的度数是（）\nA. 20° B. 35° C. 40° D. 70°",
      "questionType": "选择题",
      "difficulty": 1
    },
    "questionAnswer": {
      "answer": "B",
      "content": "解析：∵ AD是△ABC的中线，AB=AC，\n∴ AD⊥BC，∴ ∠ADC=90°。\n又∠CAD=20°，∴ ∠ACB=180°-∠CAD-∠ADC=70°。\n又CE是△ABC的角平分线，\n∴ ∠ACE=$\\frac{1}{2}$∠ACB=35°。"
    }
  }
]
```

### 响应格式

成功响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "question": {
        "id": "550e8400-e29b-41d4-a716-************",
        "content": "如图1.1-1，在△ABC中，点D在边BC上，CD=AB，DE∥AB，∠DCE=∠A. 求证：DE=BC.",
        "questionType": "证明题",
        "difficulty": 1,
        "createdAt": "2025-03-01T10:30:00",
        "updatedAt": "2025-03-01T10:30:00"
      },
      "questionAnswer": {
        "id": "660e8400-e29b-41d4-a716-************",
        "answer": "见解析",
        "content": "证明：∵ DE∥AB，∴ ∠EDC=∠B.\n在△CDE和△ABC中，$\\left\\{\\begin{array}{l}\\angle EDC=\\angle B, \\\\ CD=AB, \\\\ \\angle DCE=\\angle A,\\end{array}\\right.$\n∴ △CDE≌△ABC(ASA)，\n∴ DE=BC.",
        "createdAt": "2025-03-01T10:30:00",
        "updatedAt": "2025-03-01T10:30:00"
      }
    },
    // 更多题目和答案...
  ]
}
```

## 查询API

### 根据ID查询题目及其所有答案

#### API 端点

```
GET /api/math/questions/{id}/with-answers
```

#### 功能描述

此API允许用户根据题目ID查询题目及其所有关联的答案。

#### 请求参数

- `id` (Path Variable): 题目的UUID

#### 响应格式

成功响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "question": {
      "id": "550e8400-e29b-41d4-a716-************",
      "content": "如图1.1-1，在△ABC中，点D在边BC上，CD=AB，DE∥AB，∠DCE=∠A. 求证：DE=BC.",
      "questionType": "证明题",
      "difficulty": 1,
      "createdAt": "2025-03-01T10:30:00",
      "updatedAt": "2025-03-01T10:30:00"
    },
    "questionAnswers": [
      {
        "id": "660e8400-e29b-41d4-a716-************",
        "answer": "见解析",
        "content": "证明：∵ DE∥AB，∴ ∠EDC=∠B.\n在△CDE和△ABC中，$\\left\\{\\begin{array}{l}\\angle EDC=\\angle B, \\\\ CD=AB, \\\\ \\angle DCE=\\angle A,\\end{array}\\right.$\n∴ △CDE≌△ABC(ASA)，\n∴ DE=BC.",
        "createdAt": "2025-03-01T10:30:00",
        "updatedAt": "2025-03-01T10:30:00"
      },
      {
        "id": "770e8400-e29b-41d4-a716-************",
        "answer": "另一种解法",
        "content": "另一种证明方法...",
        "createdAt": "2025-03-01T10:30:00",
        "updatedAt": "2025-03-01T10:30:00"
      }
      // 一个题目可以有多个答案
    ]
  }
}
```

错误响应示例：

```json
{
  "code": 404,
  "message": "题目不存在",
  "data": null
}
```

### 查询多个题目及其所有答案

#### API 端点

```
GET /api/math/questions/with-answers
```

#### 功能描述

此API允许用户根据条件查询多个题目及其所有关联的答案。

#### 请求参数

- `questionType` (Query Parameter, 可选): 题目类型，例如"选择题"、"证明题"等
- `difficulty` (Query Parameter, 可选): 难度级别，通常为1-5的整数
- `content` (Query Parameter, 可选): 题目内容关键词，支持模糊查询

#### 响应格式

成功响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "question": {
        "id": "550e8400-e29b-41d4-a716-************",
        "content": "如图1.1-1，在△ABC中，点D在边BC上，CD=AB，DE∥AB，∠DCE=∠A. 求证：DE=BC.",
        "questionType": "证明题",
        "difficulty": 1,
        "createdAt": "2025-03-01T10:30:00",
        "updatedAt": "2025-03-01T10:30:00"
      },
      "questionAnswers": [
        {
          "id": "660e8400-e29b-41d4-a716-************",
          "answer": "见解析",
          "content": "证明：∵ DE∥AB，∴ ∠EDC=∠B.\n在△CDE和△ABC中，$\\left\\{\\begin{array}{l}\\angle EDC=\\angle B, \\\\ CD=AB, \\\\ \\angle DCE=\\angle A,\\end{array}\\right.$\n∴ △CDE≌△ABC(ASA)，\n∴ DE=BC.",
          "createdAt": "2025-03-01T10:30:00",
          "updatedAt": "2025-03-01T10:30:00"
        }
      ]
    },
    {
      "question": {
        "id": "880e8400-e29b-41d4-a716-************",
        "content": "(湖州中考)如图1.1-3，AD，CE分别是△ABC的中线和角平分线。若AB=AC，∠CAD=20°，则∠ACE的度数是（）\nA. 20° B. 35° C. 40° D. 70°",
        "questionType": "选择题",
        "difficulty": 1,
        "createdAt": "2025-03-01T10:30:00",
        "updatedAt": "2025-03-01T10:30:00"
      },
      "questionAnswers": [
        {
          "id": "990e8400-e29b-41d4-a716-************",
          "answer": "B",
          "content": "解析：∵ AD是△ABC的中线，AB=AC，\n∴ AD⊥BC，∴ ∠ADC=90°。\n又∠CAD=20°，∴ ∠ACB=180°-∠CAD-∠ADC=70°。\n又CE是△ABC的角平分线，\n∴ ∠ACE=$\\frac{1}{2}$∠ACB=35°。",
          "createdAt": "2025-03-01T10:30:00",
          "updatedAt": "2025-03-01T10:30:00"
        },
        {
          "id": "110e8400-e29b-41d4-a716-************",
          "answer": "B",
          "content": "另一种解法：...",
          "createdAt": "2025-03-01T10:30:00",
          "updatedAt": "2025-03-01T10:30:00"
        }
      ]
    }
  ]
}
```

### 根据多个题目ID批量查询题目及其所有答案

#### API 端点

```
POST /api/math/questions/batch/with-answers
```

#### 功能描述

此API允许用户根据多个题目ID批量查询题目及其所有关联的答案。返回结果保持与请求中题目ID的顺序一致。

#### 请求参数

请求体是一个JSON数组，包含多个题目ID：

```json
[
  "550e8400-e29b-41d4-a716-************",
  "880e8400-e29b-41d4-a716-************",
  "990e8400-e29b-41d4-a716-446655440001"
]
```

#### 响应格式

成功响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "question": {
        "id": "550e8400-e29b-41d4-a716-************",
        "content": "如图1.1-1，在△ABC中，点D在边BC上，CD=AB，DE∥AB，∠DCE=∠A. 求证：DE=BC.",
        "questionType": "证明题",
        "difficulty": 1,
        "createdAt": "2025-03-01T10:30:00",
        "updatedAt": "2025-03-01T10:30:00"
      },
      "questionAnswers": [
        {
          "id": "660e8400-e29b-41d4-a716-************",
          "answer": "见解析",
          "content": "证明：∵ DE∥AB，∴ ∠EDC=∠B.\n在△CDE和△ABC中，$\\left\\{\\begin{array}{l}\\angle EDC=\\angle B, \\\\ CD=AB, \\\\ \\angle DCE=\\angle A,\\end{array}\\right.$\n∴ △CDE≌△ABC(ASA)，\n∴ DE=BC.",
          "createdAt": "2025-03-01T10:30:00",
          "updatedAt": "2025-03-01T10:30:00"
        }
      ]
    },
    {
      "question": {
        "id": "880e8400-e29b-41d4-a716-************",
        "content": "(湖州中考)如图1.1-3，AD，CE分别是△ABC的中线和角平分线。若AB=AC，∠CAD=20°，则∠ACE的度数是（）\nA. 20° B. 35° C. 40° D. 70°",
        "questionType": "选择题",
        "difficulty": 1,
        "createdAt": "2025-03-01T10:30:00",
        "updatedAt": "2025-03-01T10:30:00"
      },
      "questionAnswers": [
        {
          "id": "990e8400-e29b-41d4-a716-************",
          "answer": "B",
          "content": "解析：∵ AD是△ABC的中线，AB=AC，\n∴ AD⊥BC，∴ ∠ADC=90°。\n又∠CAD=20°，∴ ∠ACB=180°-∠CAD-∠ADC=70°。\n又CE是△ABC的角平分线，\n∴ ∠ACE=$\\frac{1}{2}$∠ACB=35°。",
          "createdAt": "2025-03-01T10:30:00",
          "updatedAt": "2025-03-01T10:30:00"
        }
      ]
    }
  ]
}
```

错误响应示例：

```json
{
  "code": 400,
  "message": "题目ID列表不能为空",
  "data": null
}
```

## 删除API

### API 端点

```
DELETE /api/math/questions/{id}/cascade
```

### 功能描述

此API允许用户根据题目ID级联删除题目及其关联的所有答案。删除操作会：
1. 删除指定ID的题目
2. 查找并删除与该题目相关联的所有答案
3. 删除题目与答案之间的关联关系

### 请求参数

- `id` (Path Variable): 题目的UUID

### 响应格式

成功响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

错误响应示例：

```json
{
  "code": 404,
  "message": "题目不存在",
  "data": null
}
```

或

```json
{
  "code": 500,
  "message": "删除题目失败",
  "data": null
}
```

## 注意事项

1. 接口使用了事务控制，保证数据一致性
2. 级联删除操作不可逆，请谨慎使用
3. 系统会自动处理题目与答案之间的关联关系
4. 查询功能支持一个题目对应多个答案的场景，返回结果中的 `questionAnswers` 是一个数组
5. 查询接口支持按题目类型、难度和内容关键词进行筛选
6. 批量查询接口会保持返回结果与请求中题目ID的顺序一致
